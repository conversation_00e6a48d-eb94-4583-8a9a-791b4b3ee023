{"version": 3, "sources": ["src_pages_personal-center_index_tsx-async.13146676289523221991.hot-update.js", "src/pages/personal-center/PersonalInfo.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/personal-center/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='4476642497419208368';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/settings/index.tsx\":[\"p__settings__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import {\n  ClockCircleOutlined,\n  SettingOutlined,\n  TeamOutlined,\n} from '@ant-design/icons';\nimport {\n  Alert,\n  Button,\n  Space,\n  Spin,\n  Typography,\n} from 'antd';\nimport { ProCard } from '@ant-design/pro-components';\n\nimport React, { useEffect, useState } from 'react';\nimport { UserService } from '@/services/user';\nimport type { UserProfileDetailResponse } from '@/types/api';\nimport UnifiedSettingsModal from './UnifiedSettingsModal';\nimport UserInfoPopover from './UserInfoPopover';\nimport styles from './PersonalInfo.module.css';\n\nconst { Title, Text } = Typography;\n\n/**\n * 个人信息组件\n *\n * 显示用户的基本个人信息，采用简洁的卡片设计。\n * 用户名支持悬浮显示详细信息（电话、邮箱、注册时间等）。\n *\n * 主要功能：\n * 1. 显示用户头像（使用姓名首字母）\n * 2. 显示用户姓名（支持气泡卡片显示详细信息）\n * 3. 显示最后登录时间和登录团队\n * 4. 提供设置入口\n *\n * 数据来源：\n * - 用户详细信息：通过UserService.getUserProfileDetail()获取\n */\nconst PersonalInfo: React.FC = () => {\n  /**\n   * 用户详细信息状态管理\n   */\n  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({\n    name: '',\n    position: '',\n    email: '',\n    telephone: '',\n    registerDate: '',\n    lastLoginTime: '',\n    lastLoginTeam: '',\n    teamCount: 0,\n    avatar: '',\n  });\n\n  const [userInfoLoading, setUserInfoLoading] = useState(true);\n  const [userInfoError, setUserInfoError] = useState<string | null>(null);\n\n  // Modal状态管理\n  const [settingsModalVisible, setSettingsModalVisible] = useState(false);\n\n  // 获取用户数据\n  useEffect(() => {\n    const fetchUserData = async () => {\n      try {\n        const userDetail = await UserService.getUserProfileDetail();\n        setUserInfo(userDetail);\n        setUserInfoError(null);\n      } catch (error) {\n        console.error('获取用户详细信息失败:', error);\n        setUserInfoError('获取用户详细信息失败，请稍后重试');\n      } finally {\n        setUserInfoLoading(false);\n      }\n    };\n\n    fetchUserData();\n  }, []);\n\n  return (\n    <ProCard\n      title=\"个人信息\"\n      extra={\n        <Button\n          type=\"text\"\n          icon={<SettingOutlined />}\n          onClick={() => setSettingsModalVisible(true)}\n        />\n      }\n      style={{\n        marginBottom: 16,\n        borderRadius: 8,\n        border: '1px solid #d9d9d9',\n      }}\n    >\n      {userInfoError ? (\n        <Alert\n          message=\"个人信息加载失败\"\n          description={userInfoError}\n          type=\"error\"\n          showIcon\n          style={{\n            borderRadius: 12,\n            border: 'none',\n          }}\n        />\n      ) : (\n        <Spin spinning={userInfoLoading}>\n          {/* 主要内容区域 */}\n          <div className={styles.personalInfoContent}>\n            {/* 基本信息 */}\n            <div className={styles.userBasicInfo}>\n              <UserInfoPopover userInfo={userInfo}>\n                <Typography.Title\n                  level={3}\n                  className={styles.userName}\n                >\n                  {userInfo.name || '加载中...'}\n                </Typography.Title>\n              </UserInfoPopover>\n\n              {/* 职位信息（如果有） */}\n              {userInfo.position && (\n                <Text\n                  type=\"secondary\"\n                  style={{\n                    fontSize: 14,\n                    marginBottom: 12,\n                    display: 'block',\n                  }}\n                >\n                  {userInfo.position}\n                </Text>\n              )}\n            </div>\n\n            {/* 登录信息区域 */}\n            <div className={styles.loginInfoSection}>\n              <Space direction=\"vertical\" size={8} style={{ width: '100%' }}>\n                {userInfo.lastLoginTime && (\n                  <div className={styles.loginInfoItem}>\n                    <ClockCircleOutlined\n                      style={{\n                        fontSize: 14,\n                        color: '#1890ff',\n                      }}\n                    />\n                    <Typography.Text\n                      style={{\n                        fontSize: 13,\n                        color: '#8c8c8c',\n                        fontWeight: 500,\n                      }}\n                    >\n                      最后登录：{userInfo.lastLoginTime}\n                    </Typography.Text>\n                  </div>\n                )}\n                {userInfo.lastLoginTeam && (\n                  <div className={styles.loginInfoItem}>\n                    <TeamOutlined\n                      style={{\n                        fontSize: 14,\n                        color: '#52c41a',\n                      }}\n                    />\n                    <Typography.Text\n                      style={{\n                        fontSize: 13,\n                        color: '#8c8c8c',\n                        fontWeight: 500,\n                      }}\n                    >\n                      团队：{userInfo.lastLoginTeam}\n                    </Typography.Text>\n                  </div>\n                )}\n              </Space>\n            </div>\n          </div>\n        </Spin>\n      )}\n\n      {/* 统一设置Modal */}\n      <UnifiedSettingsModal\n        visible={settingsModalVisible}\n        onCancel={() => setSettingsModalVisible(false)}\n        userInfo={userInfo}\n        onSuccess={() => {\n          // 可以在这里刷新用户信息\n          console.log('设置操作成功');\n        }}\n      />\n    </ProCard>\n  );\n};\n\nexport default PersonalInfo;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCCiMb;;;2BAAA;;;;;;;0CAhMO;yCAOA;kDACiB;oFAEmB;yCACf;kGAEK;6FACL;2GACT;;;;;;;;;;YAEnB,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;YAElC;;;;;;;;;;;;;;CAcC,GACD,MAAM,eAAyB;;gBAC7B;;GAEC,GACD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAA4B;oBAClE,MAAM;oBACN,UAAU;oBACV,OAAO;oBACP,WAAW;oBACX,cAAc;oBACd,eAAe;oBACf,eAAe;oBACf,WAAW;oBACX,QAAQ;gBACV;gBAEA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAC;gBACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;gBAElE,YAAY;gBACZ,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,IAAA,eAAQ,EAAC;gBAEjE,SAAS;gBACT,IAAA,gBAAS,EAAC;oBACR,MAAM,gBAAgB;wBACpB,IAAI;4BACF,MAAM,aAAa,MAAM,iBAAW,CAAC,oBAAoB;4BACzD,YAAY;4BACZ,iBAAiB;wBACnB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,eAAe;4BAC7B,iBAAiB;wBACnB,SAAU;4BACR,mBAAmB;wBACrB;oBACF;oBAEA;gBACF,GAAG,EAAE;gBAEL,qBACE,2BAAC,sBAAO;oBACN,OAAM;oBACN,qBACE,2BAAC,YAAM;wBACL,MAAK;wBACL,oBAAM,2BAAC,sBAAe;;;;;wBACtB,SAAS,IAAM,wBAAwB;;;;;;oBAG3C,OAAO;wBACL,cAAc;wBACd,cAAc;wBACd,QAAQ;oBACV;;wBAEC,8BACC,2BAAC,WAAK;4BACJ,SAAQ;4BACR,aAAa;4BACb,MAAK;4BACL,QAAQ;4BACR,OAAO;gCACL,cAAc;gCACd,QAAQ;4BACV;;;;;iDAGF,2BAAC,UAAI;4BAAC,UAAU;sCAEd,cAAA,2BAAC;gCAAI,WAAW,sCAAM,CAAC,mBAAmB;;kDAExC,2BAAC;wCAAI,WAAW,sCAAM,CAAC,aAAa;;0DAClC,2BAAC,wBAAe;gDAAC,UAAU;0DACzB,cAAA,2BAAC,gBAAU,CAAC,KAAK;oDACf,OAAO;oDACP,WAAW,sCAAM,CAAC,QAAQ;8DAEzB,SAAS,IAAI,IAAI;;;;;;;;;;;4CAKrB,SAAS,QAAQ,kBAChB,2BAAC;gDACC,MAAK;gDACL,OAAO;oDACL,UAAU;oDACV,cAAc;oDACd,SAAS;gDACX;0DAEC,SAAS,QAAQ;;;;;;;;;;;;kDAMxB,2BAAC;wCAAI,WAAW,sCAAM,CAAC,gBAAgB;kDACrC,cAAA,2BAAC,WAAK;4CAAC,WAAU;4CAAW,MAAM;4CAAG,OAAO;gDAAE,OAAO;4CAAO;;gDACzD,SAAS,aAAa,kBACrB,2BAAC;oDAAI,WAAW,sCAAM,CAAC,aAAa;;sEAClC,2BAAC,0BAAmB;4DAClB,OAAO;gEACL,UAAU;gEACV,OAAO;4DACT;;;;;;sEAEF,2BAAC,gBAAU,CAAC,IAAI;4DACd,OAAO;gEACL,UAAU;gEACV,OAAO;gEACP,YAAY;4DACd;;gEACD;gEACO,SAAS,aAAa;;;;;;;;;;;;;gDAIjC,SAAS,aAAa,kBACrB,2BAAC;oDAAI,WAAW,sCAAM,CAAC,aAAa;;sEAClC,2BAAC,mBAAY;4DACX,OAAO;gEACL,UAAU;gEACV,OAAO;4DACT;;;;;;sEAEF,2BAAC,gBAAU,CAAC,IAAI;4DACd,OAAO;gEACL,UAAU;gEACV,OAAO;gEACP,YAAY;4DACd;;gEACD;gEACK,SAAS,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAW1C,2BAAC,6BAAoB;4BACnB,SAAS;4BACT,UAAU,IAAM,wBAAwB;4BACxC,UAAU;4BACV,WAAW;gCACT,cAAc;gCACd,QAAQ,GAAG,CAAC;4BACd;;;;;;;;;;;;YAIR;eA5JM;iBAAA;gBA8JN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDjMD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,gCAA+B;YAAC;SAAqB;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AACrjB"}