package com.teammanage.util;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import com.teammanage.security.UserPrincipal;

/**
 * 安全工具类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class SecurityUtil {

    /**
     * 获取当前认证信息
     * 
     * @return 认证信息
     */
    public static Authentication getCurrentAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    /**
     * 获取当前用户主体信息
     * 
     * @return 用户主体信息
     */
    public static UserPrincipal getCurrentUserPrincipal() {
        Authentication authentication = getCurrentAuthentication();
        if (authentication != null && authentication.getDetails() instanceof UserPrincipal) {
            return (UserPrincipal) authentication.getDetails();
        }
        return null;
    }

    /**
     * 获取当前用户ID
     * 
     * @return 用户ID
     */
    public static Long getCurrentUserId() {
        UserPrincipal userPrincipal = getCurrentUserPrincipal();
        return userPrincipal != null ? userPrincipal.getUserId() : null;
    }

    /**
     * 获取当前用户邮箱
     * 
     * @return 用户邮箱
     */
    public static String getCurrentUserEmail() {
        UserPrincipal userPrincipal = getCurrentUserPrincipal();
        return userPrincipal != null ? userPrincipal.getEmail() : null;
    }



}
