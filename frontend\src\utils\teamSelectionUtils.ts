/**
 * 团队选择状态管理工具函数
 * 用于跟踪用户是否已经主动选择过团队，以区分初始登录状态和主动选择状态
 */

// 团队选择历史的本地存储键
const TEAM_SELECTION_KEY = 'user_team_selection_history';

/**
 * 获取用户的团队选择历史
 * @param userId 用户ID
 * @returns 用户选择过的团队ID集合
 */
export const getUserTeamSelectionHistory = (userId: number): Set<number> => {
  try {
    const history = localStorage.getItem(`${TEAM_SELECTION_KEY}_${userId}`);
    if (history) {
      return new Set(JSON.parse(history));
    }
  } catch (error) {
    console.error('获取团队选择历史失败:', error);
  }
  return new Set();
};

/**
 * 记录用户选择了某个团队
 * @param userId 用户ID
 * @param teamId 团队ID
 */
export const recordTeamSelection = (userId: number, teamId: number): void => {
  try {
    const history = getUserTeamSelectionHistory(userId);
    history.add(teamId);
    localStorage.setItem(`${TEAM_SELECTION_KEY}_${userId}`, JSON.stringify([...history]));
    console.log(`记录团队选择: 用户${userId}选择了团队${teamId}`);
  } catch (error) {
    console.error('记录团队选择历史失败:', error);
  }
};

/**
 * 检查用户是否曾经选择过某个团队
 * @param userId 用户ID
 * @param teamId 团队ID
 * @returns 是否曾经选择过该团队
 */
export const hasUserSelectedTeam = (userId: number, teamId: number): boolean => {
  const history = getUserTeamSelectionHistory(userId);
  return history.has(teamId);
};

/**
 * 清除用户的团队选择历史（用于注销等场景）
 * @param userId 用户ID
 */
export const clearUserTeamSelectionHistory = (userId: number): void => {
  try {
    localStorage.removeItem(`${TEAM_SELECTION_KEY}_${userId}`);
    console.log(`清除用户${userId}的团队选择历史`);
  } catch (error) {
    console.error('清除团队选择历史失败:', error);
  }
};

/**
 * 获取所有用户的团队选择历史键（用于调试）
 * @returns 所有相关的localStorage键
 */
export const getAllTeamSelectionKeys = (): string[] => {
  const keys: string[] = [];
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && key.startsWith(TEAM_SELECTION_KEY)) {
      keys.push(key);
    }
  }
  return keys;
};
