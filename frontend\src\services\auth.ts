/**
 * 认证相关 API 服务
 */

import type {
  LoginRequest,
  LoginResponse,
  SendVerificationCodeRequest,
  SendVerificationCodeResponse
} from '@/types/api';
import { apiRequest, TokenManager } from '@/utils/request';

/**
 * 认证服务类（验证码登录系统）
 *
 * 这是前端身份验证系统的核心服务类，提供完整的用户身份验证功能。
 *
 * 主要功能模块：
 * 1. 验证码管理：发送和验证邮箱验证码
 * 2. 用户认证：登录、注册、Token验证
 * 3. Token管理：Token存储、刷新、清除
 * 4. 团队上下文：团队选择、切换、清除
 * 5. 会话管理：登录状态检查、自动登出
 *
 * 设计特点：
 * - 采用单Token系统：一个Token同时用于用户认证和团队访问
 * - 验证码登录：提高安全性，支持自动注册
 * - 自动Token管理：透明处理Token的存储、更新和清除
 * - 统一错误处理：所有API调用都通过统一的错误处理机制
 * - 状态同步：与全局状态管理系统集成
 *
 * Token生命周期：
 * 1. 登录时生成用户Token（不包含团队信息）
 * 2. 选择团队时更新为团队Token（包含团队上下文）
 * 3. 切换团队时重新生成新的团队Token
 * 4. 刷新Token时保持当前上下文
 * 5. 登出时清除所有Token和会话信息
 *
 * 使用场景：
 * - 用户登录/注册流程
 * - 团队选择和切换
 * - API请求的身份验证
 * - 用户会话状态管理
 * - 自动登出处理
 *
 * <AUTHOR>
 * @since 1.0.0
 */
export class AuthService {
  /**
   * 发送验证码
   *
   * 向指定邮箱发送6位数字验证码，用于登录验证。
   * 如果邮箱未注册，系统会在登录时自动创建新用户。
   *
   * 功能特点：
   * - 发送6位随机数字验证码到指定邮箱
   * - 验证码有效期为5分钟
   * - 同一邮箱60秒内只能发送一次验证码（防刷机制）
   * - 支持自动注册：邮箱未注册时不会报错，登录时自动创建账户
   * - 邮箱格式验证：确保邮箱地址格式正确
   *
   * 错误处理：
   * - 邮箱格式错误：返回400错误
   * - 发送频率过高：返回429错误，提示等待时间
   * - 邮件服务异常：返回500错误，建议稍后重试
   *
   * @param data 验证码发送请求参数
   * @param data.email 接收验证码的邮箱地址，必须是有效的邮箱格式
   * @returns Promise<SendVerificationCodeResponse> 发送结果，包含成功状态和相关信息
   * @throws 当邮箱格式错误或发送频率过高时抛出异常
   *
   * @example
   * ```typescript
   * try {
   *   const response = await AuthService.sendVerificationCode({
   *     email: '<EMAIL>'
   *   });
   *
   *   if (response.success) {
   *     console.log('验证码发送成功，请检查邮箱');
   *     // 启动倒计时，禁用发送按钮60秒
   *   }
   * } catch (error) {
   *   console.error('验证码发送失败:', error.message);
   *   // 显示错误信息给用户
   * }
   * ```
   */
  static async sendVerificationCode(data: SendVerificationCodeRequest): Promise<SendVerificationCodeResponse> {
    // 调用后端API发送验证码
    // 请求会通过统一的请求拦截器处理，包括错误处理和重试机制
    const response = await apiRequest.post<SendVerificationCodeResponse>('/auth/send-code', data);

    // 直接返回响应数据，错误处理由响应拦截器统一处理
    // 成功时返回 { success: true, message: "验证码发送成功" }
    return response.data;
  }

  // 注册功能已移除，统一使用验证码登录/注册流程

  /**
   * 用户登录（验证码登录）
   *
   * 使用邮箱和验证码进行登录。如果邮箱未注册，系统会自动创建新用户。
   * 登录成功后返回用户信息和可选择的团队列表。
   *
   * 登录流程：
   * 1. 验证邮箱和验证码的有效性
   * 2. 检查用户是否存在，不存在则自动创建新用户
   * 3. 生成用户级别的JWT Token（不包含团队信息）
   * 4. 创建用户会话记录
   * 5. 查询用户所属的团队列表
   * 6. 返回用户信息、Token和团队列表
   *
   * Token特点：
   * - 初始Token只包含用户信息，不包含团队上下文
   * - Token有效期为配置的过期时间（通常为24小时）
   * - 包含用户ID、邮箱、姓名等基本信息
   * - 包含唯一的JTI（JWT ID）用于会话管理
   *
   * 自动注册机制：
   * - 如果邮箱未注册，系统会自动创建新用户
   * - 新用户的姓名默认为邮箱前缀
   * - 新用户会自动激活，无需额外验证
   *
   * 错误处理：
   * - 验证码错误或过期：返回401错误
   * - 邮箱格式错误：返回400错误
   * - 系统异常：返回500错误
   *
   * @param data 登录请求参数
   * @param data.email 用户邮箱地址，必须是有效的邮箱格式
   * @param data.code 6位数字验证码，通过邮箱接收
   * @returns Promise<LoginResponse> 登录响应，包含用户信息、Token和团队列表
   * @throws 当验证码错误、已过期或邮箱格式错误时抛出异常
   *
   * @example
   * ```typescript
   * try {
   *   const loginResponse = await AuthService.login({
   *     email: '<EMAIL>',
   *     code: '123456'
   *   });
   *
   *   console.log('登录成功，用户信息:', loginResponse.user);
   *   console.log('可选团队数量:', loginResponse.teams.length);
   *
   *   // 根据团队数量决定后续流程
   *   if (loginResponse.teams.length === 0) {
   *     // 用户没有团队，跳转到个人中心
   *     history.push('/personal-center');
   *   } else if (loginResponse.teams.length === 1) {
   *     // 用户只有一个团队，自动选择
   *     await AuthService.selectTeam({ teamId: loginResponse.teams[0].id });
   *     history.push('/dashboard');
   *   } else {
   *     // 用户有多个团队，让用户选择
   *     history.push('/personal-center', { teams: loginResponse.teams });
   *   }
   * } catch (error) {
   *   console.error('登录失败:', error.message);
   *   // 显示错误信息给用户
   * }
   * ```
   */
  static async login(data: LoginRequest): Promise<LoginResponse> {
    // 调用后端登录API
    const response = await apiRequest.post<LoginResponse>('/auth/login', data);

    // 自动保存用户Token到本地存储
    // 这是用户级别的Token，不包含团队信息
    if (response.data.token) {
      TokenManager.setToken(response.data.token);
    }

    // 返回完整的登录响应，包含用户信息和团队列表
    return response.data;
  }

  /**
   * 刷新Token
   *
   * 使用当前有效的Token获取新的Token，延长会话时间。
   * 通常在Token即将过期时自动调用，也可以手动调用来延长会话。
   *
   * 刷新机制：
   * 1. 验证当前Token的有效性
   * 2. 检查会话是否仍然活跃
   * 3. 生成新的Token，保持相同的用户和团队上下文
   * 4. 更新会话的活跃时间
   * 5. 使旧Token失效，启用新Token
   *
   * Token上下文保持：
   * - 如果当前Token包含团队信息，新Token也会包含相同的团队信息
   * - 如果当前Token只是用户Token，新Token也只包含用户信息
   * - 用户的权限和角色信息保持不变
   *
   * 安全特性：
   * - 每次刷新都会生成新的JTI（JWT ID）
   * - 旧Token立即失效，防止重放攻击
   * - 会话活跃时间更新，用于会话管理
   *
   * 自动刷新场景：
   * - Token即将过期时（通常在过期前5分钟）
   * - 长时间无操作后的首次API调用
   * - 用户主动操作时检测到Token即将过期
   *
   * @returns Promise<LoginResponse> 包含新Token和用户信息的响应
   * @throws 当当前Token无效、已过期或会话不存在时抛出异常
   *
   * @example
   * ```typescript
   * try {
   *   const refreshResponse = await AuthService.refreshToken();
   *   console.log('Token已刷新，新的过期时间:', refreshResponse.token);
   *
   *   // Token会自动保存到本地存储
   *   // 后续API请求会使用新Token
   * } catch (error) {
   *   console.log('Token刷新失败，需要重新登录:', error.message);
   *
   *   // 清除本地Token并跳转到登录页面
   *   AuthService.clearToken();
   *   history.push('/user/login');
   * }
   * ```
   */
  static async refreshToken(): Promise<LoginResponse> {
    // 调用后端Token刷新API
    // 请求会自动携带当前Token（通过请求拦截器添加）
    const response = await apiRequest.post<LoginResponse>(
      '/auth/refresh-token',
    );

    // 自动更新本地存储的Token
    // 新Token会替换旧Token，保持会话连续性
    if (response.data.token) {
      TokenManager.setToken(response.data.token);
    }

    // 返回完整响应，包含新Token和用户信息
    return response.data;
  }

  /**
   * 用户登出
   *
   * 执行完整的用户登出流程，包括清除服务器端会话和本地存储。
   * 即使服务器请求失败，也会清除本地Token确保用户能够重新登录。
   *
   * 登出流程：
   * 1. 向服务器发送登出请求，使当前会话失效
   * 2. 服务器端清除会话记录和Token黑名单
   * 3. 清除本地存储的Token和相关数据
   * 4. 清除全局状态中的用户和团队信息
   * 5. 触发路由守卫，自动跳转到登录页面
   *
   * 安全特性：
   * - 服务器端会话立即失效，防止Token被恶意使用
   * - 本地存储完全清除，不留任何敏感信息
   * - 即使网络异常也能确保本地清理完成
   *
   * 错误处理：
   * - 网络错误：仍然清除本地Token，确保用户能重新登录
   * - 服务器错误：记录日志但不影响本地清理
   * - Token已过期：正常处理，清除本地存储
   *
   * @returns Promise<void> 登出完成时resolve，不会抛出异常
   *
   * @example
   * ```typescript
   * try {
   *   await AuthService.logout();
   *   console.log('登出成功');
   *   // 页面会自动跳转到登录页面
   * } catch (error) {
   *   // 这里通常不会有异常，因为方法内部已处理所有错误
   *   console.log('登出过程中出现异常，但本地清理已完成');
   * }
   * ```
   */
  static async logout(): Promise<void> {
    try {
      // 向服务器发送登出请求，使会话失效
      // 这会清除服务器端的会话记录和Token黑名单
      await apiRequest.post<void>('/auth/logout');
    } catch (error) {
      // 即使服务器请求失败，也要继续清除本地数据
      // 这确保用户在网络异常时也能正常登出
      console.warn('服务器登出请求失败，但继续清除本地数据:', error);
    } finally {
      // 无论服务器请求是否成功，都清除本地Token
      // 这是登出流程的关键步骤，确保本地不留任何敏感信息
      TokenManager.clearToken();

      // 注意：全局状态的清理由路由守卫或页面组件处理
      // 这里只负责Token的清理
    }
  }

  /**
   * 验证Token有效性
   *
   * 向服务器验证当前Token是否仍然有效。
   * 用于检查用户会话状态，通常在应用启动时调用。
   *
   * @returns Promise<boolean> Token是否有效
   *
   * @example
   * ```typescript
   * const isValid = await AuthService.validateToken();
   * if (!isValid) {
   *   // Token无效，跳转到登录页面
   *   history.push('/user/login');
   * }
   * ```
   */
  static async validateToken(): Promise<boolean> {
    try {
      const response = await apiRequest.get<boolean>('/auth/validate');
      return response.data;
    } catch {
      return false;
    }
  }

  /**
   * 检查是否已登录
   *
   * 检查本地是否存储了有效的Token。
   * 注意：这只检查本地存储，不验证Token的服务器端有效性。
   *
   * @returns boolean 是否已登录（本地Token存在）
   *
   * @example
   * ```typescript
   * if (AuthService.isLoggedIn()) {
   *   console.log('用户已登录');
   * } else {
   *   console.log('用户未登录');
   * }
   * ```
   */
  static isLoggedIn(): boolean {
    return TokenManager.hasToken();
  }

  /**
   * 获取当前Token
   *
   * 从本地存储获取当前用户的Token。
   *
   * @returns string | null 当前Token，如果未登录则返回null
   *
   * @example
   * ```typescript
   * const token = AuthService.getToken();
   * if (token) {
   *   console.log('当前Token:', token);
   * }
   * ```
   */
  static getToken(): string | null {
    return TokenManager.getToken();
  }

  /**
   * 清除Token
   *
   * 清除本地存储的Token，但不通知服务器。
   * 通常用于强制登出或Token过期处理。
   *
   * @example
   * ```typescript
   * AuthService.clearToken();
   * console.log('Token已清除');
   * ```
   */
  static clearToken(): void {
    TokenManager.clearToken();
  }

  /**
   * 清除团队Token（兼容性方法）
   *
   * 在单Token系统中，清除Token即可。
   *
   * @deprecated 使用 clearToken() 替代
   */
  static clearTeamToken(): void {
    // 在单令牌系统中，清除Token即可
    TokenManager.clearToken();
  }

  /**
   * 选择团队
   *
   * 在用户登录后选择要操作的团队。
   * 选择团队后，Token会更新为包含团队上下文的新Token。
   *
   * @param data 团队选择请求参数
   * @param data.teamId 要选择的团队ID
   * @returns Promise<LoginResponse> 包含新Token和团队信息的响应
   * @throws 当团队不存在或用户无权限访问时抛出异常
   *
   * @example
   * ```typescript
   * const teamResponse = await AuthService.selectTeam({
   *   teamId: 123
   * });
   *
   * console.log('当前团队:', teamResponse.currentTeam);
   * console.log('团队Token已更新');
   * ```
   */
  static async selectTeam(data: { teamId: number }): Promise<LoginResponse> {
    const response = await apiRequest.post<LoginResponse>(
      '/auth/select-team',
      data,
    );

    // 更新Token
    if (response.data.token) {
      TokenManager.setToken(response.data.token);
    }

    return response.data;
  }

  /**
   * 切换团队
   *
   * 在已选择团队的情况下切换到另一个团队。
   * 与selectTeam功能相同，但语义上表示从一个团队切换到另一个团队。
   *
   * @param data 团队切换请求参数
   * @param data.teamId 要切换到的团队ID
   * @returns Promise<LoginResponse> 包含新Token和团队信息的响应
   * @throws 当团队不存在或用户无权限访问时抛出异常
   *
   * @example
   * ```typescript
   * const switchResponse = await AuthService.switchTeam({
   *   teamId: 456
   * });
   *
   * console.log('已切换到团队:', switchResponse.currentTeam?.name);
   * ```
   */
  static async switchTeam(data: { teamId: number }): Promise<LoginResponse> {
    const response = await apiRequest.post<LoginResponse>(
      '/auth/switch-team',
      data,
    );

    // 更新Token
    if (response.data.token) {
      TokenManager.setToken(response.data.token);
    }

    return response.data;
  }

  /**
   * 清除团队上下文
   *
   * 清除当前选择的团队，返回到用户级别的Token。
   * 用户可以重新选择团队或进行用户级别的操作。
   *
   * @returns Promise<string> 新的用户级别Token
   * @throws 当用户未登录时抛出异常
   *
   * @example
   * ```typescript
   * const userToken = await AuthService.clearTeam();
   * console.log('已清除团队上下文，返回用户级别');
   * ```
   */
  static async clearTeam(): Promise<string> {
    const response = await apiRequest.post<string>('/auth/clear-team');

    // 更新Token
    if (response.data) {
      TokenManager.setToken(response.data);
    }

    return response.data;
  }

  // ========== 兼容性方法 ==========

  /**
   * 检查是否已选择团队（兼容性方法）
   * @deprecated 在单令牌系统中，团队信息包含在Token中，使用 isLoggedIn 检查登录状态
   */
  static hasTeamSelected(): boolean {
    return AuthService.isLoggedIn();
  }
  /**
   * 团队登录（兼容性方法）
   * @deprecated 使用 selectTeam 替代
   */
  static async teamLogin(data: { teamId: number }): Promise<LoginResponse> {
    return AuthService.selectTeam(data);
  }

  /**
   * 清除所有Token（兼容性方法）
   * @deprecated 使用 clearToken 替代
   */
  static clearTokens(): void {
    AuthService.clearToken();
  }
}

// 导出默认实例
export default AuthService;
