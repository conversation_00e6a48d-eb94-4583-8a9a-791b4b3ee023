package com.teammanage.enums;

/**
 * 团队角色枚举
 * 
 * 定义团队中用户的角色类型，支持权限控制和功能访问管理
 * 
 * 角色层级（从高到低）：
 * - TEAM_CREATOR: 团队创建者，拥有所有权限
 * - TEAM_MEMBER: 团队成员，拥有基本访问权限
 * 
 * 扩展性设计：
 * - 可以轻松添加新角色（如 TEAM_ADMIN, TEAM_VIEWER 等）
 * - 每个角色都有明确的权限级别
 * - 支持角色比较和权限检查
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public enum TeamRole {
    
    /**
     * 团队创建者
     * - 拥有团队的完全控制权
     * - 可以管理团队信息（修改名称、描述、删除团队）
     * - 可以管理成员（邀请、移除、修改角色）
     * - 可以访问所有团队数据
     * - 权限级别：100
     */
    TEAM_CREATOR("团队创建者", 100),
    
    /**
     * 团队成员
     * - 团队的普通成员
     * - 可以访问团队数据
     * - 可以参与团队协作
     * - 无法管理团队或其他成员
     * - 权限级别：10
     */
    TEAM_MEMBER("团队成员", 10);
    
    /**
     * 角色显示名称
     */
    private final String displayName;
    
    /**
     * 权限级别（数值越大权限越高）
     */
    private final int permissionLevel;
    
    /**
     * 构造函数
     * 
     * @param displayName 角色显示名称
     * @param permissionLevel 权限级别
     */
    TeamRole(String displayName, int permissionLevel) {
        this.displayName = displayName;
        this.permissionLevel = permissionLevel;
    }
    
    /**
     * 获取角色显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * 获取权限级别
     * 
     * @return 权限级别
     */
    public int getPermissionLevel() {
        return permissionLevel;
    }
    
    /**
     * 检查当前角色是否有管理团队的权限
     * 
     * @return 是否可以管理团队
     */
    public boolean canManageTeam() {
        return this == TEAM_CREATOR;
    }
    
    /**
     * 检查当前角色是否有管理成员的权限
     * 
     * @return 是否可以管理成员
     */
    public boolean canManageMembers() {
        return this == TEAM_CREATOR;
    }
    
    /**
     * 检查当前角色是否有访问数据的权限
     * 
     * @return 是否可以访问数据
     */
    public boolean canAccessData() {
        return true; // 所有角色都可以访问数据
    }
    
    /**
     * 检查当前角色权限是否高于或等于指定角色
     * 
     * @param other 要比较的角色
     * @return 是否有更高或相等的权限
     */
    public boolean hasPermissionLevel(TeamRole other) {
        return this.permissionLevel >= other.permissionLevel;
    }
    
    /**
     * 检查当前角色权限是否高于指定角色
     * 
     * @param other 要比较的角色
     * @return 是否有更高的权限
     */
    public boolean hasHigherPermissionThan(TeamRole other) {
        return this.permissionLevel > other.permissionLevel;
    }
    
    /**
     * 根据isCreator布尔值获取对应的角色
     * 用于向后兼容现有的isCreator逻辑
     * 
     * @param isCreator 是否为创建者
     * @return 对应的团队角色
     */
    public static TeamRole fromIsCreator(boolean isCreator) {
        return isCreator ? TEAM_CREATOR : TEAM_MEMBER;
    }
    
    /**
     * 将角色转换为isCreator布尔值
     * 用于向后兼容现有的isCreator逻辑
     * 
     * @return 是否为创建者
     */
    public boolean toIsCreator() {
        return this == TEAM_CREATOR;
    }
    
    /**
     * 获取默认的邀请角色
     * 当用户被邀请加入团队时，默认分配的角色
     * 
     * @return 默认邀请角色
     */
    public static TeamRole getDefaultInvitationRole() {
        return TEAM_MEMBER;
    }
    
    @Override
    public String toString() {
        return displayName;
    }
}
