{"level":30,"time":1753801657612,"pid":1956,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] 如果你需要增加 Git 提交消息校验和自动代码格式化, max g precommit 就可以一键完成配置，详见 https://umijs.org/docs/guides/generator#precommit-配置生成器\u001b[39m"}
{"level":30,"time":1753801657615,"pid":1956,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753801657616,"pid":1956,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753801657637,"pid":1956,"hostname":"DESKTOP-KLNCNCN","msg":"generate files"}
{"level":30,"time":1753801659515,"pid":1956,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":30,"time":1753801682311,"pid":19740,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] HMR=none max dev 可以关闭 Umi 开发服务器的模块热替换功能。\u001b[39m"}
{"level":30,"time":1753801682313,"pid":19740,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753801682314,"pid":19740,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753801682315,"pid":19740,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753801683178,"pid":19740,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":30,"time":1753877058100,"pid":20088,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] max g tsconfig 可一键完成项目的 TypeScript 配置。\u001b[39m"}
{"level":30,"time":1753877058111,"pid":20088,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753877058111,"pid":20088,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753877058112,"pid":20088,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753877059492,"pid":20088,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":30,"time":1753880650095,"pid":20720,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] PORT=9000 max dev 可以指定 Umi 开发服务器的端口。\u001b[39m"}
{"level":30,"time":1753880650105,"pid":20720,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753880650106,"pid":20720,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753880650107,"pid":20720,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753880651607,"pid":20720,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":50,"time":1753883191023,"pid":20720,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"F:\\\\Project\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753883191537,"pid":20720,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":30,"time":1753883190942,"pid":9572,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] 想快速修改 html 模板、DIY 项目？试试编写项目级插件，详见 https://umijs.org/docs/guides/directory-structure#plugints\u001b[39m"}
{"level":30,"time":1753883190954,"pid":9572,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753883190954,"pid":9572,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753883190955,"pid":9572,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753883193061,"pid":9572,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":50,"time":1753883193122,"pid":20720,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 19 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753883193328,"pid":20720,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 19 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753884812395,"pid":20720,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"F:\\\\Project\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753884812602,"pid":20720,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 22 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":30,"time":1753884812281,"pid":18568,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] 遇到难解的配置问题，试试从 Umi FAQ 中寻找答案，详见 https://umijs.org/docs/introduce/faq\u001b[39m"}
{"level":30,"time":1753884812283,"pid":18568,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753884812283,"pid":18568,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753884812284,"pid":18568,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753884813557,"pid":18568,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":50,"time":1753884813673,"pid":20720,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 20 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":50,"time":1753884813918,"pid":20720,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 18 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\n..."}
{"level":30,"time":1753886054970,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] @umijs/max 是蚂蚁内网框架 Bigfish 的对外版本。\u001b[39m"}
{"level":30,"time":1753886054972,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753886054972,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753886054973,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753886055729,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":30,"time":1753886060447,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Memory Usage: 134.78 MB (RSS: 687.61 MB)"}
{"level":32,"time":1753886060497,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build user\\index.html"}
{"level":32,"time":1753886060498,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build user\\login\\index.html"}
{"level":32,"time":1753886060498,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build dashboard\\index.html"}
{"level":32,"time":1753886060499,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build team-management\\index.html"}
{"level":32,"time":1753886060499,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build team\\index.html"}
{"level":32,"time":1753886060500,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build team\\list\\index.html"}
{"level":32,"time":1753886060501,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build team\\detail\\index.html"}
{"level":32,"time":1753886060503,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build personal-center\\index.html"}
{"level":32,"time":1753886060504,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build user-manage\\index.html"}
{"level":32,"time":1753886060505,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build subscription\\index.html"}
{"level":32,"time":1753886060505,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build user\\invitations\\index.html"}
{"level":32,"time":1753886060506,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build help\\index.html"}
{"level":32,"time":1753886060507,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build index.html"}
{"level":32,"time":1753886060508,"pid":17572,"hostname":"DESKTOP-KLNCNCN","msg":"Build 404.html"}
{"level":30,"time":1753886091665,"pid":15168,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] 如果要支持低版本浏览器，可尝试新出的 legacy 配置项，详见 https://umijs.org/blog/legacy-browser\u001b[39m"}
{"level":30,"time":1753886091667,"pid":15168,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753886091667,"pid":15168,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753886091668,"pid":15168,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753886092327,"pid":15168,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":30,"time":1753888757380,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] @umijs/max 是蚂蚁内网框架 Bigfish 的对外版本。\u001b[39m"}
{"level":30,"time":1753888757382,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753888757382,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753888757382,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753888758038,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":30,"time":1753888761997,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Memory Usage: 134.91 MB (RSS: 706.79 MB)"}
{"level":32,"time":1753888762021,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build user\\index.html"}
{"level":32,"time":1753888762022,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build user\\login\\index.html"}
{"level":32,"time":1753888762023,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build dashboard\\index.html"}
{"level":32,"time":1753888762023,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build team-management\\index.html"}
{"level":32,"time":1753888762024,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build team\\index.html"}
{"level":32,"time":1753888762025,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build team\\list\\index.html"}
{"level":32,"time":1753888762025,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build team\\detail\\index.html"}
{"level":32,"time":1753888762026,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build personal-center\\index.html"}
{"level":32,"time":1753888762028,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build user-manage\\index.html"}
{"level":32,"time":1753888762029,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build subscription\\index.html"}
{"level":32,"time":1753888762030,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build user\\invitations\\index.html"}
{"level":32,"time":1753888762030,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build help\\index.html"}
{"level":32,"time":1753888762031,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build index.html"}
{"level":32,"time":1753888762036,"pid":11696,"hostname":"DESKTOP-KLNCNCN","msg":"Build 404.html"}
{"level":30,"time":1753889820605,"pid":19360,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] @umijs/max 和 umi 如何选择？max 内置了很多好用的插件，即开即用，而 umi 也可以手动配置插件，独立使用，详见 https://umijs.org/docs/guides/use-plugins\u001b[39m"}
{"level":30,"time":1753889820607,"pid":19360,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753889820607,"pid":19360,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753889820607,"pid":19360,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753889821274,"pid":19360,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":30,"time":1753889825245,"pid":19360,"hostname":"DESKTOP-KLNCNCN","msg":"Memory Usage: 134.91 MB (RSS: 673.58 MB)"}
{"level":32,"time":1753889825265,"pid":19360,"hostname":"DESKTOP-KLNCNCN","msg":"Build user\\index.html"}
{"level":32,"time":1753889825266,"pid":19360,"hostname":"DESKTOP-KLNCNCN","msg":"Build user\\login\\index.html"}
{"level":32,"time":1753889825266,"pid":19360,"hostname":"DESKTOP-KLNCNCN","msg":"Build dashboard\\index.html"}
{"level":32,"time":1753889825267,"pid":19360,"hostname":"DESKTOP-KLNCNCN","msg":"Build team-management\\index.html"}
{"level":32,"time":1753889825267,"pid":19360,"hostname":"DESKTOP-KLNCNCN","msg":"Build team\\index.html"}
{"level":32,"time":1753889825268,"pid":19360,"hostname":"DESKTOP-KLNCNCN","msg":"Build team\\list\\index.html"}
{"level":32,"time":1753889825268,"pid":19360,"hostname":"DESKTOP-KLNCNCN","msg":"Build team\\detail\\index.html"}
{"level":32,"time":1753889825269,"pid":19360,"hostname":"DESKTOP-KLNCNCN","msg":"Build personal-center\\index.html"}
{"level":32,"time":1753889825269,"pid":19360,"hostname":"DESKTOP-KLNCNCN","msg":"Build user-manage\\index.html"}
{"level":32,"time":1753889825270,"pid":19360,"hostname":"DESKTOP-KLNCNCN","msg":"Build subscription\\index.html"}
{"level":32,"time":1753889825270,"pid":19360,"hostname":"DESKTOP-KLNCNCN","msg":"Build user\\invitations\\index.html"}
{"level":32,"time":1753889825271,"pid":19360,"hostname":"DESKTOP-KLNCNCN","msg":"Build help\\index.html"}
{"level":32,"time":1753889825271,"pid":19360,"hostname":"DESKTOP-KLNCNCN","msg":"Build index.html"}
{"level":32,"time":1753889825272,"pid":19360,"hostname":"DESKTOP-KLNCNCN","msg":"Build 404.html"}
{"level":30,"time":1754047552091,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] HMR=none max dev 可以关闭 Umi 开发服务器的模块热替换功能。\u001b[39m"}
{"level":30,"time":1754047552101,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754047552102,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754047552102,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754047553489,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":50,"time":1754047929269,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 3 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:544:6: ERROR: Unexpected closing \"ProCard\" tag does not match opening \"div\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:546:0: ERROR: The character \"}\" is not valid inside a JSX element\nsrc/pages/personal-center/PersonalInfo.tsx:549:0: ERROR: Unexpected end of file before a closing \"Card\" tag"}
{"level":50,"time":1754047951149,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 6 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:392:14: ERROR: Unexpected closing \"Col\" tag does not match opening \"div\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:564:12: ERROR: Unexpected closing \"Row\" tag does not match opening \"Col\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:565:10: ERROR: Unexpected closing \"Spin\" tag does not match opening \"Row\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:566:7: ERROR: The character \"}\" is not valid inside a JSX element\nsrc/pages/personal-center/PersonalInfo.tsx:578:6: ERROR: Unexpected closing \"ProCard\" tag does not match opening \"Spin\" tag\n..."}
{"level":50,"time":1754047986274,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 3 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:598:6: ERROR: Unexpected closing \"ProCard\" tag does not match opening \"div\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:600:0: ERROR: The character \"}\" is not valid inside a JSX element\nsrc/pages/personal-center/PersonalInfo.tsx:603:0: ERROR: Unexpected end of file before a closing \"Card\" tag"}
{"level":50,"time":1754048023351,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 3 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:628:6: ERROR: Unexpected closing \"ProCard\" tag does not match opening \"div\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:630:0: ERROR: The character \"}\" is not valid inside a JSX element\nsrc/pages/personal-center/PersonalInfo.tsx:633:0: ERROR: Unexpected end of file before a closing \"Card\" tag"}
{"level":50,"time":1754048064661,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 3 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:697:6: ERROR: Unexpected closing \"ProCard\" tag does not match opening \"div\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:699:0: ERROR: The character \"}\" is not valid inside a JSX element\nsrc/pages/personal-center/PersonalInfo.tsx:702:0: ERROR: Unexpected end of file before a closing \"Card\" tag"}
{"level":50,"time":1754049010221,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 4 errors:\nsrc/pages/personal-center/TeamListCard.tsx:1031:8: ERROR: Unexpected closing \"ProCard\" tag does not match opening \"div\" tag\nsrc/pages/personal-center/TeamListCard.tsx:1079:6: ERROR: Unexpected closing fragment tag does not match opening \"Card\" tag\nsrc/pages/personal-center/TeamListCard.tsx:1081:0: ERROR: The character \"}\" is not valid inside a JSX element\nsrc/pages/personal-center/TeamListCard.tsx:1084:0: ERROR: Unexpected end of file before a closing fragment tag"}
{"level":50,"time":1754049028163,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 5 errors:\nsrc/pages/personal-center/TeamListCard.tsx:1003:22: ERROR: Unexpected closing \"ProCard\" tag does not match opening \"Card\" tag\nsrc/pages/personal-center/TeamListCard.tsx:1009:8: ERROR: Unexpected closing \"ProCard\" tag does not match opening \"div\" tag\nsrc/pages/personal-center/TeamListCard.tsx:1057:6: ERROR: Unexpected closing fragment tag does not match opening \"Card\" tag\nsrc/pages/personal-center/TeamListCard.tsx:1059:0: ERROR: The character \"}\" is not valid inside a JSX element\nsrc/pages/personal-center/TeamListCard.tsx:1062:0: ERROR: Unexpected end of file before a closing fragment tag"}
{"level":50,"time":1754049076119,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 3 errors:\nsrc/pages/personal-center/TeamListCard.tsx:653:30: ERROR: Unexpected closing \"Flex\" tag does not match opening \"div\" tag\nsrc/pages/personal-center/TeamListCard.tsx:837:28: ERROR: Unexpected closing \"Flex\" tag does not match opening \"Card\" tag\nsrc/pages/personal-center/TeamListCard.tsx:838:30: ERROR: Unterminated regular expression"}
{"level":50,"time":1754049125349,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 3 errors:\nsrc/pages/personal-center/TeamListCard.tsx:813:28: ERROR: Unexpected closing \"Flex\" tag does not match opening \"div\" tag\nsrc/pages/personal-center/TeamListCard.tsx:814:26: ERROR: Unexpected closing \"Col\" tag does not match opening \"Card\" tag\nsrc/pages/personal-center/TeamListCard.tsx:816:24: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754049155078,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 3 errors:\nsrc/pages/personal-center/TeamListCard.tsx:778:28: ERROR: Unexpected closing \"Flex\" tag does not match opening \"div\" tag\nsrc/pages/personal-center/TeamListCard.tsx:779:26: ERROR: Unexpected closing \"Col\" tag does not match opening \"Card\" tag\nsrc/pages/personal-center/TeamListCard.tsx:781:24: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754049176602,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/TeamListCard.tsx:685:42: ERROR: Unterminated regular expression"}
{"level":50,"time":1754049206093,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/TeamListCard.tsx:684:28: ERROR: Unexpected closing \"Flex\" tag does not match opening \"Card\" tag\nsrc/pages/personal-center/TeamListCard.tsx:685:30: ERROR: Unterminated regular expression"}
{"level":50,"time":1754049225880,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/TeamListCard.tsx:686:28: ERROR: Expected \")\" but found \"gutter\""}
{"level":50,"time":1754049277987,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/TeamListCard.tsx:689:14: ERROR: Unterminated regular expression"}
{"level":50,"time":1754049305959,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/TeamListCard.tsx:689:14: ERROR: Unterminated regular expression"}
{"level":50,"time":1754049330337,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/TeamListCard.tsx:689:14: ERROR: Unterminated regular expression"}
{"level":50,"time":1754049404148,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/TeamListCard.tsx:689:14: ERROR: Unterminated regular expression"}
{"level":50,"time":1754049421463,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/TeamListCard.tsx:689:14: ERROR: Unterminated regular expression"}
{"level":50,"time":1754049440820,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/TeamListCard.tsx:690:14: ERROR: Unterminated regular expression"}
{"level":50,"time":1754049538234,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/TeamListCard.tsx:690:14: ERROR: Unterminated regular expression"}
{"level":50,"time":1754049583794,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/TeamListCard.tsx:690:14: ERROR: Unterminated regular expression\nsrc/pages/personal-center/TodoManagement.tsx:683:6: ERROR: Unexpected closing \"ProCard\" tag does not match opening \"Card\" tag"}
{"level":50,"time":1754049604586,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 3 errors:\nsrc/pages/personal-center/TeamListCard.tsx:690:14: ERROR: Unterminated regular expression\nsrc/pages/personal-center/TodoManagement.tsx:300:10: ERROR: Unexpected closing \"Row\" tag does not match opening \"Card\" tag\nsrc/pages/personal-center/TodoManagement.tsx:302:8: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754049634184,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/TeamListCard.tsx:690:14: ERROR: Unterminated regular expression\nsrc/pages/personal-center/TodoManagement.tsx:585:6: ERROR: Unexpected closing \"ProCard\" tag does not match opening \"Card\" tag"}
{"level":50,"time":1754049663237,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/TeamListCard.tsx:690:14: ERROR: Unterminated regular expression\nsrc/pages/personal-center/TodoManagement.tsx:578:6: ERROR: Unexpected closing \"ProCard\" tag does not match opening \"Card\" tag"}
{"level":50,"time":1754049686963,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/TeamListCard.tsx:690:14: ERROR: Unterminated regular expression\nsrc/pages/personal-center/TodoManagement.tsx:489:13: ERROR: Expected \")\" but found \"}\""}
{"level":50,"time":1754049713665,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 3 errors:\nsrc/pages/personal-center/TeamListCard.tsx:690:14: ERROR: Unterminated regular expression\nsrc/pages/personal-center/TodoManagement.tsx:394:22: ERROR: Unexpected closing \"Flex\" tag does not match opening \"div\" tag\nsrc/pages/personal-center/TodoManagement.tsx:455:13: ERROR: Expected \")\" but found \"}\""}
{"level":50,"time":1754049744668,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 3 errors:\nsrc/pages/personal-center/TeamListCard.tsx:690:14: ERROR: Unterminated regular expression\nsrc/pages/personal-center/TodoManagement.tsx:424:22: ERROR: Unexpected closing \"Dropdown\" tag does not match opening \"Card\" tag\nsrc/pages/personal-center/TodoManagement.tsx:425:25: ERROR: Unterminated regular expression"}
{"level":50,"time":1754049763666,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 3 errors:\nsrc/pages/personal-center/TeamListCard.tsx:690:14: ERROR: Unterminated regular expression\nsrc/pages/personal-center/TodoManagement.tsx:502:10: ERROR: Unexpected closing \"Spin\" tag does not match opening \"Card\" tag\nsrc/pages/personal-center/TodoManagement.tsx:504:5: ERROR: Expected identifier but found \"/\""}
{"level":50,"time":1754049781295,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/TeamListCard.tsx:690:14: ERROR: Unterminated regular expression"}
{"level":50,"time":1754049793582,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/TeamListCard.tsx:690:14: ERROR: Unterminated regular expression"}
{"level":50,"time":1754049968612,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/TeamListCard.tsx:690:14: ERROR: Unterminated regular expression"}
{"level":50,"time":1754050101349,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/TeamListCard.tsx:690:14: ERROR: Unterminated regular expression\nsrc/pages/personal-center/index.tsx:7:27: ERROR: Could not resolve \"./TodoManagement\""}
{"level":50,"time":1754050109141,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/TeamListCard.tsx:690:14: ERROR: Unterminated regular expression"}
{"level":50,"time":1754050878666,"pid":13016,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/TeamListCard.tsx:690:14: ERROR: Unterminated regular expression"}
{"level":30,"time":1754050901248,"pid":14236,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] ANALYZE=1 max build 可以分析产物的源码构成。\u001b[39m"}
{"level":30,"time":1754050901254,"pid":14236,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754050901254,"pid":14236,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754050901255,"pid":14236,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754050901962,"pid":14236,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":60,"time":1754050902226,"pid":14236,"hostname":"DESKTOP-KLNCNCN","err":{"type":"Error","message":"Build failed with 1 error:\nsrc/pages/personal-center/TeamListCard.tsx:690:14: ERROR: Unterminated regular expression","stack":"Error: Build failed with 1 error:\nsrc/pages/personal-center/TeamListCard.tsx:690:14: ERROR: Unterminated regular expression\n    at failureErrorWithLog (F:\\Project\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1472:15)\n    at F:\\Project\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:945:25\n    at F:\\Project\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1353:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","errors":[{"id":"","location":{"column":14,"file":"src/pages/personal-center/TeamListCard.tsx","length":0,"line":690,"lineText":"        </div>","namespace":"","suggestion":""},"notes":[],"pluginName":"","text":"Unterminated regular expression"}],"warnings":[]},"msg":"Build failed with 1 error:\nsrc/pages/personal-center/TeamListCard.tsx:690:14: ERROR: Unterminated regular expression"}
{"level":60,"time":1754050902233,"pid":14236,"hostname":"DESKTOP-KLNCNCN","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1754050902248,"pid":14236,"hostname":"DESKTOP-KLNCNCN","msg":"F:\\Project\\teamAuth\\frontend\\node_modules\\.cache\\logger\\umi.log"}
{"level":60,"time":1754050902264,"pid":14236,"hostname":"DESKTOP-KLNCNCN","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":30,"time":1754050945350,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] 想快速修改 html 模板、DIY 项目？试试编写项目级插件，详见 https://umijs.org/docs/guides/directory-structure#plugints\u001b[39m"}
{"level":30,"time":1754050945357,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754050945357,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754050945358,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754050946040,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":50,"time":1754051353044,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not read from file: F:\\Project\\teamAuth\\frontend\\src\\.umi\\umi.ts"}
{"level":50,"time":1754051353322,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 22 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":50,"time":1754051354202,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 20 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754051354390,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 18 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":30,"time":1754051353020,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] dev 模式下访问 /__umi 路由，可以发现很多有用的内部信息。\u001b[39m"}
{"level":30,"time":1754051353022,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754051353022,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754051353023,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754051354163,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":50,"time":1754051479010,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"F:\\\\Project\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1754051479307,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 22 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":50,"time":1754051479037,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"F:\\\\Project\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1754051479316,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 22 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":30,"time":1754051478960,"pid":3680,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] 全局样式、全局脚本写在哪里？创建 src/global.(ts|css) 轻松解决，详见 https://umijs.org/docs/guides/directory-structure#globaljtsx\u001b[39m"}
{"level":30,"time":1754051478962,"pid":3680,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754051478962,"pid":3680,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754051478963,"pid":3680,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754051479839,"pid":3680,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":50,"time":1754051479944,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 18 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754051479878,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754051480087,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 21 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754055872909,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754055872867,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754055971048,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754055971027,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754055982632,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754055982646,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754055996319,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754055996323,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056017769,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056017770,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056029817,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056029815,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056044822,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056044822,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056060005,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056059935,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056077432,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056077481,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056091844,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056091794,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056102176,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056102229,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056117298,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056117336,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056127840,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056127854,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056143370,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056143369,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056186957,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056186955,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056197932,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056197939,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056213471,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056213473,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056224326,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056224326,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056235492,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056235497,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056253995,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056253996,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056266035,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056266036,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:487:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:489:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056275015,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056275011,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056277449,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056277448,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056293960,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056293963,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056328234,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"F:\\\\Project\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1754056328563,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 24 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":50,"time":1754056328239,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"F:\\\\Project\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1754056328561,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 24 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":50,"time":1754056329701,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 25 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":30,"time":1754056328165,"pid":19120,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] 全局布局用 layout ，多层布局用 wrappers ，从文档了解更多路由的控制方法，详见 https://umijs.org/docs/guides/routes\u001b[39m"}
{"level":30,"time":1754056328176,"pid":19120,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754056328176,"pid":19120,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754056328177,"pid":19120,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754056329687,"pid":19120,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":50,"time":1754056329916,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754056329701,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 25 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754056330020,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":60,"time":1754056330109,"pid":19120,"hostname":"DESKTOP-KLNCNCN","err":{"type":"Error","message":"Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\"","stack":"Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\"\n    at failureErrorWithLog (F:\\Project\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1472:15)\n    at F:\\Project\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:945:25\n    at F:\\Project\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1353:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","errors":[{"id":"","location":{"column":8,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":485,"lineText":"      </div>","namespace":"","suggestion":"ProCard"},"notes":[{"location":{"column":5,"file":"src/pages/personal-center/PersonalInfo.tsx","length":7,"line":114,"lineText":"    <ProCard","namespace":"","suggestion":""},"text":"The opening \"ProCard\" tag is here:"}],"pluginName":"","text":"Unexpected closing \"div\" tag does not match opening \"ProCard\" tag"},{"id":"","location":{"column":6,"file":"src/pages/personal-center/PersonalInfo.tsx","length":1,"line":487,"lineText":"      {/* 统一设置Modal */}","namespace":"","suggestion":")"},"notes":[],"pluginName":"","text":"Expected \")\" but found \"{\""}],"warnings":[]},"msg":"Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":60,"time":1754056330251,"pid":19120,"hostname":"DESKTOP-KLNCNCN","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1754056330267,"pid":19120,"hostname":"DESKTOP-KLNCNCN","msg":"F:\\Project\\teamAuth\\frontend\\node_modules\\.cache\\logger\\umi.log"}
{"level":60,"time":1754056330282,"pid":19120,"hostname":"DESKTOP-KLNCNCN","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":50,"time":1754056535061,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056535045,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056545668,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056545662,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056555638,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056555636,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056566145,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056566144,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056576979,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056576980,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056589055,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056589057,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056603722,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056603723,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056616535,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056616537,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056627052,"pid":2484,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":50,"time":1754056627162,"pid":11928,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":30,"time":1754056868295,"pid":20128,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] 如果想检测未使用的文件和导出，可尝试新出的 deadCode 配置项，详见 https://umijs.org/docs/api/config#deadcode\u001b[39m"}
{"level":30,"time":1754056868301,"pid":20128,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754056868301,"pid":20128,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754056868302,"pid":20128,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754056869002,"pid":20128,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":60,"time":1754056869338,"pid":20128,"hostname":"DESKTOP-KLNCNCN","err":{"type":"Error","message":"Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\"","stack":"Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\"\n    at failureErrorWithLog (F:\\Project\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1472:15)\n    at F:\\Project\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:945:25\n    at F:\\Project\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1353:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","errors":[{"id":"","location":{"column":8,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":485,"lineText":"      </div>","namespace":"","suggestion":"ProCard"},"notes":[{"location":{"column":5,"file":"src/pages/personal-center/PersonalInfo.tsx","length":7,"line":114,"lineText":"    <ProCard","namespace":"","suggestion":""},"text":"The opening \"ProCard\" tag is here:"}],"pluginName":"","text":"Unexpected closing \"div\" tag does not match opening \"ProCard\" tag"},{"id":"","location":{"column":6,"file":"src/pages/personal-center/PersonalInfo.tsx","length":1,"line":487,"lineText":"      {/* 统一设置Modal */}","namespace":"","suggestion":")"},"notes":[],"pluginName":"","text":"Expected \")\" but found \"{\""}],"warnings":[]},"msg":"Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":60,"time":1754056869401,"pid":20128,"hostname":"DESKTOP-KLNCNCN","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1754056869417,"pid":20128,"hostname":"DESKTOP-KLNCNCN","msg":"F:\\Project\\teamAuth\\frontend\\node_modules\\.cache\\logger\\umi.log"}
{"level":60,"time":1754056869431,"pid":20128,"hostname":"DESKTOP-KLNCNCN","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":30,"time":1754056939168,"pid":17988,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] 如果你需要使用 Jest 来测试 Umi 项目, max g jest 就可以一键完成配置，详见 https://umijs.org/docs/guides/generator#jest-配置生成器\u001b[39m"}
{"level":30,"time":1754056939169,"pid":17988,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754056939170,"pid":17988,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754056939170,"pid":17988,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754056939885,"pid":17988,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":30,"time":1754056996281,"pid":10012,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] PORT=9000 max dev 可以指定 Umi 开发服务器的端口。\u001b[39m"}
{"level":30,"time":1754056996283,"pid":10012,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754056996284,"pid":10012,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754056996284,"pid":10012,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754056996974,"pid":10012,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":50,"time":1754057063340,"pid":10012,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":30,"time":1754057111655,"pid":3944,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] max g page 可以快速生成页面模板，详见 https://umijs.org/docs/guides/generator#页面生成器\u001b[39m"}
{"level":30,"time":1754057111657,"pid":3944,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754057111657,"pid":3944,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754057111658,"pid":3944,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754057112345,"pid":3944,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":60,"time":1754057112617,"pid":3944,"hostname":"DESKTOP-KLNCNCN","err":{"type":"Error","message":"Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\"","stack":"Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\"\n    at failureErrorWithLog (F:\\Project\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1472:15)\n    at F:\\Project\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:945:25\n    at F:\\Project\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1353:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","errors":[{"id":"","location":{"column":8,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":485,"lineText":"      </div>","namespace":"","suggestion":"ProCard"},"notes":[{"location":{"column":5,"file":"src/pages/personal-center/PersonalInfo.tsx","length":7,"line":114,"lineText":"    <ProCard","namespace":"","suggestion":""},"text":"The opening \"ProCard\" tag is here:"}],"pluginName":"","text":"Unexpected closing \"div\" tag does not match opening \"ProCard\" tag"},{"id":"","location":{"column":6,"file":"src/pages/personal-center/PersonalInfo.tsx","length":1,"line":487,"lineText":"      {/* 统一设置Modal */}","namespace":"","suggestion":")"},"notes":[],"pluginName":"","text":"Expected \")\" but found \"{\""}],"warnings":[]},"msg":"Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:485:8: ERROR: Unexpected closing \"div\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:487:6: ERROR: Expected \")\" but found \"{\""}
{"level":60,"time":1754057112633,"pid":3944,"hostname":"DESKTOP-KLNCNCN","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1754057112647,"pid":3944,"hostname":"DESKTOP-KLNCNCN","msg":"F:\\Project\\teamAuth\\frontend\\node_modules\\.cache\\logger\\umi.log"}
{"level":60,"time":1754057112663,"pid":3944,"hostname":"DESKTOP-KLNCNCN","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":30,"time":1754057184819,"pid":11604,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] 如果你有 MPA（多页应用）需求，可尝试新出的 mpa 配置项，详见 https://umijs.org/docs/guides/mpa\u001b[39m"}
{"level":30,"time":1754057184821,"pid":11604,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754057184821,"pid":11604,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754057184822,"pid":11604,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754057185496,"pid":11604,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":30,"time":1754144053138,"pid":19276,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] max g component 可以快速生成组件模板，详见 https://umijs.org/docs/guides/generator#组件生成器\u001b[39m"}
{"level":30,"time":1754144053143,"pid":19276,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754144053144,"pid":19276,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754144053145,"pid":19276,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754144054124,"pid":19276,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":30,"time":1754144246465,"pid":8460,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] @umijs/max 和 umi 如何选择？max 内置了很多好用的插件，即开即用，而 umi 也可以手动配置插件，独立使用，详见 https://umijs.org/docs/guides/use-plugins\u001b[39m"}
{"level":30,"time":1754144246467,"pid":8460,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754144246467,"pid":8460,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754144246468,"pid":8460,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754144247182,"pid":8460,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":50,"time":1754146362804,"pid":8460,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 3 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:209:16: ERROR: Unexpected closing \"Col\" tag does not match opening \"div\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:413:14: ERROR: Unexpected closing \"Row\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:414:17: ERROR: Unterminated regular expression"}
{"level":50,"time":1754146378465,"pid":8460,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:402:14: ERROR: Unexpected closing \"Row\" tag does not match opening \"ProCard\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:403:17: ERROR: Unterminated regular expression"}
{"level":50,"time":1754147721624,"pid":8460,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:180:12: ERROR: Unexpected closing \"div\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:181:15: ERROR: Unterminated regular expression"}
{"level":50,"time":1754147822517,"pid":8460,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"F:\\\\Project\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1754147822829,"pid":8460,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 22 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":30,"time":1754147822443,"pid":17708,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[33m[你知道吗？] max g component 可以快速生成组件模板，详见 https://umijs.org/docs/guides/generator#组件生成器\u001b[39m"}
{"level":30,"time":1754147822452,"pid":17708,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754147822452,"pid":17708,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754147822453,"pid":17708,"hostname":"DESKTOP-KLNCNCN","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754147823787,"pid":17708,"hostname":"DESKTOP-KLNCNCN","msg":"Preparing..."}
{"level":50,"time":1754147823881,"pid":8460,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 18 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'F:/Project/teamAuth/frontend/src/.umi/exports' in 'F:\\Project\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754148472019,"pid":17708,"hostname":"DESKTOP-KLNCNCN","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:185:12: ERROR: Unexpected closing \"div\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:186:15: ERROR: Unterminated regular expression"}
