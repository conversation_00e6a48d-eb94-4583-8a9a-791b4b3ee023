package com.teammanage.service;

import com.teammanage.exception.BusinessException;
import com.teammanage.exception.DatabaseException;
import com.teammanage.exception.NetworkException;
import com.teammanage.exception.RateLimitException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 错误监控服务测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class ErrorMonitoringServiceTest {

    @InjectMocks
    private ErrorMonitoringService errorMonitoringService;

    @Mock
    private RateLimitService rateLimitService;

    @BeforeEach
    void setUp() {
        // 清除之前的统计数据
        errorMonitoringService.clearErrorStatistics();
    }

    @Test
    void testRecordBusinessException() {
        // Given
        BusinessException exception = new BusinessException("测试业务异常");
        Long userId = 1L;
        String operation = "TEST_OPERATION";
        Map<String, Object> additionalInfo = new HashMap<>();
        additionalInfo.put("testKey", "testValue");

        // When
        errorMonitoringService.recordBusinessException(exception, userId, operation, additionalInfo);

        // Then
        Map<String, Object> statistics = errorMonitoringService.getErrorStatistics();
        assertNotNull(statistics);
        
        @SuppressWarnings("unchecked")
        Map<String, Long> errorCounts = (Map<String, Long>) statistics.get("errorCounts");
        assertEquals(1L, errorCounts.get("BUSINESS_ERROR"));
        assertEquals(1L, statistics.get("totalErrors"));
        assertEquals("GOOD", statistics.get("healthStatus"));
    }

    @Test
    void testRecordDatabaseException() {
        // Given
        DatabaseException exception = DatabaseException.connectionFailed(new RuntimeException("Connection failed"));
        Long userId = 2L;
        String operation = "DATABASE_OPERATION";

        // When
        errorMonitoringService.recordDatabaseException(exception, userId, operation, null);

        // Then
        Map<String, Object> statistics = errorMonitoringService.getErrorStatistics();
        
        @SuppressWarnings("unchecked")
        Map<String, Long> errorCounts = (Map<String, Long>) statistics.get("errorCounts");
        assertEquals(1L, errorCounts.get("DATABASE_ERROR"));
    }

    @Test
    void testRecordNetworkException() {
        // Given
        NetworkException exception = NetworkException.emailServiceError("SEND_EMAIL", new RuntimeException("SMTP failed"));
        Long userId = 3L;
        String operation = "EMAIL_OPERATION";

        // When
        errorMonitoringService.recordNetworkException(exception, userId, operation, null);

        // Then
        Map<String, Object> statistics = errorMonitoringService.getErrorStatistics();
        
        @SuppressWarnings("unchecked")
        Map<String, Long> errorCounts = (Map<String, Long>) statistics.get("errorCounts");
        assertEquals(1L, errorCounts.get("NETWORK_ERROR"));
    }

    @Test
    void testRecordRateLimitException() {
        // Given
        RateLimitException exception = RateLimitException.apiCallLimit(60L, 5, 10);
        Long userId = 4L;
        String operation = "API_CALL";

        // When
        errorMonitoringService.recordRateLimitException(exception, userId, operation, null);

        // Then
        Map<String, Object> statistics = errorMonitoringService.getErrorStatistics();
        
        @SuppressWarnings("unchecked")
        Map<String, Long> errorCounts = (Map<String, Long>) statistics.get("errorCounts");
        assertEquals(1L, errorCounts.get("RATE_LIMIT_ERROR"));
    }

    @Test
    void testRecordSystemException() {
        // Given
        Exception exception = new RuntimeException("系统异常");
        Long userId = 5L;
        String operation = "SYSTEM_OPERATION";

        // When
        errorMonitoringService.recordSystemException(exception, userId, operation, null);

        // Then
        Map<String, Object> statistics = errorMonitoringService.getErrorStatistics();
        
        @SuppressWarnings("unchecked")
        Map<String, Long> errorCounts = (Map<String, Long>) statistics.get("errorCounts");
        assertEquals(1L, errorCounts.get("SYSTEM_ERROR"));
    }

    @Test
    void testMultipleErrorsAndHealthStatus() {
        // Given - 记录多个不同类型的错误
        BusinessException businessException = new BusinessException("业务异常1");
        DatabaseException databaseException = DatabaseException.connectionFailed(new RuntimeException("DB异常"));
        
        // When
        errorMonitoringService.recordBusinessException(businessException, 1L, "OP1", null);
        errorMonitoringService.recordBusinessException(businessException, 1L, "OP2", null);
        errorMonitoringService.recordDatabaseException(databaseException, 2L, "OP3", null);

        // Then
        Map<String, Object> statistics = errorMonitoringService.getErrorStatistics();
        
        @SuppressWarnings("unchecked")
        Map<String, Long> errorCounts = (Map<String, Long>) statistics.get("errorCounts");
        assertEquals(2L, errorCounts.get("BUSINESS_ERROR"));
        assertEquals(1L, errorCounts.get("DATABASE_ERROR"));
        assertEquals(3L, statistics.get("totalErrors"));
        assertEquals("GOOD", statistics.get("healthStatus")); // 3个错误仍然是GOOD状态
    }

    @Test
    void testHealthStatusProgression() {
        // Test EXCELLENT status (0 errors)
        Map<String, Object> stats = errorMonitoringService.getErrorStatistics();
        assertEquals("EXCELLENT", stats.get("healthStatus"));
        assertEquals(0L, stats.get("totalErrors"));

        // Test GOOD status (< 10 errors)
        BusinessException exception = new BusinessException("测试异常");
        for (int i = 0; i < 5; i++) {
            errorMonitoringService.recordBusinessException(exception, 1L, "TEST", null);
        }
        stats = errorMonitoringService.getErrorStatistics();
        assertEquals("GOOD", stats.get("healthStatus"));
        assertEquals(5L, stats.get("totalErrors"));

        // Test WARNING status (10-49 errors)
        for (int i = 0; i < 10; i++) {
            errorMonitoringService.recordBusinessException(exception, 1L, "TEST", null);
        }
        stats = errorMonitoringService.getErrorStatistics();
        assertEquals("WARNING", stats.get("healthStatus"));
        assertEquals(15L, stats.get("totalErrors"));

        // Test CRITICAL status (>= 50 errors)
        for (int i = 0; i < 40; i++) {
            errorMonitoringService.recordBusinessException(exception, 1L, "TEST", null);
        }
        stats = errorMonitoringService.getErrorStatistics();
        assertEquals("CRITICAL", stats.get("healthStatus"));
        assertEquals(55L, stats.get("totalErrors"));
    }

    @Test
    void testGetRecentErrors() {
        // Given
        BusinessException exception = new BusinessException("测试异常");
        
        // When
        errorMonitoringService.recordBusinessException(exception, 1L, "OP1", null);
        errorMonitoringService.recordBusinessException(exception, 2L, "OP2", null);

        // Then
        Map<String, ErrorMonitoringService.ErrorRecord> recentErrors = 
                errorMonitoringService.getRecentErrors(10);
        
        assertEquals(2, recentErrors.size());
        
        // 验证错误记录的内容
        ErrorMonitoringService.ErrorRecord firstRecord = recentErrors.values().iterator().next();
        assertEquals("BUSINESS_ERROR", firstRecord.getErrorType());
        assertEquals("测试异常", firstRecord.getMessage());
        assertNotNull(firstRecord.getTimestamp());
    }

    @Test
    void testClearErrorStatistics() {
        // Given - 先记录一些错误
        BusinessException exception = new BusinessException("测试异常");
        errorMonitoringService.recordBusinessException(exception, 1L, "TEST", null);
        
        // 验证有错误记录
        Map<String, Object> stats = errorMonitoringService.getErrorStatistics();
        assertEquals(1L, stats.get("totalErrors"));

        // When - 清除统计
        errorMonitoringService.clearErrorStatistics();

        // Then - 验证统计已清除
        stats = errorMonitoringService.getErrorStatistics();
        assertEquals(0L, stats.get("totalErrors"));
        assertEquals("EXCELLENT", stats.get("healthStatus"));
        
        Map<String, ErrorMonitoringService.ErrorRecord> recentErrors = 
                errorMonitoringService.getRecentErrors(10);
        assertEquals(0, recentErrors.size());
    }
}
