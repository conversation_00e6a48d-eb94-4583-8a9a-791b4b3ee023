/**
 * Token 解析工具模块
 *
 * 这个模块提供了一套完整的JWT Token解析工具，用于在前端直接从Token中提取信息。
 * 主要用于避免依赖异步状态更新，实现即时的Token信息获取。
 *
 * 主要功能：
 * 1. JWT Token解析：安全地解析JWT的payload部分
 * 2. 用户信息提取：从Token中提取用户ID、邮箱等信息
 * 3. 团队信息提取：从Token中提取团队ID、创建者状态等信息
 * 4. Token状态检查：检查Token是否包含特定信息
 * 5. 错误处理：安全地处理解析失败的情况
 *
 * 使用场景：
 * - 路由守卫中快速检查用户状态
 * - 组件中即时获取当前用户/团队信息
 * - 避免等待异步状态更新的场景
 * - Token切换后的即时状态检查
 *
 * 安全考虑：
 * - 只解析Token的payload部分，不验证签名
 * - 所有解析操作都有错误处理
 * - 不在控制台输出敏感信息
 * - 解析失败时返回安全的默认值
 */

/**
 * 解析 JWT Token 的 payload 部分
 *
 * 这是Token解析的核心函数，负责安全地解析JWT Token的payload部分。
 * JWT Token由三部分组成：header.payload.signature，这里只解析payload部分。
 *
 * 解析流程：
 * 1. 验证Token格式：确保Token包含三个由'.'分隔的部分
 * 2. 提取payload：获取Token的第二部分（payload）
 * 3. Base64URL解码：处理JWT特有的base64url编码格式
 * 4. JSON解析：将解码后的字符串解析为JavaScript对象
 * 5. 错误处理：捕获并处理所有可能的解析错误
 *
 * Base64URL编码处理：
 * - 替换'-'为'+'，'_'为'/'（base64url到base64的转换）
 * - 添加必要的padding字符'='
 * - 确保字符串长度是4的倍数
 *
 * 安全特性：
 * - 不验证Token签名（签名验证由后端负责）
 * - 解析失败时返回null而不是抛出异常
 * - 不在日志中输出Token内容
 * - 对所有异常情况都有处理
 *
 * @param token JWT Token字符串，格式为 header.payload.signature
 * @returns 解析后的payload对象，包含Token中的所有声明；解析失败时返回null
 *
 * @example
 * ```typescript
 * const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEyMywibmFtZSI6IkpvaG4ifQ.signature';
 * const payload = parseJwtPayload(token);
 *
 * if (payload) {
 *   console.log('用户ID:', payload.userId);
 *   console.log('用户名:', payload.name);
 * } else {
 *   console.log('Token解析失败');
 * }
 * ```
 */
export function parseJwtPayload(token: string): any {
  try {
    // 验证JWT Token格式：必须包含三个由'.'分隔的部分
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid JWT format: Token must have 3 parts separated by dots');
    }

    // 提取payload部分（第二部分）
    const payload = parts[1];

    // 处理base64url编码转换为标准base64编码
    // base64url使用'-'和'_'替代'+'和'/'，且不使用padding
    const base64 = payload.replace(/-/g, '+').replace(/_/g, '/');

    // 添加必要的padding字符，确保字符串长度是4的倍数
    const paddedBase64 = base64 + '='.repeat((4 - (base64.length % 4)) % 4);

    // 使用浏览器内置的atob函数进行base64解码
    const decodedPayload = atob(paddedBase64);

    // 将解码后的JSON字符串解析为JavaScript对象
    return JSON.parse(decodedPayload);
  } catch (error) {
    // 捕获所有可能的错误：格式错误、解码错误、JSON解析错误等
    console.error('Failed to parse JWT payload:', error);
    return null;
  }
}

/**
 * 从当前存储的Token中获取团队ID
 *
 * 这个函数用于快速获取当前用户选择的团队ID，无需等待异步状态更新。
 * 主要用于判断用户是否已选择团队，以及获取当前团队的标识。
 *
 * 工作流程：
 * 1. 从localStorage获取当前存储的Token
 * 2. 使用parseJwtPayload解析Token的payload
 * 3. 从payload中提取teamId字段
 * 4. 处理各种异常情况并返回安全的默认值
 *
 * Token状态说明：
 * - 用户Token：登录后的初始Token，不包含teamId字段
 * - 团队Token：选择团队后的Token，包含teamId字段
 * - 切换团队：会生成新的团队Token，包含新的teamId
 *
 * 返回值说明：
 * - number：有效的团队ID，表示用户已选择团队
 * - null：没有团队信息，可能是用户Token或解析失败
 *
 * 使用场景：
 * - 路由守卫中检查是否需要团队上下文
 * - 组件中判断是否显示团队相关功能
 * - 团队切换后的状态验证
 * - 避免重复的团队选择操作
 *
 * @returns number | null 团队ID（如果存在），否则返回null
 *
 * @example
 * ```typescript
 * const teamId = getTeamIdFromCurrentToken();
 *
 * if (teamId) {
 *   console.log('当前团队ID:', teamId);
 *   // 用户已选择团队，可以访问团队相关功能
 *   loadTeamData(teamId);
 * } else {
 *   console.log('用户未选择团队');
 *   // 跳转到团队选择页面或个人中心
 *   history.push('/personal-center');
 * }
 * ```
 */
export function getTeamIdFromCurrentToken(): number | null {
  try {
    // 从localStorage获取当前Token
    const token = localStorage.getItem('auth_token');
    if (!token) {
      // Token不存在，用户未登录
      return null;
    }

    // 解析Token的payload部分
    const payload = parseJwtPayload(token);
    if (!payload) {
      // Token解析失败，可能是格式错误或损坏
      return null;
    }

    // 从payload中提取teamId字段
    // 使用 || null 确保undefined也转换为null
    return payload.teamId || null;
  } catch (error) {
    // 捕获localStorage访问错误或其他异常
    console.error('Failed to get team ID from token:', error);
    return null;
  }
}

/**
 * 从当前存储的Token中获取用户ID
 *
 * 这个函数用于快速获取当前登录用户的唯一标识ID，是用户身份识别的核心方法。
 * 无论Token是用户Token还是团队Token，都包含用户ID信息。
 *
 * 工作流程：
 * 1. 从localStorage获取当前存储的Token
 * 2. 使用parseJwtPayload解析Token的payload
 * 3. 从payload中提取userId字段
 * 4. 处理各种异常情况并返回安全的默认值
 *
 * Token中的用户信息：
 * - userId：用户的唯一标识ID，数字类型
 * - email：用户邮箱地址
 * - name：用户显示名称
 * - 其他用户相关字段
 *
 * 返回值说明：
 * - number：有效的用户ID，表示用户已登录
 * - null：没有用户信息，表示用户未登录或Token无效
 *
 * 使用场景：
 * - 用户身份验证和权限检查
 * - 用户相关数据的查询和操作
 * - 日志记录和审计追踪
 * - 用户状态的快速检查
 * - 避免重复的用户信息获取
 *
 * 注意事项：
 * - 这个ID在整个系统中是唯一的
 * - 不会因为团队切换而改变
 * - 用于标识用户在所有团队中的身份
 *
 * @returns number | null 用户ID（如果存在），否则返回null
 *
 * @example
 * ```typescript
 * const userId = getUserIdFromCurrentToken();
 *
 * if (userId) {
 *   console.log('当前用户ID:', userId);
 *   // 用户已登录，可以进行用户相关操作
 *   loadUserProfile(userId);
 *   recordUserActivity(userId);
 * } else {
 *   console.log('用户未登录');
 *   // 跳转到登录页面
 *   history.push('/user/login');
 * }
 * ```
 */
export function getUserIdFromCurrentToken(): number | null {
  try {
    // 从localStorage获取当前Token
    const token = localStorage.getItem('auth_token');
    if (!token) {
      // Token不存在，用户未登录
      return null;
    }

    // 解析Token的payload部分
    const payload = parseJwtPayload(token);
    if (!payload) {
      // Token解析失败，可能是格式错误或损坏
      return null;
    }

    // 从payload中提取userId字段
    // 使用 || null 确保undefined也转换为null
    return payload.userId || null;
  } catch (error) {
    // 捕获localStorage访问错误或其他异常
    console.error('Failed to get user ID from token:', error);
    return null;
  }
}

/**
 * 检查当前Token是否包含团队信息
 *
 * 这是一个便捷函数，用于快速判断当前用户是否已选择团队。
 * 通过检查Token中是否包含有效的团队ID来判断团队上下文状态。
 *
 * 判断逻辑：
 * 1. 调用getTeamIdFromCurrentToken()获取团队ID
 * 2. 检查团队ID是否为有效值（非null且非undefined）
 * 3. 返回布尔值表示是否包含团队信息
 *
 * Token状态对应：
 * - true：用户已选择团队，Token包含团队上下文
 * - false：用户未选择团队，Token只包含用户信息
 *
 * 使用场景：
 * - 路由守卫中判断是否需要团队权限
 * - 组件中决定是否显示团队相关功能
 * - 菜单权限控制
 * - 页面访问权限检查
 *
 * @returns boolean 如果Token包含有效的团队信息返回true，否则返回false
 *
 * @example
 * ```typescript
 * if (hasTeamInCurrentToken()) {
 *   console.log('用户已选择团队，可以访问团队功能');
 *   // 显示团队相关的菜单和功能
 *   showTeamFeatures();
 * } else {
 *   console.log('用户未选择团队，显示团队选择界面');
 *   // 跳转到团队选择页面
 *   history.push('/personal-center');
 * }
 * ```
 */
export function hasTeamInCurrentToken(): boolean {
  const teamId = getTeamIdFromCurrentToken();
  return teamId !== null && teamId !== undefined;
}

/**
 * 从当前Token中获取用户是否为团队创建者
 *
 * 这个函数用于获取当前用户在所选团队中的创建者状态，用于权限控制。
 * 只有在用户已选择团队的情况下，这个字段才有意义。
 *
 * 工作流程：
 * 1. 从localStorage获取当前存储的Token
 * 2. 使用parseJwtPayload解析Token的payload
 * 3. 从payload中提取isCreator字段
 * 4. 处理各种异常情况并返回安全的默认值
 *
 * 权限级别说明：
 * - true：用户是团队创建者，拥有最高权限
 * - false：用户是普通团队成员，权限受限
 * - null：没有团队信息或解析失败
 *
 * 权限控制用途：
 * - 团队设置的访问权限
 * - 成员管理功能的显示
 * - 团队删除等危险操作的权限
 * - 邀请新成员的权限
 *
 * 注意事项：
 * - 只有在hasTeamInCurrentToken()返回true时，这个值才有意义
 * - 团队切换时，这个值会随着新Token更新
 * - 用于前端权限控制，后端仍需要验证
 *
 * @returns boolean | null 创建者状态（true/false），如果没有团队信息则返回null
 *
 * @example
 * ```typescript
 * const isCreator = getIsCreatorFromCurrentToken();
 *
 * if (isCreator === true) {
 *   console.log('用户是团队创建者，显示管理功能');
 *   // 显示团队管理、成员邀请等功能
 *   showAdminFeatures();
 * } else if (isCreator === false) {
 *   console.log('用户是普通成员，隐藏管理功能');
 *   // 隐藏管理功能，只显示基本功能
 *   showMemberFeatures();
 * } else {
 *   console.log('没有团队信息');
 *   // 用户未选择团队或Token无效
 * }
 * ```
 */
export function getIsCreatorFromCurrentToken(): boolean | null {
  try {
    // 从localStorage获取当前Token
    const token = localStorage.getItem('auth_token');
    if (!token) {
      // Token不存在，用户未登录
      return null;
    }

    // 解析Token的payload部分
    const payload = parseJwtPayload(token);
    if (!payload) {
      // Token解析失败，可能是格式错误或损坏
      return null;
    }

    // 从payload中提取isCreator字段
    // 使用 || null 确保undefined也转换为null
    return payload.isCreator || null;
  } catch (error) {
    // 捕获localStorage访问错误或其他异常
    console.error('Failed to get isCreator from token:', error);
    return null;
  }
}

/**
 * 获取当前Token的完整信息
 *
 * 这是一个综合性函数，一次性提取Token中的所有关键信息。
 * 相比于分别调用多个单独的函数，这个函数更高效，只需要解析一次Token。
 *
 * 功能特性：
 * 1. 一次性获取所有Token信息，避免重复解析
 * 2. 返回结构化的对象，包含所有关键字段
 * 3. 统一的错误处理，确保返回值的一致性
 * 4. 类型安全的返回值定义
 *
 * 返回信息说明：
 * - userId：用户唯一标识ID，所有Token都包含
 * - teamId：团队ID，只有团队Token包含
 * - isCreator：是否为团队创建者，只有团队Token包含
 * - email：用户邮箱地址，所有Token都包含
 * - name：用户显示名称，所有Token都包含
 *
 * 使用场景：
 * - 需要同时使用多个Token字段的场景
 * - 组件初始化时获取完整的用户状态
 * - 权限检查时需要多个字段的组合判断
 * - 用户信息展示组件
 * - 调试和日志记录
 *
 * 性能优势：
 * - 避免多次localStorage访问
 * - 避免多次Token解析
 * - 减少函数调用开销
 * - 统一的错误处理逻辑
 *
 * @returns 包含Token所有关键信息的对象，解析失败时所有字段为null
 *
 * @example
 * ```typescript
 * const tokenInfo = getCurrentTokenInfo();
 *
 * console.log('用户信息:', {
 *   id: tokenInfo.userId,
 *   email: tokenInfo.email,
 *   name: tokenInfo.name
 * });
 *
 * if (tokenInfo.teamId) {
 *   console.log('团队信息:', {
 *     teamId: tokenInfo.teamId,
 *     isCreator: tokenInfo.isCreator
 *   });
 *
 *   // 根据创建者状态显示不同的功能
 *   if (tokenInfo.isCreator) {
 *     showAdminPanel();
 *   } else {
 *     showMemberPanel();
 *   }
 * } else {
 *   console.log('用户未选择团队');
 *   showTeamSelection();
 * }
 * ```
 */
export function getCurrentTokenInfo(): {
  userId: number | null;
  teamId: number | null;
  isCreator: boolean | null;
  email: string | null;
  name: string | null;
} {
  try {
    // 从localStorage获取当前Token
    const token = localStorage.getItem('auth_token');
    if (!token) {
      // Token不存在时返回所有字段为null的对象
      return {
        userId: null,
        teamId: null,
        isCreator: null,
        email: null,
        name: null,
      };
    }

    // 解析Token的payload部分
    const payload = parseJwtPayload(token);
    if (!payload) {
      // Token解析失败时返回所有字段为null的对象
      return {
        userId: null,
        teamId: null,
        isCreator: null,
        email: null,
        name: null,
      };
    }

    // 从payload中提取所有关键字段
    // 使用 || null 确保undefined也转换为null，保持返回值的一致性
    return {
      userId: payload.userId || null,
      teamId: payload.teamId || null,
      isCreator: payload.isCreator || null,
      email: payload.email || null,
      name: payload.name || null,
    };
  } catch (error) {
    // 捕获所有异常并返回安全的默认值
    console.error('Failed to get token info:', error);
    return {
      userId: null,
      teamId: null,
      isCreator: null,
      email: null,
      name: null,
    };
  }
}
