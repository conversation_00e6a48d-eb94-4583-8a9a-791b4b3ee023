{"version": 3, "sources": ["src/.umi/plugin-layout/Exception.tsx", "src/.umi/plugin-layout/Layout.tsx", "src/.umi/plugin-layout/Logo.tsx", "src/.umi/plugin-layout/rightRender.tsx"], "sourcesContent": ["// @ts-nocheck\n// This file is generated by <PERSON><PERSON> automatically\n// DO NOT CHANGE IT MANUALLY!\nimport React from 'react';\nimport { history, type IRoute } from '@umijs/max';\nimport { Result, Button } from 'antd';\n\nconst Exception: React.FC<{\n  children: React.ReactNode;\n  route?: IRoute;\n  notFound?: React.ReactNode;\n  noAccessible?: React.ReactNode;\n  unAccessible?: React.ReactNode;\n  noFound?: React.ReactNode;\n}> = (props) => (\n  // render custom 404\n  (!props.route && (props.noFound || props.notFound)) ||\n  // render custom 403\n  (props.route?.unaccessible && (props.unAccessible || props.noAccessible)) ||\n  // render default exception\n  ((!props.route || props.route?.unaccessible) && (\n    <Result\n      status={props.route ? '403' : '404'}\n      title={props.route ? '403' : '404'}\n      subTitle={props.route ? '抱歉，你无权访问该页面' : '抱歉，你访问的页面不存在'}\n      extra={\n        <Button type=\"primary\" onClick={() => history.push('/')}>\n          返回首页\n        </Button>\n      }\n    />\n  )) ||\n  // normal render\n  props.children\n);\n\nexport default Exception;\n", "// @ts-nocheck\n// This file is generated by <PERSON><PERSON> automatically\n// DO NOT CHANGE IT MANUALLY!\n/// <reference types=\"@ant-design/pro-components\" />\n/// <reference types=\"antd\" />\n\nimport {\n  Link, useLocation, useNavigate, Outlet, useAppData, matchRoutes,\n  type IRoute\n} from '@umijs/max';\nimport React, { useMemo } from 'react';\nimport {\n  ProLayout,\n} from \"F:/Project/teamAuth/frontend/node_modules/@ant-design/pro-components\";\nimport './Layout.css';\nimport Logo from './Logo';\nimport Exception from './Exception';\nimport { getRightRenderContent } from './rightRender';\nimport { useModel } from '@@/plugin-model';\nimport { useAccessMarkedRoutes } from '@@/plugin-access';\n\n\n// 过滤出需要显示的路由, 这里的filterFn 指 不希望显示的层级\nconst filterRoutes = (routes: IRoute[], filterFn: (route: IRoute) => boolean) => {\n  if (routes.length === 0) {\n    return []\n  }\n\n  let newRoutes = []\n  for (const route of routes) {\n    const newRoute = {...route };\n    if (filterFn(route)) {\n      if (Array.isArray(newRoute.routes)) {\n        newRoutes.push(...filterRoutes(newRoute.routes, filterFn))\n      }\n    } else {\n      if (Array.isArray(newRoute.children)) {\n        newRoute.children = filterRoutes(newRoute.children, filterFn);\n        newRoute.routes = newRoute.children;\n      }\n      newRoutes.push(newRoute);\n    }\n  }\n\n  return newRoutes;\n}\n\n// 格式化路由 处理因 wrapper 导致的 菜单 path 不一致\nconst mapRoutes = (routes: IRoute[]) => {\n  if (routes.length === 0) {\n    return []\n  }\n  return routes.map(route => {\n    // 需要 copy 一份, 否则会污染原始数据\n    const newRoute = {...route}\n    if (route.originPath) {\n      newRoute.path = route.originPath\n    }\n\n    if (Array.isArray(route.routes)) {\n      newRoute.routes = mapRoutes(route.routes);\n    }\n\n    if (Array.isArray(route.children)) {\n      newRoute.children = mapRoutes(route.children);\n    }\n\n    return newRoute\n  })\n}\n\nexport default (props: any) => {\n  const location = useLocation();\n  const navigate = useNavigate();\n  const { clientRoutes, pluginManager } = useAppData();\n  const initialInfo = (useModel && useModel('@@initialState')) || {\n    initialState: undefined,\n    loading: false,\n    setInitialState: null,\n  };\n  const { initialState, loading, setInitialState } = initialInfo;\n  const userConfig = {\n  \"locale\": false,\n  \"navTheme\": \"light\",\n  \"colorPrimary\": \"#1890ff\",\n  \"layout\": \"side\",\n  \"contentWidth\": \"Fluid\",\n  \"fixedHeader\": false,\n  \"fixSiderbar\": true,\n  \"colorWeak\": false,\n  \"title\": \"团队协作管理系统\",\n  \"pwa\": false,\n  \"logo\": \"/logo.svg\",\n  \"iconfontUrl\": \"\",\n  \"token\": {}\n};\nconst formatMessage = undefined;\n  const runtimeConfig = pluginManager.applyPlugins({\n    key: 'layout',\n    type: 'modify',\n    initialValue: {\n      ...initialInfo\n    },\n  });\n\n\n  // 现在的 layout 及 wrapper 实现是通过父路由的形式实现的, 会导致路由数据多了冗余层级, proLayout 消费时, 无法正确展示菜单, 这里对冗余数据进行过滤操作\n  const newRoutes = filterRoutes(clientRoutes.filter(route => route.id === 'ant-design-pro-layout'), (route) => {\n    return (!!route.isLayout && route.id !== 'ant-design-pro-layout') || !!route.isWrapper;\n  })\n  const [route] = useAccessMarkedRoutes(mapRoutes(newRoutes));\n\n  const matchedRoute = useMemo(() => matchRoutes(route.children, location.pathname)?.pop?.()?.route, [location.pathname]);\n\n  return (\n    <ProLayout\n      route={route}\n      location={location}\n      title={userConfig.title || 'teamauth-frontend'}\n      navTheme=\"dark\"\n      siderWidth={256}\n      onMenuHeaderClick={(e) => {\n        e.stopPropagation();\n        e.preventDefault();\n        navigate('/');\n      }}\n      formatMessage={userConfig.formatMessage || formatMessage}\n      menu={{ locale: userConfig.locale }}\n      logo={Logo}\n      menuItemRender={(menuItemProps, defaultDom) => {\n        if (menuItemProps.isUrl || menuItemProps.children) {\n          return defaultDom;\n        }\n        if (menuItemProps.path && location.pathname !== menuItemProps.path) {\n          return (\n            // handle wildcard route path, for example /slave/* from qiankun\n            <Link to={menuItemProps.path.replace('/*', '')} target={menuItemProps.target}>\n              {defaultDom}\n            </Link>\n          );\n        }\n        return defaultDom;\n      }}\n      itemRender={(route, _, routes) => {\n        const { breadcrumbName, title, path } = route;\n        const label = title || breadcrumbName\n        const last = routes[routes.length - 1]\n        if (last) {\n          if (last.path === path || last.linkPath === path) {\n            return <span>{label}</span>;\n          }\n        }\n        return <Link to={path}>{label}</Link>;\n      }}\n      disableContentMargin\n      fixSiderbar\n      fixedHeader\n      {...runtimeConfig}\n      rightContentRender={\n        runtimeConfig.rightContentRender !== false &&\n        ((layoutProps) => {\n          const dom = getRightRenderContent({\n            runtimeConfig,\n            loading,\n            initialState,\n            setInitialState,\n          });\n          if (runtimeConfig.rightContentRender) {\n            return runtimeConfig.rightContentRender(layoutProps, dom, {\n              // BREAK CHANGE userConfig > runtimeConfig\n              userConfig,\n              runtimeConfig,\n              loading,\n              initialState,\n              setInitialState,\n            });\n          }\n          return dom;\n        })\n      }\n    >\n      <Exception\n        route={matchedRoute}\n        noFound={runtimeConfig?.noFound}\n        notFound={runtimeConfig?.notFound}\n        unAccessible={runtimeConfig?.unAccessible}\n        noAccessible={runtimeConfig?.noAccessible}\n      >\n        {runtimeConfig.childrenRender\n          ? runtimeConfig.childrenRender(<Outlet />, props)\n          : <Outlet />\n        }\n      </Exception>\n    </ProLayout>\n  );\n}\n", "// @ts-nocheck\n// This file is generated by Umi automatically\n// DO NOT CHANGE IT MANUALLY!\nimport React from 'react';\n\nconst LogoIcon: React.FC = () => {\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      width=\"32\"\n      height=\"32\"\n      viewBox=\"0 0 200 200\"\n    >\n      <defs>\n        <linearGradient\n          id=\"linearGradient-1\"\n          x1=\"62.102%\"\n          x2=\"108.197%\"\n          y1=\"0%\"\n          y2=\"37.864%\"\n        >\n          <stop offset=\"0%\" stopColor=\"#4285EB\"></stop>\n          <stop offset=\"100%\" stopColor=\"#2EC7FF\"></stop>\n        </linearGradient>\n        <linearGradient\n          id=\"linearGradient-2\"\n          x1=\"69.644%\"\n          x2=\"54.043%\"\n          y1=\"0%\"\n          y2=\"108.457%\"\n        >\n          <stop offset=\"0%\" stopColor=\"#29CDFF\"></stop>\n          <stop offset=\"37.86%\" stopColor=\"#148EFF\"></stop>\n          <stop offset=\"100%\" stopColor=\"#0A60FF\"></stop>\n        </linearGradient>\n        <linearGradient\n          id=\"linearGradient-3\"\n          x1=\"69.691%\"\n          x2=\"16.723%\"\n          y1=\"-12.974%\"\n          y2=\"117.391%\"\n        >\n          <stop offset=\"0%\" stopColor=\"#FA816E\"></stop>\n          <stop offset=\"41.473%\" stopColor=\"#F74A5C\"></stop>\n          <stop offset=\"100%\" stopColor=\"#F51D2C\"></stop>\n        </linearGradient>\n        <linearGradient\n          id=\"linearGradient-4\"\n          x1=\"68.128%\"\n          x2=\"30.44%\"\n          y1=\"-35.691%\"\n          y2=\"114.943%\"\n        >\n          <stop offset=\"0%\" stopColor=\"#FA8E7D\"></stop>\n          <stop offset=\"51.264%\" stopColor=\"#F74A5C\"></stop>\n          <stop offset=\"100%\" stopColor=\"#F51D2C\"></stop>\n        </linearGradient>\n      </defs>\n      <g fill=\"none\" fillRule=\"evenodd\" stroke=\"none\" strokeWidth=\"1\">\n        <g transform=\"translate(-20 -20)\">\n          <g transform=\"translate(20 20)\">\n            <g>\n              <g fillRule=\"nonzero\">\n                <g>\n                  <path\n                    fill=\"url(#linearGradient-1)\"\n                    d=\"M91.588 4.177L4.18 91.513a11.981 11.981 0 000 16.974l87.408 87.336a12.005 12.005 0 0016.989 0l36.648-36.618c4.209-4.205 4.209-11.023 0-15.228-4.208-4.205-11.031-4.205-15.24 0l-27.783 27.76c-1.17 1.169-2.945 1.169-4.114 0l-69.802-69.744c-1.17-1.169-1.17-2.942 0-4.11l69.802-69.745c1.17-1.169 2.944-1.169 4.114 0l27.783 27.76c4.209 4.205 11.032 4.205 15.24 0 4.209-4.205 4.209-11.022 0-15.227L108.581 4.056c-4.719-4.594-12.312-4.557-16.993.12z\"\n                  ></path>\n                  <path\n                    fill=\"url(#linearGradient-2)\"\n                    d=\"M91.588 4.177L4.18 91.513a11.981 11.981 0 000 16.974l87.408 87.336a12.005 12.005 0 0016.989 0l36.648-36.618c4.209-4.205 4.209-11.023 0-15.228-4.208-4.205-11.031-4.205-15.24 0l-27.783 27.76c-1.17 1.169-2.945 1.169-4.114 0l-69.802-69.744c-1.17-1.169-1.17-2.942 0-4.11l69.802-69.745c2.912-2.51 7.664-7.596 14.642-8.786 5.186-.883 10.855 1.062 17.009 5.837L108.58 4.056c-4.719-4.594-12.312-4.557-16.993.12z\"\n                  ></path>\n                </g>\n                <path\n                  fill=\"url(#linearGradient-3)\"\n                  d=\"M153.686 135.855c4.208 4.205 11.031 4.205 15.24 0l27.034-27.012c4.7-4.696 4.7-12.28 0-16.974l-27.27-27.15c-4.218-4.2-11.043-4.195-15.254.013-4.209 4.205-4.209 11.022 0 15.227l18.418 18.403c1.17 1.169 1.17 2.943 0 4.111l-18.168 18.154c-4.209 4.205-4.209 11.023 0 15.228z\"\n                ></path>\n              </g>\n              <ellipse\n                cx=\"100.519\"\n                cy=\"100.437\"\n                fill=\"url(#linearGradient-4)\"\n                rx=\"23.6\"\n                ry=\"23.581\"\n              ></ellipse>\n            </g>\n          </g>\n        </g>\n      </g>\n    </svg>\n  );\n};\n\nexport default LogoIcon;\n", "// @ts-nocheck\n// This file is generated by Um<PERSON> automatically\n// DO NOT CHANGE IT MANUALLY!\nimport React from 'react';\nimport { Avatar, version, Dropdown, Menu, Spin } from 'antd';\nimport { LogoutOutlined } from 'F:/Project/teamAuth/frontend/node_modules/@umijs/plugins/node_modules/@ant-design/icons';\n\nexport function getRightRenderContent (opts: {\n   runtimeConfig: any,\n   loading: boolean,\n   initialState: any,\n   setInitialState: any,\n }) {\n  if (opts.runtimeConfig.rightRender) {\n    return opts.runtimeConfig.rightRender(\n      opts.initialState,\n      opts.setInitialState,\n      opts.runtimeConfig,\n    );\n  }\n\n  const showAvatar = opts.initialState?.avatar || opts.initialState?.name || opts.runtimeConfig.logout;\n  const disableAvatarImg = opts.initialState?.avatar === false;\n  const nameClassName = disableAvatarImg ? 'umi-plugin-layout-name umi-plugin-layout-hide-avatar-img' : 'umi-plugin-layout-name';\n  const avatar =\n    showAvatar ? (\n      <span className=\"umi-plugin-layout-action\">\n        {!disableAvatarImg ?\n          (\n            <Avatar\n              size=\"small\"\n              className=\"umi-plugin-layout-avatar\"\n              src={\n                opts.initialState?.avatar ||\n                \"https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png\"\n              }\n              alt=\"avatar\"\n            />\n          ) : null}\n        <span className={nameClassName}>{opts.initialState?.name}</span>\n      </span>\n    ) : null;\n\n\n  if (opts.loading) {\n    return (\n      <div className=\"umi-plugin-layout-right\">\n        <Spin size=\"small\" style={ { marginLeft: 8, marginRight: 8 } } />\n      </div>\n    );\n  }\n\n  // 如果没有打开Locale，并且头像为空就取消掉这个返回的内容\n    if(!avatar) return null;\n\n  const langMenu = {\n    className: \"umi-plugin-layout-menu\",\n    selectedKeys: [],\n    items: [\n      {\n        key: \"logout\",\n        label: (\n          <>\n            <LogoutOutlined />\n            退出登录\n          </>\n        ),\n        onClick: () => {\n          opts?.runtimeConfig?.logout?.(opts.initialState);\n        },\n      },\n    ],\n  };\n  // antd@5 和  4.24 之后推荐使用 menu，性能更好\n  let dropdownProps;\n  if (version.startsWith(\"5.\") || version.startsWith(\"4.24.\")) {\n    dropdownProps = { menu: langMenu };\n  } else if (version.startsWith(\"3.\")) {\n    dropdownProps = {\n      overlay: (\n        <Menu>\n          {langMenu.items.map((item) => (\n            <Menu.Item key={item.key} onClick={item.onClick}>\n              {item.label}\n            </Menu.Item>\n          ))}\n        </Menu>\n      ),\n    };\n  } else { // 需要 antd 4.20.0 以上版本\n    dropdownProps = { overlay: <Menu {...langMenu} /> };\n  }\n\n\n\n  return (\n    <div className=\"umi-plugin-layout-right anticon\">\n      {opts.runtimeConfig.logout ? (\n        <Dropdown {...dropdownProps} overlayClassName=\"umi-plugin-layout-container\">\n          {avatar}\n        </Dropdown>\n      ) : (\n        avatar\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;AAAA,cAAc;AACd,8CAA8C;AAC9C,6BAA6B;;;;;4BAkC7B;;;eAAA;;;;;;;uEAjCkB;4BACmB;6BACN;;;;;;;;;AAE/B,MAAM,YAOD,CAAC;QAIH,cAEiB;WALlB,oBAAoB;IACnB,CAAC,MAAM,KAAK,IAAK,CAAA,MAAM,OAAO,IAAI,MAAM,QAAQ,AAAD,KAE/C,EAAA,eAAA,MAAM,KAAK,cAAX,mCAAA,aAAa,YAAY,KAAK,CAAA,MAAM,YAAY,IAAI,MAAM,YAAY,AAAD,KAErE,AAAC,CAAA,CAAC,MAAM,KAAK,MAAI,gBAAA,MAAM,KAAK,cAAX,oCAAA,cAAa,YAAY,CAAD,mBACxC,2BAAC,YAAM;QACL,QAAQ,MAAM,KAAK,GAAG,QAAQ;QAC9B,OAAO,MAAM,KAAK,GAAG,QAAQ;QAC7B,UAAU,MAAM,KAAK,GAAG,gBAAgB;QACxC,qBACE,2BAAC,YAAM;YAAC,MAAK;YAAU,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;sBAAM;;;;;;;;;;gBAM/D,gBAAgB;IAChB,MAAM,QAAQ;;KA1BV;IA6BN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpCf,cAAc;AACd,8CAA8C;AAC9C,6BAA6B;AAC7B,oDAAoD;AACpD,8BAA8B;;;;;;;;;;;;;;;4BAKvB;wEACwB;sCAGxB;;sEAEU;2EACK;oCACgB;oCACb;qCACa;;;;;;;;;;AAGtC,qCAAqC;AACrC,MAAM,eAAe,CAAC,QAAkB;IACtC,IAAI,OAAO,MAAM,KAAK,GACpB,OAAO,EAAE;IAGX,IAAI,YAAY,EAAE;IAClB,KAAK,MAAM,SAAS,OAAQ;QAC1B,MAAM,WAAW;YAAC,GAAG,KAAK;QAAC;QAC3B,IAAI,SAAS,QACX;YAAA,IAAI,MAAM,OAAO,CAAC,SAAS,MAAM,GAC/B,UAAU,IAAI,IAAI,aAAa,SAAS,MAAM,EAAE;QAClD,OACK;YACL,IAAI,MAAM,OAAO,CAAC,SAAS,QAAQ,GAAG;gBACpC,SAAS,QAAQ,GAAG,aAAa,SAAS,QAAQ,EAAE;gBACpD,SAAS,MAAM,GAAG,SAAS,QAAQ;YACrC;YACA,UAAU,IAAI,CAAC;QACjB;IACF;IAEA,OAAO;AACT;AAEA,oCAAoC;AACpC,MAAM,YAAY,CAAC;IACjB,IAAI,OAAO,MAAM,KAAK,GACpB,OAAO,EAAE;IAEX,OAAO,OAAO,GAAG,CAAC,CAAA;QAChB,wBAAwB;QACxB,MAAM,WAAW;YAAC,GAAG,KAAK;QAAA;QAC1B,IAAI,MAAM,UAAU,EAClB,SAAS,IAAI,GAAG,MAAM,UAAU;QAGlC,IAAI,MAAM,OAAO,CAAC,MAAM,MAAM,GAC5B,SAAS,MAAM,GAAG,UAAU,MAAM,MAAM;QAG1C,IAAI,MAAM,OAAO,CAAC,MAAM,QAAQ,GAC9B,SAAS,QAAQ,GAAG,UAAU,MAAM,QAAQ;QAG9C,OAAO;IACT;AACF;AAEe,qBAAC;;IACd,MAAM,WAAW,IAAA,gBAAW;IAC5B,MAAM,WAAW,IAAA,gBAAW;IAC5B,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG,IAAA,eAAU;IAClD,MAAM,cAAc,AAAC,qBAAQ,IAAI,IAAA,qBAAQ,EAAC,qBAAsB;QAC9D,cAAc;QACd,SAAS;QACT,iBAAiB;IACnB;IACA,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG;IACnD,MAAM,aAAa;QACnB,UAAU;QACV,YAAY;QACZ,gBAAgB;QAChB,UAAU;QACV,gBAAgB;QAChB,eAAe;QACf,eAAe;QACf,aAAa;QACb,SAAS;QACT,OAAO;QACP,QAAQ;QACR,eAAe;QACf,SAAS,CAAC;IACZ;IACA,MAAM,gBAAgB;IACpB,MAAM,gBAAgB,cAAc,YAAY,CAAC;QAC/C,KAAK;QACL,MAAM;QACN,cAAc;YACZ,GAAG,WAAW;QAChB;IACF;IAGA,6FAA6F;IAC7F,MAAM,YAAY,aAAa,aAAa,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,0BAA0B,CAAC;QAClG,OAAO,AAAC,CAAC,CAAC,MAAM,QAAQ,IAAI,MAAM,EAAE,KAAK,2BAA4B,CAAC,CAAC,MAAM,SAAS;IACxF;IACA,MAAM,CAAC,MAAM,GAAG,IAAA,mCAAqB,EAAC,UAAU;IAEhD,MAAM,eAAe,IAAA,cAAO,EAAC;YAAM,kBAAA,mBAAA;gBAAA,eAAA,IAAA,gBAAW,EAAC,MAAM,QAAQ,EAAE,SAAS,QAAQ,eAA7C,oCAAA,oBAAA,aAAgD,GAAG,cAAnD,yCAAA,mBAAA,uBAAA,2BAAA,uCAAA,iBAAyD,KAAK;OAAE;QAAC,SAAS,QAAQ;KAAC;IAEtH,qBACE,2BAAC,wBAAS;QACR,OAAO;QACP,UAAU;QACV,OAAO,WAAW,KAAK,IAAI;QAC3B,UAAS;QACT,YAAY;QACZ,mBAAmB,CAAC;YAClB,EAAE,eAAe;YACjB,EAAE,cAAc;YAChB,SAAS;QACX;QACA,eAAe,WAAW,aAAa,IAAI;QAC3C,MAAM;YAAE,QAAQ,WAAW,MAAM;QAAC;QAClC,MAAM,aAAI;QACV,gBAAgB,CAAC,eAAe;YAC9B,IAAI,cAAc,KAAK,IAAI,cAAc,QAAQ,EAC/C,OAAO;YAET,IAAI,cAAc,IAAI,IAAI,SAAS,QAAQ,KAAK,cAAc,IAAI,EAChE,OACE,gEAAgE;0BAChE,2BAAC,SAAI;gBAAC,IAAI,cAAc,IAAI,CAAC,OAAO,CAAC,MAAM;gBAAK,QAAQ,cAAc,MAAM;0BACzE;;;;;;YAIP,OAAO;QACT;QACA,YAAY,CAAC,OAAO,GAAG;YACrB,MAAM,EAAE,cAAc,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG;YACxC,MAAM,QAAQ,SAAS;YACvB,MAAM,OAAO,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;YACtC,IAAI,MAAM;gBACR,IAAI,KAAK,IAAI,KAAK,QAAQ,KAAK,QAAQ,KAAK,MAC1C,qBAAO,2BAAC;8BAAM;;;;;;YAElB;YACA,qBAAO,2BAAC,SAAI;gBAAC,IAAI;0BAAO;;;;;;QAC1B;QACA,oBAAoB;QACpB,WAAW;QACX,WAAW;QACV,GAAG,aAAa;QACjB,oBACE,cAAc,kBAAkB,KAAK,SACpC,CAAA,CAAC;YACA,MAAM,MAAM,IAAA,kCAAqB,EAAC;gBAChC;gBACA;gBACA;gBACA;YACF;YACA,IAAI,cAAc,kBAAkB,EAClC,OAAO,cAAc,kBAAkB,CAAC,aAAa,KAAK;gBACxD,0CAA0C;gBAC1C;gBACA;gBACA;gBACA;gBACA;YACF;YAEF,OAAO;QACT,CAAA;kBAGF,cAAA,2BAAC,kBAAS;YACR,OAAO;YACP,OAAO,EAAE,0BAAA,oCAAA,cAAe,OAAO;YAC/B,QAAQ,EAAE,0BAAA,oCAAA,cAAe,QAAQ;YACjC,YAAY,EAAE,0BAAA,oCAAA,cAAe,YAAY;YACzC,YAAY,EAAE,0BAAA,oCAAA,cAAe,YAAY;sBAExC,cAAc,cAAc,GACzB,cAAc,cAAc,eAAC,2BAAC,WAAM;;;;sBAAK,uBACzC,2BAAC,WAAM;;;;;;;;;;;;;;;AAKnB;;;QA3HmB,gBAAW;QACX,gBAAW;QACY,eAAU;QACjB,qBAAQ;QAmCzB,mCAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9GvC,cAAc;AACd,8CAA8C;AAC9C,6BAA6B;;;;;4BA2F7B;;;eAAA;;;;;;;uEA1FkB;;;;;;;;;AAElB,MAAM,WAAqB;IACzB,qBACE,2BAAC;QACC,OAAM;QACN,OAAM;QACN,QAAO;QACP,SAAQ;;0BAER,2BAAC;;kCACC,2BAAC;wBACC,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,IAAG;;0CAEH,2BAAC;gCAAK,QAAO;gCAAK,WAAU;;;;;;0CAC5B,2BAAC;gCAAK,QAAO;gCAAO,WAAU;;;;;;;;;;;;kCAEhC,2BAAC;wBACC,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,IAAG;;0CAEH,2BAAC;gCAAK,QAAO;gCAAK,WAAU;;;;;;0CAC5B,2BAAC;gCAAK,QAAO;gCAAS,WAAU;;;;;;0CAChC,2BAAC;gCAAK,QAAO;gCAAO,WAAU;;;;;;;;;;;;kCAEhC,2BAAC;wBACC,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,IAAG;;0CAEH,2BAAC;gCAAK,QAAO;gCAAK,WAAU;;;;;;0CAC5B,2BAAC;gCAAK,QAAO;gCAAU,WAAU;;;;;;0CACjC,2BAAC;gCAAK,QAAO;gCAAO,WAAU;;;;;;;;;;;;kCAEhC,2BAAC;wBACC,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,IAAG;;0CAEH,2BAAC;gCAAK,QAAO;gCAAK,WAAU;;;;;;0CAC5B,2BAAC;gCAAK,QAAO;gCAAU,WAAU;;;;;;0CACjC,2BAAC;gCAAK,QAAO;gCAAO,WAAU;;;;;;;;;;;;;;;;;;0BAGlC,2BAAC;gBAAE,MAAK;gBAAO,UAAS;gBAAU,QAAO;gBAAO,aAAY;0BAC1D,cAAA,2BAAC;oBAAE,WAAU;8BACX,cAAA,2BAAC;wBAAE,WAAU;kCACX,cAAA,2BAAC;;8CACC,2BAAC;oCAAE,UAAS;;sDACV,2BAAC;;8DACC,2BAAC;oDACC,MAAK;oDACL,GAAE;;;;;;8DAEJ,2BAAC;oDACC,MAAK;oDACL,GAAE;;;;;;;;;;;;sDAGN,2BAAC;4CACC,MAAK;4CACL,GAAE;;;;;;;;;;;;8CAGN,2BAAC;oCACC,IAAG;oCACH,IAAG;oCACH,MAAK;oCACL,IAAG;oCACH,IAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB;KAtFM;IAwFN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7Ff,cAAc;AACd,8CAA8C;AAC9C,6BAA6B;;;;;4BAKb;;;eAAA;;;;;;;uEAJE;6BACoC;8BACvB;;;;;;;;;AAExB,SAAS,sBAAuB,IAKrC;QASmB,oBAA6B,qBACvB,qBAWX,qBAMyB;IA1BvC,IAAI,KAAK,aAAa,CAAC,WAAW,EAChC,OAAO,KAAK,aAAa,CAAC,WAAW,CACnC,KAAK,YAAY,EACjB,KAAK,eAAe,EACpB,KAAK,aAAa;IAItB,MAAM,aAAa,EAAA,qBAAA,KAAK,YAAY,cAAjB,yCAAA,mBAAmB,MAAM,OAAI,sBAAA,KAAK,YAAY,cAAjB,0CAAA,oBAAmB,IAAI,KAAI,KAAK,aAAa,CAAC,MAAM;IACpG,MAAM,mBAAmB,EAAA,sBAAA,KAAK,YAAY,cAAjB,0CAAA,oBAAmB,MAAM,MAAK;IACvD,MAAM,gBAAgB,mBAAmB,6DAA6D;IACtG,MAAM,SACJ,2BACE,2BAAC;QAAK,WAAU;;YACb,CAAC,iCAEE,2BAAC,YAAM;gBACL,MAAK;gBACL,WAAU;gBACV,KACE,EAAA,sBAAA,KAAK,YAAY,cAAjB,0CAAA,oBAAmB,MAAM,KACzB;gBAEF,KAAI;;;;;uBAEJ;0BACN,2BAAC;gBAAK,WAAW;2BAAgB,sBAAA,KAAK,YAAY,cAAjB,0CAAA,oBAAmB,IAAI;;;;;;;;;;;eAExD;IAGN,IAAI,KAAK,OAAO,EACd,qBACE,2BAAC;QAAI,WAAU;kBACb,cAAA,2BAAC,UAAI;YAAC,MAAK;YAAQ,OAAQ;gBAAE,YAAY;gBAAG,aAAa;YAAE;;;;;;;;;;;IAKjE,iCAAiC;IAC/B,IAAG,CAAC,QAAQ,OAAO;IAErB,MAAM,WAAW;QACf,WAAW;QACX,cAAc,EAAE;QAChB,OAAO;YACL;gBACE,KAAK;gBACL,qBACE;;sCACE,2BAAC,qBAAc;;;;;wBAAG;;;gBAItB,SAAS;wBACP,4BAAA;oBAAA,iBAAA,oBAAA,sBAAA,KAAM,aAAa,cAAnB,mCAAA,6BAAA,oBAAqB,MAAM,cAA3B,yCAAA,gCAAA,qBAA8B,KAAK,YAAY;gBACjD;YACF;SACD;IACH;IACA,kCAAkC;IAClC,IAAI;IACJ,IAAI,aAAO,CAAC,UAAU,CAAC,SAAS,aAAO,CAAC,UAAU,CAAC,UACjD,gBAAgB;QAAE,MAAM;IAAS;SAC5B,IAAI,aAAO,CAAC,UAAU,CAAC,OAC5B,gBAAgB;QACd,uBACE,2BAAC,UAAI;sBACF,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,qBACnB,2BAAC,UAAI,CAAC,IAAI;oBAAgB,SAAS,KAAK,OAAO;8BAC5C,KAAK,KAAK;mBADG,KAAK,GAAG;;;;;;;;;;IAMhC;SAEA,gBAAgB;QAAE,uBAAS,2BAAC,UAAI;YAAE,GAAG,QAAQ;;;;;;IAAK;IAKpD,qBACE,2BAAC;QAAI,WAAU;kBACZ,KAAK,aAAa,CAAC,MAAM,iBACxB,2BAAC,cAAQ;YAAE,GAAG,aAAa;YAAE,kBAAiB;sBAC3C;;;;;mBAGH;;;;;;AAIR"}