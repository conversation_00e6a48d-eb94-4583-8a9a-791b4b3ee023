package com.teammanage.dto.request;

import jakarta.validation.constraints.Size;


/**
 * 更新用户资料请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

public class UpdateUserProfileRequest {

    /**
     * 用户名
     */
    @Size(max = 100, message = "用户名长度不能超过100字符")
    private String name;

    /**
     * 当前密码（修改密码时必填）
     */
    private String currentPassword;

    /**
     * 新密码
     */
    @Size(min = 8, message = "密码长度至少8位")
    private String newPassword;

    // 手动添加getter/setter方法
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getCurrentPassword() { return currentPassword; }
    public void setCurrentPassword(String currentPassword) { this.currentPassword = currentPassword; }

    public String getNewPassword() { return newPassword; }
    public void setNewPassword(String newPassword) { this.newPassword = newPassword; }

}
