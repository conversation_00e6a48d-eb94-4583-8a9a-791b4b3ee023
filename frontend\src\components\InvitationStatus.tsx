/**
 * 邀请状态显示组件
 */

import React from 'react';
import { Tag } from 'antd';
import {
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  StopOutlined,
} from '@ant-design/icons';
import { InvitationStatus } from '@/types/api';

interface InvitationStatusProps {
  status: InvitationStatus;
  isExpired?: boolean;
}

/**
 * 邀请状态组件
 */
const InvitationStatusComponent: React.FC<InvitationStatusProps> = ({ 
  status, 
  isExpired = false 
}) => {
  // 如果已过期，优先显示过期状态
  if (isExpired && status === InvitationStatus.PENDING) {
    return (
      <Tag icon={<ExclamationCircleOutlined />} color="orange">
        已过期
      </Tag>
    );
  }

  switch (status) {
    case InvitationStatus.PENDING:
      return (
        <Tag icon={<ClockCircleOutlined />} color="blue">
          待确认
        </Tag>
      );
    case InvitationStatus.ACCEPTED:
      return (
        <Tag icon={<CheckCircleOutlined />} color="green">
          已接受
        </Tag>
      );
    case InvitationStatus.REJECTED:
      return (
        <Tag icon={<CloseCircleOutlined />} color="red">
          已拒绝
        </Tag>
      );
    case InvitationStatus.EXPIRED:
      return (
        <Tag icon={<ExclamationCircleOutlined />} color="orange">
          已过期
        </Tag>
      );
    case InvitationStatus.CANCELLED:
      return (
        <Tag icon={<StopOutlined />} color="default">
          已取消
        </Tag>
      );
    default:
      return (
        <Tag color="default">
          未知状态
        </Tag>
      );
  }
};

export default InvitationStatusComponent;
