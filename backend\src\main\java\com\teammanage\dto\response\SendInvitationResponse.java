package com.teammanage.dto.response;

import java.util.List;

/**
 * 发送邀请响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class SendInvitationResponse {

    /**
     * 成功创建的邀请列表
     */
    private List<InvitationResult> invitations;

    /**
     * 总邀请数量
     */
    private Integer totalCount;

    /**
     * 成功数量
     */
    private Integer successCount;

    /**
     * 失败数量
     */
    private Integer failureCount;

    /**
     * 邀请结果详情
     */
    public static class InvitationResult {
        
        /**
         * 邀请ID
         */
        private Long invitationId;
        
        /**
         * 被邀请人邮箱
         */
        private String email;
        
        /**
         * 邀请链接
         */
        private String invitationLink;
        
        /**
         * 是否成功
         */
        private Boolean success;
        
        /**
         * 错误消息（如果失败）
         */
        private String errorMessage;

        // Getter and Setter methods
        public Long getInvitationId() { return invitationId; }
        public void setInvitationId(Long invitationId) { this.invitationId = invitationId; }

        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }

        public String getInvitationLink() { return invitationLink; }
        public void setInvitationLink(String invitationLink) { this.invitationLink = invitationLink; }

        public Boolean getSuccess() { return success; }
        public void setSuccess(Boolean success) { this.success = success; }

        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }

    // Getter and Setter methods
    public List<InvitationResult> getInvitations() { return invitations; }
    public void setInvitations(List<InvitationResult> invitations) { this.invitations = invitations; }

    public Integer getTotalCount() { return totalCount; }
    public void setTotalCount(Integer totalCount) { this.totalCount = totalCount; }

    public Integer getSuccessCount() { return successCount; }
    public void setSuccessCount(Integer successCount) { this.successCount = successCount; }

    public Integer getFailureCount() { return failureCount; }
    public void setFailureCount(Integer failureCount) { this.failureCount = failureCount; }
}
