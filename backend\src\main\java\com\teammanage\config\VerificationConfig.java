package com.teammanage.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 验证码配置类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@ConfigurationProperties(prefix = "app.verification")
public class VerificationConfig {

    /**
     * 验证码长度
     */
    private int codeLength = 6;

    /**
     * 验证码过期时间（分钟）
     */
    private int expireMinutes = 5;

    /**
     * 最大验证尝试次数
     */
    private int maxAttempts = 3;

    /**
     * 重发间隔（秒）
     */
    private int resendInterval = 60;

    // Getter and Setter methods
    public int getCodeLength() { return codeLength; }
    public void setCodeLength(int codeLength) { this.codeLength = codeLength; }

    public int getExpireMinutes() { return expireMinutes; }
    public void setExpireMinutes(int expireMinutes) { this.expireMinutes = expireMinutes; }

    public int getMaxAttempts() { return maxAttempts; }
    public void setMaxAttempts(int maxAttempts) { this.maxAttempts = maxAttempts; }

    public int getResendInterval() { return resendInterval; }
    public void setResendInterval(int resendInterval) { this.resendInterval = resendInterval; }
}
