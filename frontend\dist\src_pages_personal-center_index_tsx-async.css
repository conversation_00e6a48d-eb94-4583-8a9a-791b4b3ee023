.personalInfoContent-j3z_Qmb_ {
  padding: 8px 0;
}
.userInfoSection-ge4WE5-U {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  margin-bottom: 20px;
}
.userBasicInfo-hoOMII6q {
  flex: 1 1;
  min-width: 0;
}
.userName-MACou0NP {
  margin: 0 0 8px 0 !important;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  line-height: 1.2;
  cursor: pointer;
  transition: all 0.2s ease;
}
.userName-MACou0NP:hover {
  color: #1890ff;
}
.loginInfoSection-p7RLKcXS {
  margin-top: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}
.loginInfoSection-p7RLKcXS:hover {
  background: #f5f5f5;
  border-color: #e0e0e0;
}
.loginInfoItem-74tohz9t {
  display: flex;
  align-items: center;
  gap: 8px;
}
.decorativeCircle1-HEEi2j9f {
  position: absolute;
  top: -50px;
  right: -50px;
  width: 120px;
  height: 120px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  z-index: 1;
  -webkit-animation: float 6s ease-in-out infinite;
  animation: float-DAEgOouD 6s ease-in-out infinite;
}
.decorativeCircle2-t8HUdx07 {
  position: absolute;
  bottom: -30px;
  left: -30px;
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  z-index: 1;
  animation: float-DAEgOouD 8s ease-in-out infinite reverse;
}
.contentArea-_n2x4BDb {
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  margin: 2px;
  border-radius: 14px;
  padding: 24px;
  transition: all 0.3s ease;
}
.titleBar-GUpHpaO- {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}
.title-OR5wN18v {
  margin: 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 600;
}
.settingsButton-mrJVLRGb {
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  transition: all 0.3s ease;
}
.settingsButton-mrJVLRGb:hover {
  background: rgba(102, 126, 234, 0.1) !important;
  transform: scale(1.1);
}
.avatarContainer-DSdWVJ9f {
  position: relative;
}
.avatar-_T1D7bf2 {
  background: linear-gradient(135deg, #667eea, #764ba2);
  font-size: 28px;
  font-weight: 600;
  border: 4px solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
}
.avatar-_T1D7bf2:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 32px rgba(102, 126, 234, 0.3);
}
.onlineIndicator-dZLd46X5 {
  position: absolute;
  bottom: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  background: #52c41a;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  -webkit-animation: pulse 2s infinite;
  animation: pulse-DmGLSG7j 2s infinite;
}
@keyframes float-DAEgOouD {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}
@keyframes pulse-DmGLSG7j {
  0% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(82, 196, 26, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}
@keyframes fadeInUp-LmQACtmh {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@media (max-width: 768px) {
  .contentArea-_n2x4BDb {
    padding: 16px;
  }
  .titleBar-GUpHpaO- {
    margin-bottom: 16px;
    padding-bottom: 12px;
  }
  .avatar-_T1D7bf2 {
    font-size: 24px;
  }
  .contactCard-iYaWi3ca {
    padding: 6px 10px;
  }
  .additionalInfo-SWj22vN7 {
    margin-top: 10px;
    padding: 12px;
  }
}
@media (max-width: 576px) {
  .personalInfoCard-GMTUvuoL {
    margin-bottom: 12px;
    border-radius: 12px;
  }
  .contentArea-_n2x4BDb {
    padding: 12px;
    border-radius: 10px;
  }
  .decorativeCircle1-HEEi2j9f {
    width: 80px;
    height: 80px;
    top: -40px;
    right: -40px;
  }
  .decorativeCircle2-t8HUdx07 {
    width: 60px;
    height: 60px;
    bottom: -30px;
    left: -30px;
  }
}
.loadingContainer-lZKULeb8 {
  -webkit-animation: fadeInUp 0.6s ease-out;
  animation: fadeInUp-LmQACtmh 0.6s ease-out;
}
.statsCard-RbylBLDK:active {
  transform: translateY(-1px) scale(0.98);
  transition: all 0.1s ease;
}
.contactCard-iYaWi3ca:active {
  transform: translateX(2px) scale(0.98);
  transition: all 0.1s ease;
}
.settingsButton-mrJVLRGb:active {
  transform: scale(0.95);
  transition: all 0.1s ease;
}
.avatar-_T1D7bf2:active {
  transform: scale(1.02);
  transition: all 0.1s ease;
}
.skeletonCard-fH7lshhh {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  -webkit-animation: shimmer 1.5s infinite;
  animation: shimmer-lCzub6k8 1.5s infinite;
  border-radius: 12px;
  height: 80px;
}
@keyframes shimmer-lCzub6k8 {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
.successAnimation-UGiJBscn {
  -webkit-animation: successPulse 0.6s ease-out;
  animation: successPulse-xvCPq0dm 0.6s ease-out;
}
@keyframes successPulse-xvCPq0dm {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(82, 196, 26, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}
.errorAnimation-SVfCef80 {
  -webkit-animation: errorShake 0.6s ease-out;
  animation: errorShake-RkVI4HkN 0.6s ease-out;
}
@keyframes errorShake-RkVI4HkN {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}
.fadeInDelay1-kMZ2-Q5v {
  -webkit-animation: fadeInUp 0.6s ease-out 0.1s both;
  animation: fadeInUp-LmQACtmh 0.6s ease-out 0.1s both;
}
.fadeInDelay2-ZracY-tQ {
  -webkit-animation: fadeInUp 0.6s ease-out 0.2s both;
  animation: fadeInUp-LmQACtmh 0.6s ease-out 0.2s both;
}
.fadeInDelay3-nbwSZP8A {
  -webkit-animation: fadeInUp 0.6s ease-out 0.3s both;
  animation: fadeInUp-LmQACtmh 0.6s ease-out 0.3s both;
}
.fadeInDelay4-TJToLvzm {
  -webkit-animation: fadeInUp 0.6s ease-out 0.4s both;
  animation: fadeInUp-LmQACtmh 0.6s ease-out 0.4s both;
}
/*# sourceMappingURL=src_pages_personal-center_index_tsx-async.css.map*/