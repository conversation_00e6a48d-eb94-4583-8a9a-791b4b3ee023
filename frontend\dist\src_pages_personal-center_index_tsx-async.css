.popoverContent-EhVEGF9V {
  padding: 12px 0;
  min-width: 300px;
  max-width: 350px;
}
.popoverTitle-gZ0PK1Dm {
  padding: 8px 0 12px 0;
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}
.infoItem-TRvgqB7k {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 10px 0;
  transition: all 0.2s ease;
  border-radius: 6px;
  margin: 0 -8px;
  position: relative;
}
.infoItem-TRvgqB7k:hover {
  background: rgba(24, 144, 255, 0.04);
  padding: 12px 8px;
}
.infoItem-TRvgqB7k.email-YepxYkkf:hover {
  background: rgba(24, 144, 255, 0.04);
}
.infoItem-TRvgqB7k.phone-EFJPU7Vm:hover {
  background: rgba(82, 196, 26, 0.04);
}
.infoItem-TRvgqB7k.register-NbpyKnYq:hover {
  background: rgba(114, 46, 209, 0.04);
}
.infoItem-TRvgqB7k.lastLogin-_0fqwZFk:hover {
  background: rgba(250, 140, 22, 0.04);
}
.infoItem-TRvgqB7k.team-iiXX2L4w:hover {
  background: rgba(19, 194, 194, 0.04);
}
.iconWrapper-HCDP4HDG {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border: 1px solid rgba(59, 130, 246, 0.1);
  flex-shrink: 0;
  margin-top: 1px;
  transition: all 0.2s ease;
}
.icon-z1rS9q3h {
  font-size: 13px;
  font-weight: 500;
}
.infoContent-AvzO53H- {
  flex: 1 1;
  min-width: 0;
}
.label-fY2ApLsi {
  display: block;
  font-size: 12px;
  line-height: 1.3;
  margin-bottom: 4px;
  color: #8c8c8c;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
.value-ClCU1LzT {
  display: block;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
  color: #262626;
  word-break: break-all;
}
.trigger-vd13A828 {
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 6px;
  padding: 4px 6px;
  margin: -4px -6px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.trigger-vd13A828:hover {
  background: rgba(24, 144, 255, 0.06);
  transform: scale(1.05);
}
.questionIcon-GFk04_0Q {
  font-size: 18px;
  color: #8c8c8c;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 4px;
  border-radius: 50%;
  background: transparent;
}
.questionIcon-GFk04_0Q:hover {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.08);
  transform: scale(1.1);
}
.settingIcon-JoqAnTNp {
  font-size: 18px;
  color: #8c8c8c;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 4px;
  border-radius: 50%;
  background: transparent;
}
.settingIcon-JoqAnTNp:hover {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.08);
  transform: scale(1.1);
}
@media (max-width: 768px) {
  .popoverContent-EhVEGF9V {
    min-width: 280px;
    max-width: 320px;
  }
  .infoItem-TRvgqB7k {
    gap: 10px;
    padding: 8px 0;
  }
  .iconWrapper-HCDP4HDG {
    width: 24px;
    height: 24px;
  }
  .icon-z1rS9q3h {
    font-size: 12px;
  }
  .label-fY2ApLsi {
    font-size: 11px;
  }
  .value-ClCU1LzT {
    font-size: 13px;
  }
}
@media (max-width: 576px) {
  .popoverContent-EhVEGF9V {
    min-width: 260px;
    max-width: 300px;
  }
  .popoverTitle-gZ0PK1Dm {
    font-size: 13px;
    padding: 6px 0 10px 0;
    margin-bottom: 10px;
  }
  .infoItem-TRvgqB7k {
    gap: 8px;
    padding: 6px 0;
  }
  .iconWrapper-HCDP4HDG {
    width: 22px;
    height: 22px;
  }
  .icon-z1rS9q3h {
    font-size: 11px;
  }
  .label-fY2ApLsi {
    font-size: 10px;
    margin-bottom: 2px;
  }
  .value-ClCU1LzT {
    font-size: 12px;
  }
}
@keyframes fadeIn-Bxe6iemy {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.popoverContent-EhVEGF9V {
  -webkit-animation: fadeIn 0.2s ease-out;
  animation: fadeIn-Bxe6iemy 0.2s ease-out;
}
.divider-kogfDmru {
  margin: 8px 0;
  border-color: #f0f0f0;
}
.value-ClCU1LzT .ant-typography-copy {
  color: #8c8c8c;
  margin-left: 4px;
  opacity: 0.7;
  transition: all 0.2s ease;
}
.value-ClCU1LzT:hover .ant-typography-copy {
  opacity: 1;
  color: #1890ff;
}
/*# sourceMappingURL=src_pages_personal-center_index_tsx-async.css.map*/