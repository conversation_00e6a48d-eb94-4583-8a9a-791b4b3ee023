package com.teammanage.context;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.teammanage.enums.TeamRole;

/**
 * 团队上下文管理器
 * 使用ThreadLocal管理当前线程的团队上下文
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class TeamContextHolder {

    private static final Logger log = LoggerFactory.getLogger(TeamContextHolder.class);

    private static final ThreadLocal<TeamContext> contextHolder = new ThreadLocal<>();

    /**
     * 设置团队上下文
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @param isCreator 是否为创建者
     */
    public static void setTeamContext(Long teamId, Long userId, boolean isCreator) {
        TeamContext context = TeamContext.builder()
                .teamId(teamId)
                .userId(userId)
                .isCreator(isCreator)
                .build();
        contextHolder.set(context);
        log.debug("设置团队上下文: teamId={}, userId={}, isCreator={}", teamId, userId, isCreator);
    }

    /**
     * 获取团队上下文
     * 
     * @return 团队上下文
     * @throws SecurityException 如果团队上下文未设置
     */
    public static TeamContext getTeamContext() {
        TeamContext context = contextHolder.get();
        if (context == null) {
            throw new SecurityException("团队上下文未设置，请确保在团队环境中操作");
        }
        return context;
    }

    /**
     * 获取当前团队ID
     * 
     * @return 团队ID
     */
    public static Long getCurrentTeamId() {
        return getTeamContext().getTeamId();
    }

    /**
     * 获取当前用户ID
     * 
     * @return 用户ID
     */
    public static Long getCurrentUserId() {
        return getTeamContext().getUserId();
    }

    /**
     * 检查当前用户是否为团队创建者
     *
     * @return 是否为创建者
     * @deprecated 使用 getCurrentUserRole() 方法替代
     */
    @Deprecated
    public static boolean isCurrentUserCreator() {
        return getTeamContext().isCreator();
    }

    /**
     * 获取当前用户的团队角色
     *
     * @return 团队角色
     */
    public static TeamRole getCurrentUserRole() {
        return TeamRole.fromIsCreator(getTeamContext().isCreator());
    }

    /**
     * 检查是否有团队上下文
     * 
     * @return 是否有团队上下文
     */
    public static boolean hasTeamContext() {
        return contextHolder.get() != null;
    }

    /**
     * 清理团队上下文
     */
    public static void clear() {
        contextHolder.remove();
        log.debug("清理团队上下文");
    }

    /**
     * 团队上下文数据
     */
    public static class TeamContext {
        /**
         * 团队ID
         */
        private Long teamId;

        /**
         * 用户ID
         */
        private Long userId;

        /**
         * 是否为创建者
         */
        private boolean isCreator;

        /**
         * 获取当前用户的团队角色
         *
         * @return 团队角色
         */
        public TeamRole getRole() {
            return TeamRole.fromIsCreator(isCreator);
        }

        /**
         * 检查是否可以管理团队
         *
         * @deprecated 使用 getRole().canManageTeam() 替代
         */
        @Deprecated
        public boolean canManageTeam() {
            return getRole().canManageTeam();
        }

        /**
         * 检查是否可以管理成员
         *
         * @deprecated 使用 getRole().canManageMembers() 替代
         */
        @Deprecated
        public boolean canManageMembers() {
            return getRole().canManageMembers();
        }

        /**
         * 检查是否可以访问数据
         *
         * @deprecated 使用 getRole().canAccessData() 替代
         */
        @Deprecated
        public boolean canAccessData() {
            return getRole().canAccessData();
        }

        // 构造函数
        public TeamContext() {}

        public TeamContext(Long teamId, Long userId, boolean isCreator) {
            this.teamId = teamId;
            this.userId = userId;
            this.isCreator = isCreator;
        }

        // Getter and Setter methods
        public Long getTeamId() { return teamId; }
        public void setTeamId(Long teamId) { this.teamId = teamId; }

        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }

        public boolean isCreator() { return isCreator; }
        public void setCreator(boolean isCreator) { this.isCreator = isCreator; }

        // Builder模式
        public static TeamContextBuilder builder() {
            return new TeamContextBuilder();
        }

        public static class TeamContextBuilder {
            private Long teamId;
            private Long userId;
            private boolean isCreator;

            public TeamContextBuilder teamId(Long teamId) {
                this.teamId = teamId;
                return this;
            }

            public TeamContextBuilder userId(Long userId) {
                this.userId = userId;
                return this;
            }

            public TeamContextBuilder isCreator(boolean isCreator) {
                this.isCreator = isCreator;
                return this;
            }

            public TeamContext build() {
                return new TeamContext(teamId, userId, isCreator);
            }
        }
    }

}
