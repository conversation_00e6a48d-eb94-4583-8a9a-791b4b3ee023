package com.teammanage.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Objects;

/**
 * 团队实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

@TableName("team")
public class Team extends BaseEntity {

    /**
     * 团队名称
     */
    private String name;

    /**
     * 团队描述
     */
    private String description;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 删除标记
     */
    @TableLogic
    private Boolean isDeleted;

    // 手动添加getter/setter方法
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public Long getCreatedBy() { return createdBy; }
    public void setCreatedBy(Long createdBy) { this.createdBy = createdBy; }

    public Boolean getIsDeleted() { return isDeleted; }
    public void setIsDeleted(Boolean isDeleted) { this.isDeleted = isDeleted; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        Team team = (Team) o;
        return Objects.equals(name, team.name) &&
               Objects.equals(description, team.description) &&
               Objects.equals(createdBy, team.createdBy) &&
               Objects.equals(isDeleted, team.isDeleted);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), name, description, createdBy, isDeleted);
    }

}
