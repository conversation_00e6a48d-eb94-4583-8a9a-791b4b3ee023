import { useModel, history } from '@umijs/max';
import {
  Form,
  Input,
  Button,
  Typography,
  Space,
  Divider,
  message,
  Modal,
  Tag,
  Flex,
  Popconfirm,
  Dropdown,
} from 'antd';
import { ProCard, ModalForm, ProList } from '@ant-design/pro-components';
import {
  TeamOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  LogoutOutlined,
  MoreOutlined,
} from '@ant-design/icons';
import React, { useState, useEffect } from 'react';
import { TeamService, AuthService } from '@/services';
import type { CreateTeamRequest, TeamDetailResponse } from '@/types/api';

const { Title, Text } = Typography;
const { TextArea } = Input;

/**
 * 团队管理设置卡片组件
 *
 * 提供团队相关的管理功能，包括：
 * - 创建新团队
 * - 查看用户的团队列表
 * - 团队快速操作
 * - 团队设置入口
 *
 * 功能特点：
 * - 创建团队模态框
 * - 团队列表展示
 * - 快速导航到团队管理
 * - 响应式设计
 */
const TeamManagementCard: React.FC = () => {
  const [form] = Form.useForm();
  const [editForm] = Form.useForm();
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [createLoading, setCreateLoading] = useState(false);
  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);
  const [teamsLoading, setTeamsLoading] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingTeam, setEditingTeam] = useState<TeamDetailResponse | null>(null);
  const [editLoading, setEditLoading] = useState(false);
  const [operationLoading, setOperationLoading] = useState<{[key: number]: boolean}>({});
  const { initialState, setInitialState } = useModel('@@initialState');

  /**
   * 获取用户团队列表
   *
   * 在组件挂载时获取用户的团队列表
   */
  useEffect(() => {
    if (initialState?.currentUser) {
      fetchTeams();
    }
  }, [initialState?.currentUser]);

  /**
   * 获取团队列表数据
   */
  const fetchTeams = async () => {
    setTeamsLoading(true);
    try {
      const teamsData = await TeamService.getUserTeamsWithStats();
      setTeams(teamsData);
    } catch (error) {
      console.error('获取团队列表失败:', error);
    } finally {
      setTeamsLoading(false);
    }
  };

  /**
   * 创建团队处理函数
   *
   * 处理团队创建的完整流程：
   * 1. 表单验证
   * 2. API调用
   * 3. 更新团队列表
   * 4. 关闭模态框
   * 5. 用户反馈
   */
  const handleCreateTeam = async (values: CreateTeamRequest) => {
    setCreateLoading(true);
    try {
      await TeamService.createTeam(values);

      // 重新获取团队列表
      await fetchTeams();

      // 关闭模态框并重置表单
      setCreateModalVisible(false);
      form.resetFields();

      message.success('团队创建成功！');
    } catch (error) {
      // 错误处理由响应拦截器统一处理
      console.error('创建团队失败:', error);
    } finally {
      setCreateLoading(false);
    }
  };

  /**
   * 编辑团队名称
   */
  const handleEditTeam = (team: TeamDetailResponse) => {
    setEditingTeam(team);
    editForm.setFieldsValue({
      name: team.name,
      description: team.description,
    });
    setEditModalVisible(true);
  };

  /**
   * 保存团队编辑
   */
  const handleSaveTeamEdit = async (values: any) => {
    if (!editingTeam) return;

    setEditLoading(true);
    try {
      // 先切换到目标团队
      await AuthService.selectTeam({ teamId: editingTeam.id });

      // 更新团队信息
      await TeamService.updateCurrentTeam({
        name: values.name,
        description: values.description,
      });

      // 重新获取团队列表
      await fetchTeams();

      // 关闭模态框
      setEditModalVisible(false);
      setEditingTeam(null);
      editForm.resetFields();

      message.success('团队信息更新成功！');
    } catch (error) {
      console.error('更新团队失败:', error);
      message.error('更新团队失败');
    } finally {
      setEditLoading(false);
    }
  };

  /**
   * 删除团队
   */
  const handleDeleteTeam = async (team: TeamDetailResponse) => {
    setOperationLoading(prev => ({ ...prev, [team.id]: true }));
    try {
      await TeamService.deleteTeam(team.id);

      // 重新获取团队列表
      await fetchTeams();

      message.success('团队删除成功！');
    } catch (error) {
      console.error('删除团队失败:', error);
      message.error('删除团队失败');
    } finally {
      setOperationLoading(prev => ({ ...prev, [team.id]: false }));
    }
  };

  /**
   * 退出团队
   */
  const handleLeaveTeam = async (team: TeamDetailResponse) => {
    setOperationLoading(prev => ({ ...prev, [team.id]: true }));
    try {
      await TeamService.leaveTeam();

      // 重新获取团队列表
      await fetchTeams();

      // 更新全局状态，清除当前团队
      if (setInitialState) {
        await setInitialState((prevState) => ({
          ...prevState,
          currentTeam: undefined,
        }));
      }

      message.success('已退出团队！');
    } catch (error) {
      console.error('退出团队失败:', error);
      message.error('退出团队失败');
    } finally {
      setOperationLoading(prev => ({ ...prev, [team.id]: false }));
    }
  };



  return (
    <>
      <ProCard
        className="dashboard-card"
        style={{
          borderRadius: 16,
          boxShadow: '0 6px 20px rgba(0,0,0,0.08)',
          border: 'none',
          background: 'linear-gradient(145deg, #ffffff, #f8faff)',
          height: '100%',
        }}
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
            <TeamOutlined
              style={{
                fontSize: '24px',
                color: '#1890ff',
                padding: '8px',
                backgroundColor: '#e6f7ff',
                borderRadius: '8px',
              }}
            />
            <div>
              <Title
                level={4}
                style={{
                  margin: 0,
                  background: 'linear-gradient(135deg, #1890ff, #722ed1)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  fontWeight: 600,
                }}
              >
                团队管理
              </Title>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                创建和管理您的团队
              </Text>
            </div>
          </div>
        }
      >
        {/* 创建团队按钮 */}
        <div style={{ marginBottom: 24 }}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
            size="large"
            block
            style={{
              borderRadius: 8,
              background: 'linear-gradient(135deg, #1890ff, #722ed1)',
              border: 'none',
              boxShadow: '0 2px 8px rgba(24, 144, 255, 0.3)',
              height: '48px',
              fontSize: '16px',
              fontWeight: 600,
            }}
          >
            创建新团队
          </Button>
        </div>

        <Divider style={{ margin: '24px 0' }}>
          <Text type="secondary">我的团队</Text>
        </Divider>

        {/* 团队列表 */}
        <ProList
          loading={teamsLoading}
          dataSource={teams} // 显示所有团队
          renderItem={(team) => {
            const getTeamActions = (team: TeamDetailResponse) => {
              if (team.isCreator) {
                return [
                  {
                    key: 'edit',
                    label: '编辑团队名称',
                    icon: <EditOutlined />,
                    onClick: () => handleEditTeam(team),
                  },
                  {
                    key: 'delete',
                    label: (
                      <Popconfirm
                        title="删除团队"
                        description={`确定要删除团队"${team.name}"吗？此操作不可恢复！`}
                        onConfirm={() => handleDeleteTeam(team)}
                        okText="确定删除"
                        cancelText="取消"
                        okButtonProps={{ danger: true }}
                      >
                        <span style={{ color: '#ff4d4f' }}>删除团队</span>
                      </Popconfirm>
                    ),
                    icon: <DeleteOutlined />,
                    danger: true,
                  },
                ];
              } else {
                return [
                  {
                    key: 'leave',
                    label: (
                      <Popconfirm
                        title="退出团队"
                        description={`确定要退出团队"${team.name}"吗？`}
                        onConfirm={() => handleLeaveTeam(team)}
                        okText="确定退出"
                        cancelText="取消"
                        okButtonProps={{ danger: true }}
                      >
                        <span style={{ color: '#ff4d4f' }}>退出团队</span>
                      </Popconfirm>
                    ),
                    icon: <LogoutOutlined />,
                    danger: true,
                  },
                ];
              }
            };

            return (
              <div
                style={{
                  padding: '12px 0',
                  borderBottom: '1px solid #f0f0f0',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
                  <div style={{ marginRight: 16 }}>
                    <div
                      style={{
                        width: 40,
                        height: 40,
                        borderRadius: 8,
                        background: 'linear-gradient(135deg, #1890ff, #722ed1)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        fontWeight: 'bold',
                        fontSize: '16px',
                      }}
                    >
                      {team.name.charAt(0).toUpperCase()}
                    </div>
                  </div>
                  <div style={{ flex: 1 }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                      <Text strong style={{ fontSize: '14px' }}>
                        {team.name}
                      </Text>
                      {team.isCreator && (
                        <Tag color="blue" style={{ fontSize: '10px' }}>
                          创建者
                        </Tag>
                      )}
                    </div>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {team.memberCount} 名成员
                    </Text>
                  </div>
                </div>
                <Dropdown
                  menu={{
                    items: getTeamActions(team),
                  }}
                  trigger={['click']}
                  placement="bottomRight"
                >
                  <Button
                    type="text"
                    icon={<MoreOutlined />}
                    loading={operationLoading[team.id]}
                    style={{ fontSize: '16px' }}
                  />
                </Dropdown>
              </div>
            );
          }}
          locale={{ emptyText: '暂无团队，创建您的第一个团队吧！' }}
        />


      </ProCard>

      {/* 编辑团队模态框 */}
      <ModalForm
        title="编辑团队信息"
        open={editModalVisible}
        onOpenChange={(visible) => {
          if (!visible) {
            setEditModalVisible(false);
            setEditingTeam(null);
            editForm.resetFields();
          }
        }}
        form={editForm}
        layout="vertical"
        onFinish={handleSaveTeamEdit}
        autoComplete="off"
        width={500}
        modalProps={{ style: { top: 100 } }}
        submitter={{
          searchConfig: {
            submitText: '保存修改',
            resetText: '取消',
          },
          submitButtonProps: {
            loading: editLoading,
            icon: <EditOutlined />,
          },
          onReset: () => {
            setEditModalVisible(false);
            setEditingTeam(null);
            editForm.resetFields();
          },
        }}
      >
        <Form.Item
          label="团队名称"
          name="name"
          rules={[
            { required: true, message: '请输入团队名称！' },
            { max: 100, message: '团队名称长度不能超过100字符！' },
            { min: 2, message: '团队名称至少需要2个字符！' },
          ]}
        >
          <Input placeholder="请输入团队名称" size="large" />
        </Form.Item>

        <Form.Item
          label="团队描述"
          name="description"
          rules={[{ max: 500, message: '团队描述长度不能超过500字符！' }]}
        >
          <TextArea
            placeholder="请输入团队描述（可选）"
            rows={4}
            showCount
            maxLength={500}
          />
        </Form.Item>
      </ModalForm>

      {/* 创建团队模态框 */}
      <ModalForm
        title={
          <Flex align="center" gap={8}>
            <TeamOutlined style={{ color: '#1890ff' }} />
            <span>创建新团队</span>
          </Flex>
        }
        open={createModalVisible}
        onOpenChange={(visible) => {
          if (!visible) {
            setCreateModalVisible(false);
            form.resetFields();
          }
        }}
        form={form}
        layout="vertical"
        onFinish={handleCreateTeam}
        autoComplete="off"
        width={500}
        modalProps={{ style: { top: 100 } }}
        submitter={{
          searchConfig: {
            submitText: '创建团队',
            resetText: '取消',
          },
          submitButtonProps: {
            loading: createLoading,
            style: {
              background: 'linear-gradient(135deg, #1890ff, #722ed1)',
              border: 'none',
            },
          },
          onReset: () => {
            setCreateModalVisible(false);
            form.resetFields();
          },
        }}
      >
        <Form.Item
          label="团队名称"
          name="name"
          rules={[
            { required: true, message: '请输入团队名称！' },
            { max: 100, message: '团队名称长度不能超过100字符！' },
            { min: 2, message: '团队名称至少需要2个字符！' },
          ]}
        >
          <Input placeholder="请输入团队名称" size="large" />
        </Form.Item>

        <Form.Item
          label="团队描述"
          name="description"
          rules={[{ max: 500, message: '团队描述长度不能超过500字符！' }]}
        >
          <TextArea
            placeholder="请输入团队描述（可选）"
            rows={4}
            showCount
            maxLength={500}
          />
        </Form.Item>
      </ModalForm>
    </>
  );
};

export default TeamManagementCard;
