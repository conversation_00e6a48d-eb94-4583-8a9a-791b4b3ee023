package com.teammanage.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.teammanage.entity.TeamMember;
import com.teammanage.enums.TeamRole;
import com.teammanage.exception.TeamAccessDeniedException;
import com.teammanage.mapper.TeamMemberMapper;

/**
 * 团队成员访问验证服务测试
 */
@ExtendWith(MockitoExtension.class)
class TeamMemberAccessValidatorTest {

    @Mock
    private TeamMemberMapper teamMemberMapper;

    @InjectMocks
    private TeamMemberAccessValidator validator;

    private Long teamId = 1L;
    private Long userId = 100L;

    @BeforeEach
    void setUp() {
        // 每个测试前的初始化
    }

    @Test
    void testValidateTeamAccess_Success() {
        // 准备测试数据 - 活跃的团队成员
        TeamMember activeMember = createTeamMember(true, false);
        when(teamMemberMapper.findByTeamIdAndAccountId(teamId, userId)).thenReturn(activeMember);

        // 执行测试 - 应该不抛出异常
        assertDoesNotThrow(() -> validator.validateTeamAccess(teamId, userId));
    }

    @Test
    void testValidateTeamAccess_NotTeamMember() {
        // 准备测试数据 - 用户不是团队成员
        when(teamMemberMapper.findByTeamIdAndAccountId(teamId, userId)).thenReturn(null);

        // 执行测试 - 应该抛出异常
        TeamAccessDeniedException exception = assertThrows(
            TeamAccessDeniedException.class,
            () -> validator.validateTeamAccess(teamId, userId)
        );

        assertEquals("您不是该团队的成员", exception.getMessage());
        assertEquals(teamId, exception.getTeamId());
        assertEquals(userId, exception.getUserId());
    }

    @Test
    void testValidateTeamAccess_InactiveMember() {
        // 准备测试数据 - 被停用的团队成员
        TeamMember inactiveMember = createTeamMember(false, false);
        when(teamMemberMapper.findByTeamIdAndAccountId(teamId, userId)).thenReturn(inactiveMember);

        // 执行测试 - 应该抛出异常
        TeamAccessDeniedException exception = assertThrows(
            TeamAccessDeniedException.class,
            () -> validator.validateTeamAccess(teamId, userId)
        );

        assertEquals("您的账户已在此团队中被停用", exception.getMessage());
        assertEquals(teamId, exception.getTeamId());
        assertEquals(userId, exception.getUserId());
    }

    @Test
    void testValidateTeamAccess_DeletedMember() {
        // 准备测试数据 - 被删除的团队成员
        TeamMember deletedMember = createTeamMember(true, true);
        when(teamMemberMapper.findByTeamIdAndAccountId(teamId, userId)).thenReturn(deletedMember);

        // 执行测试 - 应该抛出异常
        TeamAccessDeniedException exception = assertThrows(
            TeamAccessDeniedException.class,
            () -> validator.validateTeamAccess(teamId, userId)
        );

        assertEquals("您已不是该团队的成员", exception.getMessage());
    }

    @Test
    void testCanAccessTeam_ActiveMember() {
        // 准备测试数据 - 活跃的团队成员
        TeamMember activeMember = createTeamMember(true, false);
        when(teamMemberMapper.findByTeamIdAndAccountId(teamId, userId)).thenReturn(activeMember);

        // 执行测试
        boolean canAccess = validator.canAccessTeam(teamId, userId);

        assertTrue(canAccess);
    }

    @Test
    void testCanAccessTeam_InactiveMember() {
        // 准备测试数据 - 被停用的团队成员
        TeamMember inactiveMember = createTeamMember(false, false);
        when(teamMemberMapper.findByTeamIdAndAccountId(teamId, userId)).thenReturn(inactiveMember);

        // 执行测试
        boolean canAccess = validator.canAccessTeam(teamId, userId);

        assertFalse(canAccess);
    }

    @Test
    void testIsTeamMember_ActiveMember() {
        // 准备测试数据 - 活跃的团队成员
        TeamMember activeMember = createTeamMember(true, false);
        when(teamMemberMapper.findByTeamIdAndAccountId(teamId, userId)).thenReturn(activeMember);

        // 执行测试
        boolean isMember = validator.isTeamMember(teamId, userId);

        assertTrue(isMember);
    }

    @Test
    void testIsTeamMember_InactiveMember() {
        // 准备测试数据 - 被停用的团队成员（仍然是成员，只是不能访问）
        TeamMember inactiveMember = createTeamMember(false, false);
        when(teamMemberMapper.findByTeamIdAndAccountId(teamId, userId)).thenReturn(inactiveMember);

        // 执行测试
        boolean isMember = validator.isTeamMember(teamId, userId);

        assertTrue(isMember); // 被停用的成员仍然是团队成员，只是不能访问
    }

    @Test
    void testIsTeamMember_DeletedMember() {
        // 准备测试数据 - 被删除的团队成员
        TeamMember deletedMember = createTeamMember(true, true);
        when(teamMemberMapper.findByTeamIdAndAccountId(teamId, userId)).thenReturn(deletedMember);

        // 执行测试
        boolean isMember = validator.isTeamMember(teamId, userId);

        assertFalse(isMember); // 被删除的成员不再是团队成员
    }

    @Test
    void testGetAccessDeniedReason_ActiveMember() {
        // 准备测试数据 - 活跃的团队成员
        TeamMember activeMember = createTeamMember(true, false);
        when(teamMemberMapper.findByTeamIdAndAccountId(teamId, userId)).thenReturn(activeMember);

        // 执行测试
        String reason = validator.getAccessDeniedReason(teamId, userId);

        assertNull(reason); // 活跃成员可以访问，没有拒绝原因
    }

    @Test
    void testGetAccessDeniedReason_InactiveMember() {
        // 准备测试数据 - 被停用的团队成员
        TeamMember inactiveMember = createTeamMember(false, false);
        when(teamMemberMapper.findByTeamIdAndAccountId(teamId, userId)).thenReturn(inactiveMember);

        // 执行测试
        String reason = validator.getAccessDeniedReason(teamId, userId);

        assertEquals("您的账户已在此团队中被停用", reason);
    }

    /**
     * 创建测试用的团队成员对象
     */
    private TeamMember createTeamMember(boolean isActive, boolean isDeleted) {
        TeamMember member = new TeamMember();
        member.setTeamId(teamId);
        member.setAccountId(userId);
        member.setRole(TeamRole.TEAM_MEMBER);
        member.setIsActive(isActive);
        member.setIsDeleted(isDeleted);
        return member;
    }
}
