package com.teammanage.entity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.Test;

import com.teammanage.enums.TeamRole;

/**
 * TeamMember 访问控制方法测试
 */
class TeamMemberAccessControlTest {

    @Test
    void testIsActiveStatus() {
        TeamMember member = new TeamMember();
        
        // 测试活跃状态
        member.setIsActive(true);
        assertTrue(member.isActiveStatus());
        
        // 测试非活跃状态
        member.setIsActive(false);
        assertFalse(member.isActiveStatus());
        
        // 测试null状态
        member.setIsActive(null);
        assertFalse(member.isActiveStatus());
    }

    @Test
    void testIsDisabled() {
        TeamMember member = new TeamMember();
        
        // 测试活跃状态（未禁用）
        member.setIsActive(true);
        assertFalse(member.isDisabled());
        
        // 测试非活跃状态（已禁用）
        member.setIsActive(false);
        assertTrue(member.isDisabled());
        
        // 测试null状态（视为禁用）
        member.setIsActive(null);
        assertTrue(member.isDisabled());
    }

    @Test
    void testIsDeactivated() {
        TeamMember member = new TeamMember();
        
        // isDeactivated 应该与 isDisabled 行为一致
        member.setIsActive(true);
        assertFalse(member.isDeactivated());
        
        member.setIsActive(false);
        assertTrue(member.isDeactivated());
        
        member.setIsActive(null);
        assertTrue(member.isDeactivated());
    }

    @Test
    void testCanAccessTeam_ActiveMember() {
        TeamMember member = new TeamMember();
        member.setIsActive(true);
        member.setIsDeleted(false);
        
        assertTrue(member.canAccessTeam());
    }

    @Test
    void testCanAccessTeam_InactiveMember() {
        TeamMember member = new TeamMember();
        member.setIsActive(false);
        member.setIsDeleted(false);
        
        assertFalse(member.canAccessTeam());
    }

    @Test
    void testCanAccessTeam_DeletedMember() {
        TeamMember member = new TeamMember();
        member.setIsActive(true);
        member.setIsDeleted(true);
        
        assertFalse(member.canAccessTeam());
    }

    @Test
    void testCanAccessTeam_DeletedAndInactiveMember() {
        TeamMember member = new TeamMember();
        member.setIsActive(false);
        member.setIsDeleted(true);
        
        assertFalse(member.canAccessTeam());
    }

    @Test
    void testShouldShowInTeamList_ActiveMember() {
        TeamMember member = new TeamMember();
        member.setIsActive(true);
        member.setIsDeleted(false);
        
        assertTrue(member.shouldShowInTeamList());
    }

    @Test
    void testShouldShowInTeamList_InactiveMember() {
        TeamMember member = new TeamMember();
        member.setIsActive(false);
        member.setIsDeleted(false);
        
        assertTrue(member.shouldShowInTeamList()); // 被禁用的成员仍应显示
    }

    @Test
    void testShouldShowInTeamList_DeletedMember() {
        TeamMember member = new TeamMember();
        member.setIsActive(true);
        member.setIsDeleted(true);
        
        assertFalse(member.shouldShowInTeamList()); // 被删除的成员不应显示
    }

    @Test
    void testGetStatusDescription() {
        TeamMember member = new TeamMember();
        
        // 测试活跃状态
        member.setIsActive(true);
        member.setIsDeleted(false);
        assertEquals("活跃", member.getStatusDescription());
        
        // 测试停用状态
        member.setIsActive(false);
        member.setIsDeleted(false);
        assertEquals("已停用", member.getStatusDescription());
        
        // 测试删除状态（优先级高于活跃状态）
        member.setIsActive(true);
        member.setIsDeleted(true);
        assertEquals("已删除", member.getStatusDescription());
        
        // 测试删除且停用状态
        member.setIsActive(false);
        member.setIsDeleted(true);
        assertEquals("已删除", member.getStatusDescription());
    }

    @Test
    void testGetAccessDeniedMessage() {
        TeamMember member = new TeamMember();
        
        // 测试活跃成员（不应该有拒绝消息，但方法会返回默认消息）
        member.setIsActive(true);
        member.setIsDeleted(false);
        assertEquals("无法访问该团队", member.getAccessDeniedMessage());
        
        // 测试停用成员
        member.setIsActive(false);
        member.setIsDeleted(false);
        assertEquals("您的账户已在此团队中被停用", member.getAccessDeniedMessage());
        
        // 测试删除成员（优先级高于停用状态）
        member.setIsActive(false);
        member.setIsDeleted(true);
        assertEquals("您已不是该团队的成员", member.getAccessDeniedMessage());
        
        // 测试删除但活跃的成员
        member.setIsActive(true);
        member.setIsDeleted(true);
        assertEquals("您已不是该团队的成员", member.getAccessDeniedMessage());
    }

    @Test
    void testAccessControlWithNullValues() {
        TeamMember member = new TeamMember();
        
        // 测试所有字段为null的情况
        member.setIsActive(null);
        member.setIsDeleted(null);
        
        // null值应该被视为false（安全默认值）
        assertFalse(member.isActiveStatus());
        assertTrue(member.isDisabled());
        assertFalse(member.canAccessTeam()); // isActive为null时不能访问团队
        assertTrue(member.shouldShowInTeamList()); // isDeleted为null时视为false，应该显示
        assertEquals("已停用", member.getStatusDescription());
        assertEquals("您的账户已在此团队中被停用", member.getAccessDeniedMessage());
    }

    @Test
    void testAccessControlIntegration() {
        // 测试完整的访问控制流程
        
        // 场景1：正常活跃成员
        TeamMember activeMember = new TeamMember();
        activeMember.setTeamId(1L);
        activeMember.setAccountId(100L);
        activeMember.setRole(TeamRole.TEAM_MEMBER);
        activeMember.setIsActive(true);
        activeMember.setIsDeleted(false);
        
        assertTrue(activeMember.canAccessTeam());
        assertTrue(activeMember.shouldShowInTeamList());
        assertEquals("活跃", activeMember.getStatusDescription());
        
        // 场景2：被停用的成员
        TeamMember inactiveMember = new TeamMember();
        inactiveMember.setTeamId(1L);
        inactiveMember.setAccountId(101L);
        inactiveMember.setRole(TeamRole.TEAM_MEMBER);
        inactiveMember.setIsActive(false);
        inactiveMember.setIsDeleted(false);
        
        assertFalse(inactiveMember.canAccessTeam());
        assertTrue(inactiveMember.shouldShowInTeamList()); // 仍应在列表中显示
        assertEquals("已停用", inactiveMember.getStatusDescription());
        assertEquals("您的账户已在此团队中被停用", inactiveMember.getAccessDeniedMessage());
        
        // 场景3：被删除的成员
        TeamMember deletedMember = new TeamMember();
        deletedMember.setTeamId(1L);
        deletedMember.setAccountId(102L);
        deletedMember.setRole(TeamRole.TEAM_MEMBER);
        deletedMember.setIsActive(true);
        deletedMember.setIsDeleted(true);
        
        assertFalse(deletedMember.canAccessTeam());
        assertFalse(deletedMember.shouldShowInTeamList()); // 不应在列表中显示
        assertEquals("已删除", deletedMember.getStatusDescription());
        assertEquals("您已不是该团队的成员", deletedMember.getAccessDeniedMessage());
    }
}
