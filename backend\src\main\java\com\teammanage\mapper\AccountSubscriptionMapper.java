package com.teammanage.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teammanage.entity.AccountSubscription;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户订阅记录Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface AccountSubscriptionMapper extends BaseMapper<AccountSubscription> {

    /**
     * 根据用户ID查询订阅记录
     * 
     * @param accountId 用户ID
     * @return 订阅记录列表
     */
    @Select("SELECT * FROM account_subscription WHERE account_id = #{accountId} ORDER BY created_at DESC")
    List<AccountSubscription> findByAccountId(@Param("accountId") Long accountId);

    /**
     * 根据用户ID查询当前有效订阅
     * 
     * @param accountId 用户ID
     * @return 当前有效订阅
     */
    @Select("SELECT * FROM account_subscription WHERE account_id = #{accountId} AND status = 'ACTIVE' AND (end_date IS NULL OR end_date >= CURDATE()) ORDER BY created_at DESC LIMIT 1")
    AccountSubscription findCurrentActiveByAccountId(@Param("accountId") Long accountId);

}
