package com.teammanage.dto.request;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;


/**
 * 创建订阅请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

public class CreateSubscriptionRequest {

    /**
     * 套餐ID
     */
    @NotNull(message = "套餐ID不能为空")
    private Long planId;

    /**
     * 订阅时长（月）
     */
    @Min(value = 1, message = "订阅时长至少1个月")
    private Integer duration;

    // 手动添加getter/setter方法
    public Long getPlanId() { return planId; }
    public void setPlanId(Long planId) { this.planId = planId; }

    public Integer getDuration() { return duration; }
    public void setDuration(Integer duration) { this.duration = duration; }

}
