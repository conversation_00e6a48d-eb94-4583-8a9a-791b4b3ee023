2025-08-02 22:14:21.179 [background-preinit] INFO  o.h.validator.internal.util.Version -- HV000001: Hibernate Validator 8.0.2.Final
2025-08-02 22:14:21.222 [main] INFO  c.teammanage.TeamManageApplication -- Starting TeamManageApplication using Java 24.0.1 with PID 18268 (F:\Project\teamAuth\backend\target\classes started by X in F:\Project\teamAuth)
2025-08-02 22:14:21.223 [main] INFO  c.teammanage.TeamManageApplication -- No active profile set, falling back to 1 default profile: "default"
2025-08-02 22:14:22.715 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer -- <PERSON><PERSON> initialized with port 8080 (http)
2025-08-02 22:14:22.732 [main] INFO  o.a.coyote.http11.Http11NioProtocol -- Initializing ProtocolHandler ["http-nio-8080"]
2025-08-02 22:14:22.735 [main] INFO  o.a.catalina.core.StandardService -- Starting service [Tomcat]
2025-08-02 22:14:22.735 [main] INFO  o.a.catalina.core.StandardEngine -- Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-02 22:14:22.799 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] -- Initializing Spring embedded WebApplicationContext
2025-08-02 22:14:22.800 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext -- Root WebApplicationContext: initialization completed in 1517 ms
2025-08-02 22:14:23.765 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration -- 

Using generated security password: 2c765c3d-3c2c-4699-bbac-e62cc5a3e6e9

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-02 22:14:23.976 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware -- Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5b1f29fa
2025-08-02 22:14:24.148 [main] INFO  o.a.coyote.http11.Http11NioProtocol -- Starting ProtocolHandler ["http-nio-8080"]
2025-08-02 22:14:24.170 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer -- Tomcat started on port 8080 (http) with context path '/api/v1'
2025-08-02 22:14:24.182 [main] INFO  c.teammanage.TeamManageApplication -- Started TeamManageApplication in 3.617 seconds (process running for 3.995)
2025-08-02 22:14:24.189 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource -- HikariPool-1 - Starting...
2025-08-02 22:14:24.270 [scheduling-1] INFO  com.zaxxer.hikari.pool.HikariPool -- HikariPool-1 - Added connection org.mariadb.jdbc.Connection@10d9dd96
2025-08-02 22:14:24.272 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource -- HikariPool-1 - Start completed.
2025-08-02 22:14:24.302 [scheduling-1] DEBUG c.t.m.T.updateExpiredInvitations -- ==>  Preparing: UPDATE team_invitation SET status = 'EXPIRED', updated_at = ? WHERE status = 'PENDING' AND expires_at <= ?
2025-08-02 22:14:24.327 [scheduling-1] DEBUG c.t.m.T.updateExpiredInvitations -- ==> Parameters: 2025-08-02T22:14:24.278094(LocalDateTime), 2025-08-02T22:14:24.278094(LocalDateTime)
2025-08-02 22:14:24.328 [scheduling-1] DEBUG c.t.m.T.updateExpiredInvitations -- <==    Updates: 12
2025-08-02 22:14:24.329 [scheduling-1] INFO  c.t.service.TeamInvitationService -- 更新过期邀请状态完成: count=12
2025-08-02 22:14:24.333 [scheduling-1] INFO  c.t.task.InvitationCleanupTask -- 定时任务：更新过期邀请状态完成，更新数量: 12
2025-08-02 22:14:53.410 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[localhost].[/api/v1] -- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-02 22:14:53.410 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet -- Initializing Servlet 'dispatcherServlet'
2025-08-02 22:14:53.411 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet -- Completed initialization in 1 ms
2025-08-02 22:14:53.542 [http-nio-8080-exec-1] WARN  c.t.security.JwtAuthenticationFilter -- 会话无效或已过期: 3107e84d-fb31-42e5-ad42-b82937b20f00
2025-08-02 22:14:53.547 [http-nio-8080-exec-1] WARN  c.t.s.JwtAuthenticationEntryPoint -- 未认证的请求: GET /api/v1/users/profile
2025-08-02 22:14:59.929 [http-nio-8080-exec-2] INFO  c.t.service.VerificationCodeService -- 发送验证码: email=<EMAIL>, code=858110, type=login
2025-08-02 22:15:19.717 [http-nio-8080-exec-3] INFO  c.t.service.VerificationCodeService -- 验证码验证成功: email=<EMAIL>, type=login
2025-08-02 22:15:19.719 [http-nio-8080-exec-3] DEBUG c.t.mapper.AccountMapper.findByEmail -- ==>  Preparing: SELECT * FROM account WHERE email = ?
2025-08-02 22:15:19.720 [http-nio-8080-exec-3] DEBUG c.t.mapper.AccountMapper.findByEmail -- ==> Parameters: <EMAIL>(String)
2025-08-02 22:15:19.736 [http-nio-8080-exec-3] DEBUG c.t.mapper.AccountMapper.findByEmail -- <==      Total: 1
2025-08-02 22:15:19.755 [http-nio-8080-exec-3] INFO  c.t.service.UserSessionService -- 创建用户会话: accountId=8, tokenHash=49c6cada-4b2f-450f-a223-21e7f95bb0a2
2025-08-02 22:15:19.756 [http-nio-8080-exec-3] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 22:15:19.758 [http-nio-8080-exec-3] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 22:15:19.762 [http-nio-8080-exec-3] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 22:15:19.763 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 22:15:19.763 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 22:15:19.765 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 22:15:19.765 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 22:15:19.766 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 22:15:19.767 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 22:15:19.768 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 22:15:19.768 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 22:15:19.769 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 22:15:19.769 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 22:15:19.770 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 22:15:19.770 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 22:15:19.772 [http-nio-8080-exec-3] INFO  com.teammanage.service.AuthService -- 用户登录成功: userId=8, email=<EMAIL>, teamCount=2
2025-08-02 22:15:20.212 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 22:15:20.215 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 22:15:20.215 [http-nio-8080-exec-4] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 22:15:20.216 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 22:15:20.218 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 22:15:20.218 [http-nio-8080-exec-4] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 22:15:20.222 [http-nio-8080-exec-4] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 22:15:20.222 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 22:15:20.226 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 22:15:20.226 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 22:15:20.226 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 22:15:20.226 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 22:15:20.227 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 22:15:20.227 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 22:15:20.227 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 22:15:20.228 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 22:15:20.228 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 22:15:20.228 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 22:15:20.228 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 22:15:20.228 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 22:15:20.229 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 22:15:20.228 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 22:15:20.229 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 22:15:20.229 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 22:15:20.229 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 22:15:20.229 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 22:15:20.230 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 22:15:20.230 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 22:15:20.230 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 22:15:20.230 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 22:15:20.230 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 22:15:20.230 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 22:15:20.230 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 22:15:20.231 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 22:15:20.231 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 22:15:20.231 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 22:15:20.232 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 22:15:20.232 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 22:15:20.232 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 22:15:20.232 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 22:15:20.233 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 22:15:20.233 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 22:15:20.233 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 22:15:20.233 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 22:15:20.234 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 22:15:20.234 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 22:15:20.236 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 22:15:20.238 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 22:15:20.277 [http-nio-8080-exec-7] DEBUG c.t.mapper.TodoMapper.selectList -- ==>  Preparing: SELECT id,title,description,status,priority,user_id,created_at,updated_at FROM todos WHERE (user_id = ?) ORDER BY created_at DESC
2025-08-02 22:15:20.277 [http-nio-8080-exec-8] DEBUG c.t.mapper.TodoMapper.selectList -- ==>  Preparing: SELECT id,title,description,status,priority,user_id,created_at,updated_at FROM todos WHERE (user_id = ?)
2025-08-02 22:15:20.278 [http-nio-8080-exec-7] DEBUG c.t.mapper.TodoMapper.selectList -- ==> Parameters: 8(Long)
2025-08-02 22:15:20.278 [http-nio-8080-exec-8] DEBUG c.t.mapper.TodoMapper.selectList -- ==> Parameters: 8(Long)
2025-08-02 22:15:20.280 [http-nio-8080-exec-8] DEBUG c.t.mapper.TodoMapper.selectList -- <==      Total: 6
2025-08-02 22:15:20.280 [http-nio-8080-exec-7] DEBUG c.t.mapper.TodoMapper.selectList -- <==      Total: 6
2025-08-02 22:17:14.286 [background-preinit] INFO  o.h.validator.internal.util.Version -- HV000001: Hibernate Validator 8.0.2.Final
2025-08-02 22:17:14.334 [main] INFO  c.teammanage.TeamManageApplication -- Starting TeamManageApplication using Java 24.0.1 with PID 19692 (F:\Project\teamAuth\backend\target\classes started by X in F:\Project\teamAuth)
2025-08-02 22:17:14.336 [main] INFO  c.teammanage.TeamManageApplication -- No active profile set, falling back to 1 default profile: "default"
2025-08-02 22:17:16.156 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer -- Tomcat initialized with port 8080 (http)
2025-08-02 22:17:16.173 [main] INFO  o.a.coyote.http11.Http11NioProtocol -- Initializing ProtocolHandler ["http-nio-8080"]
2025-08-02 22:17:16.176 [main] INFO  o.a.catalina.core.StandardService -- Starting service [Tomcat]
2025-08-02 22:17:16.176 [main] INFO  o.a.catalina.core.StandardEngine -- Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-02 22:17:16.244 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] -- Initializing Spring embedded WebApplicationContext
2025-08-02 22:17:16.245 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext -- Root WebApplicationContext: initialization completed in 1842 ms
2025-08-02 22:17:17.235 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration -- 

Using generated security password: 908fd5f1-c115-4421-9bec-b2c0c956e38b

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-02 22:17:17.428 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware -- Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5b1f29fa
2025-08-02 22:17:17.603 [main] INFO  o.a.coyote.http11.Http11NioProtocol -- Starting ProtocolHandler ["http-nio-8080"]
2025-08-02 22:17:17.624 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer -- Tomcat started on port 8080 (http) with context path '/api/v1'
2025-08-02 22:17:17.636 [main] INFO  c.teammanage.TeamManageApplication -- Started TeamManageApplication in 3.891 seconds (process running for 4.217)
2025-08-02 22:17:17.644 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource -- HikariPool-1 - Starting...
2025-08-02 22:17:17.718 [scheduling-1] INFO  com.zaxxer.hikari.pool.HikariPool -- HikariPool-1 - Added connection org.mariadb.jdbc.Connection@5873029d
2025-08-02 22:17:17.720 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource -- HikariPool-1 - Start completed.
2025-08-02 22:17:17.745 [scheduling-1] DEBUG c.t.m.T.updateExpiredInvitations -- ==>  Preparing: UPDATE team_invitation SET status = 'EXPIRED', updated_at = ? WHERE status = 'PENDING' AND expires_at <= ?
2025-08-02 22:17:17.764 [scheduling-1] DEBUG c.t.m.T.updateExpiredInvitations -- ==> Parameters: 2025-08-02T22:17:17.724671500(LocalDateTime), 2025-08-02T22:17:17.724671500(LocalDateTime)
2025-08-02 22:17:17.766 [scheduling-1] DEBUG c.t.m.T.updateExpiredInvitations -- <==    Updates: 0
