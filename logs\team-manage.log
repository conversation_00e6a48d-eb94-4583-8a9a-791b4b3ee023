2025-08-03 00:00:10.040 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-03 00:00:10.042 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-03 00:00:10.043 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-03 00:00:10.043 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-03 00:00:10.044 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-03 00:00:10.044 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-03 00:00:10.045 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-03 00:00:10.046 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-03 00:00:10.046 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-03 00:00:10.047 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-03 00:00:10.047 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-03 00:00:10.048 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-03 00:00:10.048 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-03 00:00:10.048 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-03 00:00:10.049 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
