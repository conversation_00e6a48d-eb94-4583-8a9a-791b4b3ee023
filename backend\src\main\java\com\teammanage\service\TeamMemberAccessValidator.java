package com.teammanage.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.teammanage.entity.TeamMember;
import com.teammanage.exception.TeamAccessDeniedException;
import com.teammanage.mapper.TeamMemberMapper;

/**
 * 团队成员访问验证服务
 * 
 * 提供团队成员状态验证功能，确保只有活跃的成员可以访问团队
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class TeamMemberAccessValidator {

    private static final Logger log = LoggerFactory.getLogger(TeamMemberAccessValidator.class);

    @Autowired
    private TeamMemberMapper teamMemberMapper;

    /**
     * 验证用户是否可以访问指定团队
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @throws TeamAccessDeniedException 如果用户无法访问团队
     */
    public void validateTeamAccess(Long teamId, Long userId) {
        TeamMember teamMember = teamMemberMapper.findByTeamIdAndAccountId(teamId, userId);
        
        if (teamMember == null) {
            log.warn("用户尝试访问不属于的团队: userId={}, teamId={}", userId, teamId);
            throw TeamAccessDeniedException.notTeamMember(teamId, userId);
        }

        if (!teamMember.canAccessTeam()) {
            log.warn("被禁用用户尝试访问团队: userId={}, teamId={}, status={}", 
                    userId, teamId, teamMember.getStatusDescription());
            throw new TeamAccessDeniedException(teamMember.getAccessDeniedMessage(), teamId, userId);
        }

        log.debug("团队访问验证通过: userId={}, teamId={}", userId, teamId);
    }

    /**
     * 检查用户是否可以访问指定团队（不抛出异常）
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 是否可以访问
     */
    public boolean canAccessTeam(Long teamId, Long userId) {
        try {
            validateTeamAccess(teamId, userId);
            return true;
        } catch (TeamAccessDeniedException e) {
            return false;
        }
    }

    /**
     * 获取用户在团队中的成员信息
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 团队成员信息，如果不存在返回null
     */
    public TeamMember getTeamMember(Long teamId, Long userId) {
        return teamMemberMapper.findByTeamIdAndAccountId(teamId, userId);
    }

    /**
     * 验证用户是否为团队成员（包括被禁用的成员）
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 是否为团队成员
     */
    public boolean isTeamMember(Long teamId, Long userId) {
        TeamMember teamMember = teamMemberMapper.findByTeamIdAndAccountId(teamId, userId);
        return teamMember != null && teamMember.shouldShowInTeamList();
    }

    /**
     * 获取用户无法访问团队的原因
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 无法访问的原因，如果可以访问返回null
     */
    public String getAccessDeniedReason(Long teamId, Long userId) {
        TeamMember teamMember = teamMemberMapper.findByTeamIdAndAccountId(teamId, userId);
        
        if (teamMember == null) {
            return "您不是该团队的成员";
        }

        if (!teamMember.canAccessTeam()) {
            return teamMember.getAccessDeniedMessage();
        }

        return null; // 可以访问
    }

    /**
     * 验证并记录团队访问尝试
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @param operation 操作描述
     * @throws TeamAccessDeniedException 如果用户无法访问团队
     */
    public void validateAndLogTeamAccess(Long teamId, Long userId, String operation) {
        try {
            validateTeamAccess(teamId, userId);
            log.info("团队访问成功: userId={}, teamId={}, operation={}", userId, teamId, operation);
        } catch (TeamAccessDeniedException e) {
            log.warn("团队访问被拒绝: userId={}, teamId={}, operation={}, reason={}", 
                    userId, teamId, operation, e.getMessage());
            throw e;
        }
    }
}
