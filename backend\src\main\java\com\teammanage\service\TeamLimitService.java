package com.teammanage.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.teammanage.entity.AccountSubscription;
import com.teammanage.entity.SubscriptionPlan;
import com.teammanage.mapper.AccountSubscriptionMapper;
import com.teammanage.mapper.SubscriptionPlanMapper;

/**
 * 团队限制检查服务
 * 
 * 用于检查用户的团队创建限制，避免服务间的循环依赖
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class TeamLimitService {

    private static final Logger log = LoggerFactory.getLogger(TeamLimitService.class);

    @Autowired
    private AccountSubscriptionMapper accountSubscriptionMapper;

    @Autowired
    private SubscriptionPlanMapper subscriptionPlanMapper;

    /**
     * 获取用户可创建的团队数量限制
     *
     * @param userId 用户ID
     * @return 团队数量限制
     */
    public int getUserTeamLimit(Long userId) {
        AccountSubscription subscription = accountSubscriptionMapper.findCurrentActiveByAccountId(userId);
        if (subscription != null) {
            SubscriptionPlan plan = subscriptionPlanMapper.selectById(subscription.getSubscriptionPlanId());
            if (plan != null) {
                return plan.getMaxSize();
            }
        }

        // 默认返回免费套餐限制（可创建1个团队）
        return 1;
    }
}
