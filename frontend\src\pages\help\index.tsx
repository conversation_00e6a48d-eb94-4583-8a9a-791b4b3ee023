import {
  BookOutlined,
  QuestionCircleOutlined,
  SettingOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { Button, Divider, Space, Typography } from 'antd';
import { ProCard } from '@ant-design/pro-components';

const { Title, Paragraph, Text } = Typography;

const HelpPage: React.FC = () => {
  return (
    <PageContainer
      title="帮助中心"
      subTitle="团队协作管理系统使用指南"
      extra={[
        <Button key="contact" type="primary">
          联系技术支持
        </Button>,
      ]}
    >
      <div style={{ maxWidth: 1200, margin: '0 auto' }}>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* 快速开始 */}
          <ProCard>
            <Title level={3}>
              <BookOutlined style={{ marginRight: 8 }} />
              快速开始
            </Title>
            <Paragraph>
              欢迎使用团队协作管理系统！本系统帮助您高效管理团队成员、项目和任务。
            </Paragraph>
            <Paragraph>
              <Text strong>首次使用步骤：</Text>
              <ol>
                <li>注册账号并登录系统</li>
                <li>创建或加入团队</li>
                <li>邀请团队成员</li>
                <li>开始协作管理</li>
              </ol>
            </Paragraph>
          </ProCard>

          {/* 团队管理 */}
          <ProCard>
            <Title level={3}>
              <TeamOutlined style={{ marginRight: 8 }} />
              团队管理
            </Title>
            <Paragraph>
              <Text strong>创建团队：</Text>
              在团队页面点击"创建团队"按钮，填写团队信息即可创建新团队。
            </Paragraph>
            <Paragraph>
              <Text strong>邀请成员：</Text>
              团队管理员可以通过邮箱邀请新成员加入团队。
            </Paragraph>
            <Paragraph>
              <Text strong>角色权限：</Text>
              系统支持多种角色权限，包括管理员、普通成员等，确保团队协作的安全性。
            </Paragraph>
          </ProCard>

          {/* 系统设置 */}
          <ProCard>
            <Title level={3}>
              <SettingOutlined style={{ marginRight: 8 }} />
              系统设置
            </Title>
            <Paragraph>
              <Text strong>个人设置：</Text>
              在右上角头像菜单中可以修改个人信息、密码等设置。
            </Paragraph>
            <Paragraph>
              <Text strong>团队设置：</Text>
              团队管理员可以在团队设置页面修改团队信息、管理成员权限。
            </Paragraph>
          </ProCard>

          {/* 常见问题 */}
          <ProCard>
            <Title level={3}>
              <QuestionCircleOutlined style={{ marginRight: 8 }} />
              常见问题
            </Title>
            <Paragraph>
              <Text strong>Q: 如何切换团队？</Text>
              <br />
              A: 在顶部导航栏的团队名称处点击，可以选择切换到其他团队。
            </Paragraph>
            <Divider />
            <Paragraph>
              <Text strong>Q: 忘记密码怎么办？</Text>
              <br />
              A: 在登录页面点击"忘记密码"，通过邮箱重置密码。
            </Paragraph>
            <Divider />
            <Paragraph>
              <Text strong>Q: 如何邀请新成员？</Text>
              <br />
              A: 团队管理员可以在团队管理页面通过邮箱邀请新成员。
            </Paragraph>
          </ProCard>

          {/* 联系我们 */}
          <ProCard>
            <Title level={3}>联系我们</Title>
            <Paragraph>
              如果您在使用过程中遇到问题，可以通过以下方式联系我们：
            </Paragraph>
            <Paragraph>
              <Text strong>技术支持邮箱：</Text> <EMAIL>
              <br />
              <Text strong>用户反馈：</Text> <EMAIL>
              <br />
              <Text strong>工作时间：</Text> 周一至周五 9:00-18:00
            </Paragraph>
          </ProCard>
        </Space>
      </div>
    </PageContainer>
  );
};

export default HelpPage;
