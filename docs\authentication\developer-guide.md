# 开发者指南

## 概述

本指南为新加入团队的开发者提供身份验证系统的快速上手指导，包括开发环境设置、常用API使用、调试技巧和最佳实践。

## 1. 快速开始

### 1.1 环境准备

**前端环境**:
```bash
# 安装依赖
npm install

# 启动开发服务器
npm start

# 访问应用
http://localhost:8000
```

**后端环境**:
```bash
# 启动后端服务
mvn spring-boot:run

# API文档地址
http://localhost:8080/swagger-ui.html
```

### 1.2 核心文件结构

```
frontend/src/
├── services/
│   └── auth.ts              # 认证服务API
├── utils/
│   ├── request.ts           # HTTP请求配置
│   └── tokenUtils.ts        # Token工具函数
├── pages/
│   ├── user/login/          # 登录页面
│   └── personal-center/     # 个人中心
├── access.ts                # 权限控制
└── app.tsx                  # 应用初始化

docs/authentication/
├── README.md                # 文档概览
├── architecture.md          # 系统架构
├── auth-flow.md            # 认证流程
├── token-management.md      # Token管理
├── api-integration.md       # API集成
└── developer-guide.md       # 开发指南
```

## 2. 常用开发任务

### 2.1 添加新的认证API

**步骤1: 定义接口类型**
```typescript
// 在 frontend/src/services/types.ts 中添加
interface NewAuthRequest {
  // 请求参数定义
}

interface NewAuthResponse {
  // 响应数据定义
}
```

**步骤2: 实现Service方法**
```typescript
// 在 frontend/src/services/auth.ts 中添加
static async newAuthMethod(data: NewAuthRequest): Promise<NewAuthResponse> {
  const response = await apiRequest.post<NewAuthResponse>(
    '/auth/new-endpoint',
    data
  );
  return response.data;
}
```

**步骤3: 在组件中使用**
```typescript
const handleNewAuth = async () => {
  try {
    const result = await AuthService.newAuthMethod(requestData);
    // 处理成功响应
  } catch (error) {
    // 错误已由拦截器处理
  }
};
```

### 2.2 添加新的权限检查

**步骤1: 定义权限类型**
```typescript
// 在 frontend/src/access.ts 中添加
interface AccessPermissions {
  // 现有权限...
  canNewFeature: boolean;  // 新权限
}
```

**步骤2: 实现权限逻辑**
```typescript
export default function access(initialState: InitialState): AccessPermissions {
  const { currentUser, currentTeam } = initialState || {};
  
  return {
    // 现有权限...
    canNewFeature: currentUser && currentTeam && 
                   hasTeamInCurrentToken() && 
                   getIsCreatorFromCurrentToken(),
  };
}
```

**步骤3: 在组件中使用**
```typescript
import { useAccess } from 'umi';

const MyComponent = () => {
  const { canNewFeature } = useAccess();
  
  return (
    <div>
      {canNewFeature && (
        <Button>新功能按钮</Button>
      )}
    </div>
  );
};
```

### 2.3 处理新的错误类型

**步骤1: 定义错误类型**
```typescript
// 在错误处理函数中添加新的错误类型
const handleApiError = (error: any) => {
  const { response } = error;
  const { status, data } = response;
  
  switch (status) {
    // 现有错误处理...
    case 422:
      message.error('数据验证失败: ' + data.message);
      break;
    // 其他错误...
  }
};
```

**步骤2: 业务特定错误处理**
```typescript
// 在具体的API调用中处理特定错误
try {
  await AuthService.someMethod();
} catch (error) {
  if (error.code === 'SPECIFIC_ERROR_CODE') {
    // 处理特定错误
    Modal.confirm({
      title: '特定错误处理',
      content: '是否重试？',
      onOk: () => handleRetry(),
    });
  }
  // 其他错误由拦截器处理
}
```

## 3. 调试技巧

### 3.1 Token调试

**查看当前Token信息**:
```javascript
// 在浏览器控制台执行
import { getCurrentTokenInfo } from '@/utils/tokenUtils';
console.log('Token信息:', getCurrentTokenInfo());
```

**检查Token有效性**:
```javascript
import { TokenManager } from '@/utils/request';
const token = TokenManager.getToken();
console.log('Token存在:', !!token);
console.log('Token内容:', token);
```

**模拟Token过期**:
```javascript
// 清除Token测试登录流程
TokenManager.clearToken();
window.location.reload();
```

### 3.2 API调试

**查看网络请求**:
1. 打开浏览器开发者工具
2. 切换到Network标签页
3. 筛选XHR/Fetch请求
4. 查看请求头中的Authorization字段

**模拟API错误**:
```javascript
// 在拦截器中添加调试代码
apiRequest.interceptors.response.use(
  (response) => {
    // 模拟401错误
    if (response.config.url?.includes('/debug-401')) {
      return Promise.reject({ response: { status: 401 } });
    }
    return response;
  }
);
```

### 3.3 状态调试

**查看全局状态**:
```javascript
// 在组件中使用
import { useModel } from 'umi';

const MyComponent = () => {
  const { initialState } = useModel('@@initialState');
  console.log('全局状态:', initialState);
};
```

**监控状态变化**:
```javascript
// 在app.tsx中添加状态监控
export function onStateChange({ matchedRoutes, location, action, initialState }) {
  console.log('状态变化:', { location, initialState });
}
```

## 4. 常见问题解决

### 4.1 登录问题

**问题**: 验证码发送失败
```typescript
// 检查邮箱格式
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
if (!emailRegex.test(email)) {
  message.error('邮箱格式不正确');
  return;
}

// 检查网络连接
try {
  await AuthService.sendVerificationCode({ email });
} catch (error) {
  if (error.code === 'NETWORK_ERROR') {
    message.error('网络连接失败，请检查网络设置');
  }
}
```

**问题**: 登录后状态未更新
```typescript
// 确保在登录成功后更新全局状态
const handleLogin = async (values) => {
  const response = await AuthService.login(values);
  
  // 手动更新状态
  if (setInitialState) {
    setInitialState({
      ...initialState,
      currentUser: response.user,
      currentTeam: response.team,
    });
  }
};
```

### 4.2 权限问题

**问题**: 权限检查不准确
```typescript
// 确保权限检查使用最新的Token信息
const checkPermission = () => {
  // 重新获取Token信息
  const tokenInfo = getCurrentTokenInfo();
  if (!tokenInfo) return false;
  
  // 基于最新Token信息检查权限
  return tokenInfo.role === 'TEAM_CREATOR';
};
```

**问题**: 团队切换后权限未更新
```typescript
// 在团队切换后强制刷新权限
const handleTeamSwitch = async (teamId) => {
  await AuthService.selectTeam({ teamId });
  
  // 刷新全局状态以更新权限
  if (initialState?.fetchUserInfo && initialState?.fetchTeamInfo) {
    const [user, team] = await Promise.all([
      initialState.fetchUserInfo(),
      initialState.fetchTeamInfo(),
    ]);
    
    setInitialState({
      ...initialState,
      currentUser: user,
      currentTeam: team,
    });
  }
};
```

### 4.3 Token问题

**问题**: Token自动刷新失败
```typescript
// 检查刷新Token的实现
const handleTokenRefresh = async () => {
  try {
    const newToken = await AuthService.refreshToken();
    TokenManager.setToken(newToken);
    return newToken;
  } catch (error) {
    // 刷新失败，清除状态并跳转登录
    TokenManager.clearToken();
    window.location.href = '/user/login';
    throw error;
  }
};
```

## 5. 性能优化建议

### 5.1 API调用优化

**避免重复请求**:
```typescript
// 使用缓存避免重复的用户信息请求
let userInfoCache: UserProfileResponse | null = null;
let userInfoPromise: Promise<UserProfileResponse> | null = null;

const getUserInfo = async (): Promise<UserProfileResponse> => {
  if (userInfoCache) return userInfoCache;
  if (userInfoPromise) return userInfoPromise;
  
  userInfoPromise = UserService.getCurrentUser();
  userInfoCache = await userInfoPromise;
  userInfoPromise = null;
  
  return userInfoCache;
};
```

**批量状态更新**:
```typescript
// 批量更新状态而不是多次单独更新
const updateUserAndTeam = async () => {
  const [user, team] = await Promise.all([
    UserService.getCurrentUser(),
    TeamService.getCurrentTeam(),
  ]);
  
  // 一次性更新状态
  setInitialState({
    ...initialState,
    currentUser: user,
    currentTeam: team,
  });
};
```

### 5.2 组件优化

**避免不必要的重渲染**:
```typescript
import { memo, useMemo } from 'react';

const AuthComponent = memo(({ user, team }) => {
  const permissions = useMemo(() => {
    return calculatePermissions(user, team);
  }, [user, team]);
  
  return <div>{/* 组件内容 */}</div>;
});
```

## 6. 测试指南

### 6.1 单元测试

**测试Token工具函数**:
```typescript
import { parseJwtPayload, getTeamIdFromCurrentToken } from '@/utils/tokenUtils';

describe('Token工具函数测试', () => {
  it('应该正确解析JWT Payload', () => {
    const mockToken = 'header.eyJzdWIiOiIxMjMiLCJ0ZWFtSWQiOiI0NTYifQ.signature';
    const payload = parseJwtPayload(mockToken);
    
    expect(payload).toEqual({
      sub: '123',
      teamId: '456'
    });
  });
});
```

**测试API Service**:
```typescript
import { AuthService } from '@/services/auth';

jest.mock('@/utils/request');

describe('AuthService测试', () => {
  it('应该成功调用登录API', async () => {
    const mockResponse = { token: 'test-token', user: { id: 1 } };
    (apiRequest.post as jest.Mock).mockResolvedValue({ data: mockResponse });
    
    const result = await AuthService.login({ email: '<EMAIL>', code: '123456' });
    
    expect(result).toEqual(mockResponse);
  });
});
```

### 6.2 集成测试

**测试完整登录流程**:
```typescript
import { render, fireEvent, waitFor } from '@testing-library/react';
import LoginPage from '@/pages/user/login';

describe('登录页面集成测试', () => {
  it('应该完成完整的登录流程', async () => {
    const { getByPlaceholderText, getByText } = render(<LoginPage />);
    
    // 输入邮箱
    fireEvent.change(getByPlaceholderText('邮箱'), {
      target: { value: '<EMAIL>' }
    });
    
    // 点击发送验证码
    fireEvent.click(getByText('发送验证码'));
    
    // 等待验证码发送成功
    await waitFor(() => {
      expect(getByText('验证码已发送')).toBeInTheDocument();
    });
  });
});
```

## 7. 部署注意事项

### 7.1 环境配置

**生产环境Token安全**:
- 确保使用HTTPS
- 设置合适的Token过期时间
- 配置安全的JWT密钥

**环境变量配置**:
```bash
# .env.production
REACT_APP_API_BASE_URL=https://api.production.com
REACT_APP_TOKEN_STORAGE_KEY=prod_auth_token
```

### 7.2 监控和日志

**添加错误监控**:
```typescript
// 在生产环境中添加错误上报
const handleApiError = (error: any) => {
  // 现有错误处理...
  
  // 生产环境错误上报
  if (process.env.NODE_ENV === 'production') {
    errorReporting.captureException(error);
  }
};
```

---

*最后更新时间: 2025-07-31*
