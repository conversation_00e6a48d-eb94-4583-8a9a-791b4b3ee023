package com.teammanage.exception;

/**
 * 数据库相关异常
 * 
 * 用于处理数据库连接、事务、超时等相关错误
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class DatabaseException extends RuntimeException {

    private final Integer code;
    private final String operation;
    private final String details;

    /**
     * 构造函数
     * 
     * @param message 错误消息
     */
    public DatabaseException(String message) {
        super(message);
        this.code = 500;
        this.operation = null;
        this.details = null;
    }

    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param cause 原因
     */
    public DatabaseException(String message, Throwable cause) {
        super(message, cause);
        this.code = 500;
        this.operation = null;
        this.details = null;
    }

    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param operation 操作类型
     * @param details 详细信息
     */
    public DatabaseException(String message, String operation, String details) {
        super(message);
        this.code = 500;
        this.operation = operation;
        this.details = details;
    }

    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param cause 原因
     * @param operation 操作类型
     * @param details 详细信息
     */
    public DatabaseException(String message, Throwable cause, String operation, String details) {
        super(message, cause);
        this.code = 500;
        this.operation = operation;
        this.details = details;
    }

    // Getter 方法
    public Integer getCode() {
        return code;
    }

    public String getOperation() {
        return operation;
    }

    public String getDetails() {
        return details;
    }

    // 静态工厂方法，用于创建常见的数据库异常

    /**
     * 创建数据库连接失败异常
     * 
     * @param cause 原因
     * @return 异常实例
     */
    public static DatabaseException connectionFailed(Throwable cause) {
        return new DatabaseException(
            "数据库连接失败，请稍后重试",
            cause,
            "CONNECTION",
            "Database connection failed"
        );
    }

    /**
     * 创建数据库超时异常
     * 
     * @param operation 操作类型
     * @param cause 原因
     * @return 异常实例
     */
    public static DatabaseException timeout(String operation, Throwable cause) {
        return new DatabaseException(
            "数据库操作超时，请稍后重试",
            cause,
            operation,
            "Database operation timeout"
        );
    }

    /**
     * 创建事务回滚异常
     * 
     * @param operation 操作类型
     * @param cause 原因
     * @return 异常实例
     */
    public static DatabaseException transactionRollback(String operation, Throwable cause) {
        return new DatabaseException(
            "数据库事务失败，操作已回滚",
            cause,
            operation,
            "Transaction rollback occurred"
        );
    }

    /**
     * 创建连接池耗尽异常
     * 
     * @return 异常实例
     */
    public static DatabaseException connectionPoolExhausted() {
        return new DatabaseException(
            "系统繁忙，请稍后重试",
            "CONNECTION_POOL",
            "Database connection pool exhausted"
        );
    }

    /**
     * 创建数据完整性约束异常
     * 
     * @param constraint 约束名称
     * @param cause 原因
     * @return 异常实例
     */
    public static DatabaseException constraintViolation(String constraint, Throwable cause) {
        return new DatabaseException(
            "数据操作违反完整性约束",
            cause,
            "CONSTRAINT_VIOLATION",
            "Constraint violation: " + constraint
        );
    }

    @Override
    public String toString() {
        return "DatabaseException{" +
                "code=" + code +
                ", operation='" + operation + '\'' +
                ", details='" + details + '\'' +
                ", message='" + getMessage() + '\'' +
                '}';
    }
}
