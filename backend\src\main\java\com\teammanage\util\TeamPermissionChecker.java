package com.teammanage.util;


import org.springframework.stereotype.Component;

import com.teammanage.context.TeamContextHolder;
import com.teammanage.enums.TeamRole;
import com.teammanage.exception.InsufficientPermissionException;

/**
 * 团队权限检查工具类
 *
 * 功能说明：
 * - 提供统一的团队权限验证机制
 * - 基于团队上下文进行权限检查
 * - 支持不同级别的权限控制
 *
 * 权限级别：
 * - 团队管理：只有创建者可以修改团队信息、删除团队
 * - 成员管理：只有创建者可以邀请、移除成员
 * - 数据访问：所有团队成员都可以访问团队数据
 *
 * 使用方式：
 * - 在Service层调用相应的检查方法
 * - 权限不足时抛出InsufficientPermissionException
 * - 配合TeamContextHolder获取当前团队上下文
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class TeamPermissionChecker {

    // ==================== 基于角色的权限检查方法 ====================

    /**
     * 检查指定角色是否可以管理团队
     *
     * @param role 团队角色
     * @return 是否有权限
     */
    public boolean canManageTeam(TeamRole role) {
        return role != null && role.canManageTeam();
    }

    /**
     * 检查指定角色是否可以管理成员
     *
     * @param role 团队角色
     * @return 是否有权限
     */
    public boolean canManageMembers(TeamRole role) {
        return role != null && role.canManageMembers();
    }

    /**
     * 检查指定角色是否可以访问数据
     *
     * @param role 团队角色
     * @return 是否有权限
     */
    public boolean canAccessData(TeamRole role) {
        return role != null && role.canAccessData();
    }

    /**
     * 检查指定角色是否有足够的权限级别
     *
     * @param userRole 用户角色
     * @param requiredRole 所需角色
     * @return 是否有足够权限
     */
    public boolean hasPermissionLevel(TeamRole userRole, TeamRole requiredRole) {
        return userRole != null && requiredRole != null && userRole.hasPermissionLevel(requiredRole);
    }



    // ==================== 基于上下文的权限检查方法 ====================

    /**
     * 检查团队管理权限，如果没有权限则抛出异常
     *
     * @throws InsufficientPermissionException 权限不足异常
     */
    public void checkTeamManagePermission() {
        if (!TeamContextHolder.hasTeamContext()) {
            throw new InsufficientPermissionException("需要团队上下文才能执行此操作");
        }

        TeamRole currentRole = TeamContextHolder.getCurrentUserRole();
        if (!canManageTeam(currentRole)) {
            throw new InsufficientPermissionException("只有团队创建者可以管理团队");
        }
    }

    /**
     * 检查成员管理权限，如果没有权限则抛出异常
     *
     * @throws InsufficientPermissionException 权限不足异常
     */
    public void checkMemberManagePermission() {
        if (!TeamContextHolder.hasTeamContext()) {
            throw new InsufficientPermissionException("需要团队上下文才能执行此操作");
        }

        TeamRole currentRole = TeamContextHolder.getCurrentUserRole();
        if (!canManageMembers(currentRole)) {
            throw new InsufficientPermissionException("只有团队创建者可以管理成员");
        }
    }

    /**
     * 检查指定权限级别，如果没有权限则抛出异常
     *
     * @param requiredRole 所需的最低角色
     * @throws InsufficientPermissionException 权限不足异常
     */
    public void checkPermissionLevel(TeamRole requiredRole) {
        if (!TeamContextHolder.hasTeamContext()) {
            throw new InsufficientPermissionException("需要团队上下文才能执行此操作");
        }

        TeamRole currentRole = TeamContextHolder.getCurrentUserRole();
        if (!hasPermissionLevel(currentRole, requiredRole)) {
            throw new InsufficientPermissionException("权限不足，需要 " + requiredRole.getDisplayName() + " 或更高权限");
        }
    }

    /**
     * 检查数据访问权限，如果没有权限则抛出异常
     * 
     * @throws InsufficientPermissionException 权限不足异常
     */
    public void checkDataAccessPermission() {
        if (!TeamContextHolder.hasTeamContext()) {
            throw new InsufficientPermissionException("需要团队上下文才能访问数据");
        }
        // 所有团队成员都可以访问数据，无需额外检查
    }

    /**
     * 检查是否为指定团队的成员
     * 
     * @param teamId 团队ID
     * @throws InsufficientPermissionException 权限不足异常
     */
    public void checkTeamMembership(Long teamId) {
        if (!TeamContextHolder.hasTeamContext()) {
            throw new InsufficientPermissionException("需要团队上下文才能执行此操作");
        }
        
        Long currentTeamId = TeamContextHolder.getCurrentTeamId();
        if (!teamId.equals(currentTeamId)) {
            throw new InsufficientPermissionException("无权访问指定团队的数据");
        }
    }

    /**
     * 检查是否为指定用户本人或团队创建者
     *
     * @param targetUserId 目标用户ID
     * @throws InsufficientPermissionException 权限不足异常
     */
    public void checkUserAccessPermission(Long targetUserId) {
        if (!TeamContextHolder.hasTeamContext()) {
            throw new InsufficientPermissionException("需要团队上下文才能执行此操作");
        }

        Long currentUserId = TeamContextHolder.getCurrentUserId();
        TeamRole currentRole = TeamContextHolder.getCurrentUserRole();
        boolean isCreator = currentRole == TeamRole.TEAM_CREATOR;

        if (!targetUserId.equals(currentUserId) && !isCreator) {
            throw new InsufficientPermissionException("只能访问自己的信息或需要创建者权限");
        }
    }

}
