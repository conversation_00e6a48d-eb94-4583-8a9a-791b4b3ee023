package com.teammanage.dto.response;

/**
 * 通过邀请链接接受邀请响应DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class AcceptInvitationByLinkResponse {

    /**
     * 处理结果
     */
    private Boolean success;

    /**
     * 团队ID
     */
    private Long teamId;

    /**
     * 团队名称
     */
    private String teamName;

    /**
     * 用户ID（新注册用户或现有用户）
     */
    private Long userId;

    /**
     * 是否为新注册用户
     */
    private Boolean isNewUser;

    /**
     * 后续操作指引
     */
    private String nextAction;

    /**
     * 访问令牌（如果需要自动登录）
     */
    private String accessToken;

    /**
     * 错误消息（如果失败）
     */
    private String errorMessage;

    // Getter and Setter methods
    public Boolean getSuccess() { return success; }
    public void setSuccess(Boolean success) { this.success = success; }

    public Long getTeamId() { return teamId; }
    public void setTeamId(Long teamId) { this.teamId = teamId; }

    public String getTeamName() { return teamName; }
    public void setTeamName(String teamName) { this.teamName = teamName; }

    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public Boolean getIsNewUser() { return isNewUser; }
    public void setIsNewUser(Boolean isNewUser) { this.isNewUser = isNewUser; }

    public String getNextAction() { return nextAction; }
    public void setNextAction(String nextAction) { this.nextAction = nextAction; }

    public String getAccessToken() { return accessToken; }
    public void setAccessToken(String accessToken) { this.accessToken = accessToken; }

    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
}
