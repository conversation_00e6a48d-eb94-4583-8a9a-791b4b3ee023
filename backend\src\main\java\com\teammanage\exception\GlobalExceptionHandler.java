package com.teammanage.exception;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.teammanage.common.ApiResponse;
import com.teammanage.service.ErrorMonitoringService;
import com.teammanage.util.SecurityUtil;

/**
 * 全局异常处理器
 *
 * 统一异常处理规范：
 * 1. 所有API响应的HTTP状态码统一返回200 OK
 * 2. 真正的成功/失败状态通过ApiResponse中的code字段表示
 * 3. 成功：code = 200，业务异常：code = 400/404/403等，系统异常：code = 500
 * 4. 保持响应格式一致性，包含code、message、data、timestamp字段
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    private static final String ERROR_MONITORING_FAILED = "错误监控记录失败";
    private static final String STACK_TRACE_KEY = "stackTrace";

    @Autowired
    private ErrorMonitoringService errorMonitoringService;

    /**
     * 处理业务异常
     * HTTP状态码：200 OK，业务状态码在响应体中
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.OK)
    public ApiResponse<Object> handleBusinessException(BusinessException e) {
        log.warn("业务异常: {}", e.getMessage());

        // 记录错误监控信息
        try {
            Long userId = SecurityUtil.getCurrentUserId();
            Map<String, Object> additionalInfo = new HashMap<>();
            additionalInfo.put("exceptionClass", e.getClass().getSimpleName());
            errorMonitoringService.recordBusinessException(e, userId, "UNKNOWN", additionalInfo);
        } catch (Exception monitoringException) {
            log.warn(ERROR_MONITORING_FAILED, monitoringException);
        }

        return ApiResponse.error(e.getCode(), e.getMessage());
    }

    /**
     * 处理资源不存在异常
     * HTTP状态码：200 OK，业务状态码在响应体中
     */
    @ExceptionHandler(ResourceNotFoundException.class)
    @ResponseStatus(HttpStatus.OK)
    public ApiResponse<Object> handleResourceNotFoundException(ResourceNotFoundException e) {
        log.warn("资源不存在: {}", e.getMessage());
        return ApiResponse.notFound(e.getMessage());
    }

    /**
     * 处理权限不足异常
     * HTTP状态码：200 OK，业务状态码在响应体中
     */
    @ExceptionHandler(InsufficientPermissionException.class)
    @ResponseStatus(HttpStatus.OK)
    public ApiResponse<Object> handleInsufficientPermissionException(InsufficientPermissionException e) {
        log.warn("权限不足: {}", e.getMessage());
        return ApiResponse.forbidden(e.getMessage());
    }

    /**
     * 处理认证异常
     * HTTP状态码：200 OK，业务状态码在响应体中
     */
    @ExceptionHandler(AuthenticationException.class)
    @ResponseStatus(HttpStatus.OK)
    public ApiResponse<Object> handleAuthenticationException(AuthenticationException e) {
        log.warn("认证失败: {}", e.getMessage());
        return ApiResponse.unauthorized("认证失败，请重新登录");
    }

    /**
     * 处理团队访问被拒绝异常
     * HTTP状态码：200 OK，业务状态码在响应体中
     */
    @ExceptionHandler(TeamAccessDeniedException.class)
    @ResponseStatus(HttpStatus.OK)
    public ApiResponse<Object> handleTeamAccessDeniedException(TeamAccessDeniedException e) {
        log.warn("团队访问被拒绝: teamId={}, userId={}, reason={}, message={}",
                e.getTeamId(), e.getUserId(), e.getReason(), e.getMessage());
        return ApiResponse.error(e.getCode(), e.getMessage());
    }

    /**
     * 处理访问拒绝异常
     * HTTP状态码：200 OK，业务状态码在响应体中
     */
    @ExceptionHandler(AccessDeniedException.class)
    @ResponseStatus(HttpStatus.OK)
    public ApiResponse<Object> handleAccessDeniedException(AccessDeniedException e) {
        log.warn("访问被拒绝: {}", e.getMessage());
        return ApiResponse.forbidden("访问被拒绝，权限不足");
    }

    /**
     * 处理参数验证异常
     * HTTP状态码：200 OK，业务状态码在响应体中
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.OK)
    public ApiResponse<Object> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        Map<String, String> errors = new HashMap<>();
        e.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });

        log.warn("参数验证失败: {}", errors);
        return ApiResponse.badRequest("参数验证失败: " + errors.toString());
    }

    /**
     * 处理绑定异常
     * HTTP状态码：200 OK，业务状态码在响应体中
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.OK)
    public ApiResponse<Object> handleBindException(BindException e) {
        Map<String, String> errors = new HashMap<>();
        e.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });

        log.warn("参数绑定失败: {}", errors);
        return ApiResponse.badRequest("参数绑定失败: " + errors.toString());
    }

    /**
     * 处理非法参数异常
     * HTTP状态码：200 OK，业务状态码在响应体中
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.OK)
    public ApiResponse<Object> handleIllegalArgumentException(IllegalArgumentException e) {
        log.warn("非法参数: {}", e.getMessage());
        return ApiResponse.badRequest("参数错误: " + e.getMessage());
    }

    /**
     * 处理空指针异常
     * HTTP状态码：200 OK，业务状态码在响应体中
     */
    @ExceptionHandler(NullPointerException.class)
    @ResponseStatus(HttpStatus.OK)
    public ApiResponse<Object> handleNullPointerException(NullPointerException e) {
        log.error("空指针异常", e);
        return ApiResponse.error("系统内部错误，请联系管理员");
    }

    /**
     * 处理数据库异常
     * HTTP状态码：200 OK，业务状态码在响应体中
     */
    @ExceptionHandler(DatabaseException.class)
    @ResponseStatus(HttpStatus.OK)
    public ApiResponse<Object> handleDatabaseException(DatabaseException e) {
        log.error("数据库异常: operation={}, details={}", e.getOperation(), e.getDetails(), e);

        // 记录错误监控信息
        try {
            Long userId = SecurityUtil.getCurrentUserId();
            Map<String, Object> additionalInfo = new HashMap<>();
            additionalInfo.put(STACK_TRACE_KEY, getStackTraceString(e));
            errorMonitoringService.recordDatabaseException(e, userId, "DATABASE_OPERATION", additionalInfo);
        } catch (Exception monitoringException) {
            log.warn(ERROR_MONITORING_FAILED, monitoringException);
        }

        return ApiResponse.error(e.getCode(), e.getMessage());
    }

    /**
     * 处理网络异常
     * HTTP状态码：200 OK，业务状态码在响应体中
     */
    @ExceptionHandler(NetworkException.class)
    @ResponseStatus(HttpStatus.OK)
    public ApiResponse<Object> handleNetworkException(NetworkException e) {
        log.error("网络异常: service={}, operation={}, timeout={}",
                e.getService(), e.getOperation(), e.getTimeout(), e);

        // 记录错误监控信息
        try {
            Long userId = SecurityUtil.getCurrentUserId();
            Map<String, Object> additionalInfo = new HashMap<>();
            additionalInfo.put(STACK_TRACE_KEY, getStackTraceString(e));
            errorMonitoringService.recordNetworkException(e, userId, "NETWORK_OPERATION", additionalInfo);
        } catch (Exception monitoringException) {
            log.warn(ERROR_MONITORING_FAILED, monitoringException);
        }

        return ApiResponse.error(e.getCode(), e.getMessage());
    }

    /**
     * 处理速率限制异常
     * HTTP状态码：200 OK，业务状态码在响应体中
     */
    @ExceptionHandler(RateLimitException.class)
    @ResponseStatus(HttpStatus.OK)
    public ApiResponse<Object> handleRateLimitException(RateLimitException e) {
        log.warn("速率限制异常: limitType={}, retryAfter={}, currentCount={}, maxCount={}",
                e.getLimitType(), e.getRetryAfter(), e.getCurrentCount(), e.getMaxCount());

        // 记录错误监控信息
        try {
            Long userId = SecurityUtil.getCurrentUserId();
            Map<String, Object> additionalInfo = new HashMap<>();
            additionalInfo.put("userAgent", "N/A"); // 可以从HttpServletRequest获取
            errorMonitoringService.recordRateLimitException(e, userId, "RATE_LIMIT_CHECK", additionalInfo);
        } catch (Exception monitoringException) {
            log.warn(ERROR_MONITORING_FAILED, monitoringException);
        }

        // 添加重试时间到响应头信息
        Map<String, Object> data = new HashMap<>();
        data.put("retryAfter", e.getRetryAfter());
        data.put("limitType", e.getLimitType());
        data.put("currentCount", e.getCurrentCount());
        data.put("maxCount", e.getMaxCount());

        ApiResponse<Object> response = ApiResponse.error(e.getCode(), e.getMessage());
        response.setData(data);
        return response;
    }

    /**
     * 处理其他未知异常
     * HTTP状态码：200 OK，业务状态码在响应体中
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.OK)
    public ApiResponse<Object> handleException(Exception e) {
        log.error("系统异常", e);

        // 记录错误监控信息
        try {
            Long userId = SecurityUtil.getCurrentUserId();
            Map<String, Object> additionalInfo = new HashMap<>();
            additionalInfo.put(STACK_TRACE_KEY, getStackTraceString(e));
            additionalInfo.put("exceptionClass", e.getClass().getSimpleName());
            errorMonitoringService.recordSystemException(e, userId, "SYSTEM_ERROR", additionalInfo);
        } catch (Exception monitoringException) {
            log.warn(ERROR_MONITORING_FAILED, monitoringException);
        }

        return ApiResponse.error("系统内部错误，请联系管理员");
    }

    /**
     * 获取异常堆栈跟踪字符串
     *
     * @param e 异常
     * @return 堆栈跟踪字符串
     */
    private String getStackTraceString(Exception e) {
        if (e == null) {
            return "N/A";
        }

        java.io.StringWriter sw = new java.io.StringWriter();
        java.io.PrintWriter pw = new java.io.PrintWriter(sw);
        e.printStackTrace(pw);
        return sw.toString();
    }

}
