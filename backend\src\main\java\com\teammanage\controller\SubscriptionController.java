package com.teammanage.controller;

import com.teammanage.common.ApiResponse;
import com.teammanage.dto.request.CreateSubscriptionRequest;
import com.teammanage.dto.response.SubscriptionPlanResponse;
import com.teammanage.dto.response.SubscriptionResponse;
import com.teammanage.service.SubscriptionService;
import com.teammanage.util.SecurityUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 订阅控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@Tag(name = "订阅管理", description = "订阅套餐管理相关接口")
public class SubscriptionController {

    private static final Logger log = LoggerFactory.getLogger(SubscriptionController.class);

    @Autowired
    private SubscriptionService subscriptionService;

    /**
     * 获取所有订阅套餐（公开接口）
     */
    @GetMapping("/plans")
    @Operation(summary = "获取订阅套餐", description = "获取所有可用的订阅套餐列表")
    public ApiResponse<List<SubscriptionPlanResponse>> getAllPlans() {
        List<SubscriptionPlanResponse> response = subscriptionService.getAllPlans();
        return ApiResponse.success(response);
    }



    /**
     * 创建订阅（需要Account Token）
     */
    @PostMapping("/subscriptions")
    @Operation(summary = "创建订阅", description = "购买订阅套餐，需要Account Token")
    @SecurityRequirement(name = "bearerAuth")
    public ApiResponse<SubscriptionResponse> createSubscription(@Valid @RequestBody CreateSubscriptionRequest request) {
        Long userId = SecurityUtil.getCurrentUserId();
        SubscriptionResponse response = subscriptionService.createSubscription(request, userId);
        return ApiResponse.success("订阅创建成功", response);
    }

    /**
     * 获取用户订阅历史（需要Account Token）
     */
    @GetMapping("/subscriptions")
    @Operation(summary = "获取订阅历史", description = "获取当前用户的订阅历史，需要Account Token")
    @SecurityRequirement(name = "bearerAuth")
    public ApiResponse<List<SubscriptionResponse>> getUserSubscriptions() {
        Long userId = SecurityUtil.getCurrentUserId();
        List<SubscriptionResponse> response = subscriptionService.getUserSubscriptions(userId);
        return ApiResponse.success(response);
    }

    /**
     * 获取当前有效订阅（需要Account Token）
     */
    @GetMapping("/subscriptions/current")
    @Operation(summary = "获取当前订阅", description = "获取当前用户的有效订阅，需要Account Token")
    @SecurityRequirement(name = "bearerAuth")
    public ApiResponse<SubscriptionResponse> getCurrentSubscription() {
        Long userId = SecurityUtil.getCurrentUserId();
        SubscriptionResponse response = subscriptionService.getCurrentSubscription(userId);
        return ApiResponse.success(response);
    }

    /**
     * 取消订阅（需要Account Token）
     */
    @DeleteMapping("/subscriptions/{subscriptionId}")
    @Operation(summary = "取消订阅", description = "取消指定的订阅，需要Account Token")
    @SecurityRequirement(name = "bearerAuth")
    public ApiResponse<Void> cancelSubscription(
            @Parameter(description = "订阅ID") @PathVariable Long subscriptionId) {
        Long userId = SecurityUtil.getCurrentUserId();
        subscriptionService.cancelSubscription(subscriptionId, userId);
        return ApiResponse.<Void>success("订阅取消成功", null);
    }

}
