package com.teammanage.dto.response;

import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 分页响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class PageResponse<T> {

    /**
     * 数据列表
     */
    private List<T> list;

    /**
     * 总记录数
     */
    private long total;

    /**
     * 当前页码
     */
    private long current;

    /**
     * 每页大小
     */
    private long pageSize;

    public PageResponse() {}

    public PageResponse(List<T> list, long total, long current, long pageSize) {
        this.list = list;
        this.total = total;
        this.current = current;
        this.pageSize = pageSize;
    }

    /**
     * 从MyBatis Plus的IPage转换
     */
    public static <T> PageResponse<T> fromPage(IPage<T> page) {
        return new PageResponse<>(
            page.getRecords(),
            page.getTotal(),
            page.getCurrent(),
            page.getSize()
        );
    }

    // Getter and Setter methods
    public List<T> getList() { 
        return list; 
    }
    
    public void setList(List<T> list) { 
        this.list = list; 
    }

    public long getTotal() { 
        return total; 
    }
    
    public void setTotal(long total) { 
        this.total = total; 
    }

    public long getCurrent() { 
        return current; 
    }
    
    public void setCurrent(long current) { 
        this.current = current; 
    }

    public long getPageSize() { 
        return pageSize; 
    }
    
    public void setPageSize(long pageSize) { 
        this.pageSize = pageSize; 
    }
}
