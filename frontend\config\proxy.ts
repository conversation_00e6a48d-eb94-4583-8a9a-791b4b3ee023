/**
 * @name 代理的配置
 * @see 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * -------------------------------
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 *
 * @doc https://umijs.org/docs/guides/proxy
 */
export default {
  // 生产环境代理配置
  dev: {
    // localhost:8000/api/** -> http://localhost:8080/api/**
    '/api/': {
      // 要代理的地址 - 后端服务地址
      target: 'http://localhost:8080',
      // 配置了这个可以从 http 代理到 https
      // 依赖 origin 的功能可能需要这个，比如 cookie
      changeOrigin: true,
      // 路径重写，保持原路径
      pathRewrite: { '^/api/': '/api/v1/' },
    },
  },
};
