package com.teammanage.security;

import java.io.IOException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.teammanage.common.ApiResponse;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * JWT访问拒绝处理器
 * 
 * 当用户已认证但权限不足时，统一返回JSON格式的错误响应
 * HTTP状态码统一返回200，业务状态码在响应体中处理
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class JwtAccessDeniedHandler implements AccessDeniedHandler {

    private static final Logger log = LoggerFactory.getLogger(JwtAccessDeniedHandler.class);

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response,
                      AccessDeniedException accessDeniedException) throws IOException, ServletException {
        
        log.warn("访问被拒绝: {} {}, 原因: {}", request.getMethod(), request.getRequestURI(), 
                accessDeniedException.getMessage());
        
        response.setContentType("application/json;charset=UTF-8");
        // 统一返回HTTP 200状态码，业务状态码在响应体中
        response.setStatus(HttpServletResponse.SC_OK);
        
        ApiResponse<Object> apiResponse = ApiResponse.error(403, "访问被拒绝，权限不足");
        
        response.getWriter().write(objectMapper.writeValueAsString(apiResponse));
    }

}
