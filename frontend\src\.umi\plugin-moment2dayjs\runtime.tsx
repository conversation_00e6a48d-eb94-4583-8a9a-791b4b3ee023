// @ts-nocheck
// This file is generated by <PERSON><PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import dayjs from 'F:/Project/teamAuth/frontend/node_modules/dayjs';
import antdPlugin from 'F:/Project/teamAuth/frontend/node_modules/antd-dayjs-webpack-plugin/src/antd-plugin.js';

import isSameOrBefore from 'F:/Project/teamAuth/frontend/node_modules/dayjs/plugin/isSameOrBefore';
import isSameOrAfter from 'F:/Project/teamAuth/frontend/node_modules/dayjs/plugin/isSameOrAfter';
import advancedFormat from 'F:/Project/teamAuth/frontend/node_modules/dayjs/plugin/advancedFormat';
import customParseFormat from 'F:/Project/teamAuth/frontend/node_modules/dayjs/plugin/customParseFormat';
import weekday from 'F:/Project/teamAuth/frontend/node_modules/dayjs/plugin/weekday';
import weekYear from 'F:/Project/teamAuth/frontend/node_modules/dayjs/plugin/weekYear';
import weekOfYear from 'F:/Project/teamAuth/frontend/node_modules/dayjs/plugin/weekOfYear';
import isMoment from 'F:/Project/teamAuth/frontend/node_modules/dayjs/plugin/isMoment';
import localeData from 'F:/Project/teamAuth/frontend/node_modules/dayjs/plugin/localeData';
import localizedFormat from 'F:/Project/teamAuth/frontend/node_modules/dayjs/plugin/localizedFormat';
import duration from 'F:/Project/teamAuth/frontend/node_modules/dayjs/plugin/duration';

dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);
dayjs.extend(advancedFormat);
dayjs.extend(customParseFormat);
dayjs.extend(weekday);
dayjs.extend(weekYear);
dayjs.extend(weekOfYear);
dayjs.extend(isMoment);
dayjs.extend(localeData);
dayjs.extend(localizedFormat);
dayjs.extend(duration);

dayjs.extend(antdPlugin);
