package com.teammanage;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 团队管理系统主应用类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootApplication
@MapperScan("com.teammanage.mapper")
@EnableAsync
@EnableScheduling
public class TeamManageApplication {

    public static void main(String[] args) {
        SpringApplication.run(TeamManageApplication.class, args);
    }

}
