package com.teammanage.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.teammanage.common.ApiResponse;
import com.teammanage.dto.request.LoginRequest;

import com.teammanage.dto.request.SelectTeamRequest;
import com.teammanage.dto.request.SendVerificationCodeRequest;
import com.teammanage.dto.response.LoginResponse;
import com.teammanage.dto.response.SendVerificationCodeResponse;
import com.teammanage.service.AuthService;
import com.teammanage.service.VerificationCodeService;
import com.teammanage.util.JwtTokenUtil;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

/**
 * 认证控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/auth")
@Tag(name = "认证管理", description = "用户认证相关接口")
public class AuthController {

    @Autowired
    private AuthService authService;

    @Autowired
    private VerificationCodeService verificationCodeService;

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    /**
     * 发送验证码
     *
     * <p>向指定邮箱发送验证码，用于用户登录或注册。系统会根据邮箱是否已注册自动判断发送登录验证码还是注册验证码。</p>
     *
     * <h3>功能特性：</h3>
     * <ul>
     *   <li>自动识别邮箱是否已注册，发送相应类型的验证码</li>
     *   <li>验证码有效期为5分钟</li>
     *   <li>同一邮箱60秒内只能发送一次验证码（防刷机制）</li>
     *   <li>验证码为6位数字，随机生成</li>
     * </ul>
     *
     * <h3>业务流程：</h3>
     * <ol>
     *   <li>验证邮箱格式是否正确</li>
     *   <li>检查发送频率限制（60秒内不能重复发送）</li>
     *   <li>查询邮箱是否已注册，确定验证码类型</li>
     *   <li>生成6位随机验证码</li>
     *   <li>将验证码存储到缓存（Redis），设置5分钟过期时间</li>
     *   <li>发送邮件到指定邮箱</li>
     *   <li>返回发送结果</li>
     * </ol>
     *
     * @param request 发送验证码请求，包含目标邮箱地址
     * @return 验证码发送结果，包含是否成功、消息提示等信息
     * @throws IllegalArgumentException 当邮箱格式不正确时抛出
     * @throws RuntimeException 当发送过于频繁或邮件服务异常时抛出
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    @PostMapping("/send-code")
    @Operation(summary = "发送验证码", description = "发送登录或注册验证码到邮箱")
    @io.swagger.v3.oas.annotations.responses.ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "验证码发送成功",
            content = @Content(schema = @Schema(implementation = SendVerificationCodeResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400",
            description = "请求参数错误"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "429",
            description = "发送过于频繁"
        )
    })
    public com.teammanage.common.ApiResponse<SendVerificationCodeResponse> sendVerificationCode(
            @Parameter(description = "发送验证码请求", required = true)
            @Valid @RequestBody SendVerificationCodeRequest request) {
        SendVerificationCodeResponse response = verificationCodeService.sendVerificationCode(request);
        return com.teammanage.common.ApiResponse.success("验证码发送请求处理完成", response);
    }



    /**
     * 用户登录（验证码登录）
     *
     * <p>使用邮箱和验证码进行用户登录。系统采用验证码登录方式，无需密码，更加安全便捷。
     * 登录成功后返回Account Token和用户所属的团队列表。</p>
     *
     * <h3>登录流程：</h3>
     * <ol>
     *   <li>验证请求参数（邮箱格式、验证码格式）</li>
     *   <li>从缓存中获取该邮箱对应的验证码</li>
     *   <li>验证验证码是否正确且未过期</li>
     *   <li>根据邮箱查询用户信息，如果用户不存在则自动注册</li>
     *   <li>生成Account Token（包含用户基本信息，不包含团队信息）</li>
     *   <li>查询用户所属的所有团队</li>
     *   <li>记录登录日志（IP地址、登录时间等）</li>
     *   <li>清除已使用的验证码</li>
     *   <li>返回登录结果</li>
     * </ol>
     *
     * <h3>Token说明：</h3>
     * <ul>
     *   <li><strong>Account Token</strong>：包含用户基本信息，用于访问用户相关接口</li>
     *   <li>有效期：7天（可通过refresh-token接口刷新）</li>
     *   <li>不包含团队信息，需要通过select-team接口选择团队获取Team Token</li>
     * </ul>
     *
     * @param request 登录请求，包含邮箱和验证码
     * @param httpRequest HTTP请求对象，用于获取客户端IP等信息
     * @return 登录响应，包含Account Token、用户信息和团队列表
     * @throws IllegalArgumentException 当邮箱格式不正确或验证码格式错误时抛出
     * @throws RuntimeException 当验证码错误、已过期或用户状态异常时抛出
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户验证码登录，验证验证码并返回用户Token和团队列表")
    @io.swagger.v3.oas.annotations.responses.ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "登录成功",
            content = @Content(schema = @Schema(implementation = LoginResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400",
            description = "验证码错误或已过期"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "404",
            description = "用户不存在"
        )
    })
    public com.teammanage.common.ApiResponse<LoginResponse> login(
            @Parameter(description = "登录请求（包含邮箱和验证码）", required = true)
            @Valid @RequestBody LoginRequest request,
            HttpServletRequest httpRequest) {
        LoginResponse response = authService.login(request, httpRequest);
        return com.teammanage.common.ApiResponse.success("登录成功", response);
    }



    /**
     * 刷新Token
     *
     * <p>刷新即将过期的Token，延长用户会话时间。支持刷新Account Token和Team Token。</p>
     *
     * <h3>刷新机制：</h3>
     * <ul>
     *   <li>Token在过期前24小时内可以刷新</li>
     *   <li>刷新后生成新的Token，旧Token立即失效</li>
     *   <li>保持原有的用户信息和团队上下文</li>
     *   <li>重置Token有效期为7天</li>
     * </ul>
     *
     * <h3>安全特性：</h3>
     * <ul>
     *   <li>每个Token只能刷新一次，防止Token被滥用</li>
     *   <li>刷新时验证Token的完整性和有效性</li>
     *   <li>记录Token刷新日志，便于安全审计</li>
     * </ul>
     *
     * @param httpRequest HTTP请求对象，从Authorization头获取当前Token
     * @return 刷新后的Token信息，包含新的Token和用户信息
     * @throws IllegalArgumentException 当Token格式不正确时抛出
     * @throws RuntimeException 当Token已过期、已失效或刷新次数超限时抛出
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    @PostMapping("/refresh-token")
    @Operation(summary = "刷新Token", description = "刷新即将过期的Token")
    public ApiResponse<LoginResponse> refreshToken(HttpServletRequest httpRequest) {
        String currentToken = getTokenFromRequest(httpRequest);
        LoginResponse response = authService.refreshToken(currentToken);
        return ApiResponse.success("Token刷新成功", response);
    }

    /**
     * 选择团队
     */
    @PostMapping("/select-team")
    @Operation(summary = "选择团队", description = "选择团队并获取包含团队信息的Token")
    public ApiResponse<LoginResponse> selectTeam(@RequestBody SelectTeamRequest request,
                                               HttpServletRequest httpRequest) {
        String currentToken = getTokenFromRequest(httpRequest);
        LoginResponse response = authService.selectTeam(request.getTeamId(), currentToken);
        return ApiResponse.success("团队选择成功", response);
    }

    /**
     * 切换团队
     */
    @PostMapping("/switch-team")
    @Operation(summary = "切换团队", description = "切换到其他团队")
    public ApiResponse<LoginResponse> switchTeam(@RequestBody SelectTeamRequest request,
                                               HttpServletRequest httpRequest) {
        String currentToken = getTokenFromRequest(httpRequest);
        LoginResponse response = authService.switchTeam(request.getTeamId(), currentToken);
        return ApiResponse.success("团队切换成功", response);
    }

    /**
     * 清除团队上下文
     */
    @PostMapping("/clear-team")
    @Operation(summary = "清除团队上下文", description = "清除当前团队上下文")
    public ApiResponse<String> clearTeam(HttpServletRequest httpRequest) {
        String currentToken = getTokenFromRequest(httpRequest);
        String newToken = authService.clearTeam(currentToken);
        return ApiResponse.success("团队上下文已清除", newToken);
    }

    // ========== 兼容性端点 ==========



    /**
     * 登出
     */
    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "用户登出，使Token失效")
    public ApiResponse<Void> logout(HttpServletRequest httpRequest) {
        String token = getTokenFromRequest(httpRequest);
        authService.logout(token);
        return ApiResponse.<Void>success("登出成功", null);
    }

    /**
     * 验证Token
     */
    @GetMapping("/validate")
    @Operation(summary = "验证Token", description = "验证当前Token是否有效")
    public ApiResponse<Boolean> validateToken(HttpServletRequest httpRequest) {
        String token = getTokenFromRequest(httpRequest);
        if (!StringUtils.hasText(token)) {
            return ApiResponse.success("Token验证结果", false);
        }
        
        boolean isValid = jwtTokenUtil.validateToken(token);
        return ApiResponse.success("Token验证结果", isValid);
    }

    /**
     * 从请求中获取Token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }

}
