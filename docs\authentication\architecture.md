# 身份验证系统架构

## 概述

团队管理应用采用基于JWT的身份验证系统，支持邮箱验证码登录和多团队切换功能。系统设计遵循单Token原则，一个Token同时包含用户身份和团队上下文信息。

## 核心设计原则

### 1. 单Token系统
- **设计理念**: 使用一个JWT Token同时承载用户身份和团队上下文
- **优势**: 简化Token管理，减少存储复杂度，提高性能
- **Token内容**: 用户ID、团队ID、角色信息、过期时间等

### 2. 验证码认证
- **认证方式**: 邮箱 + 6位数字验证码
- **自动注册**: 新邮箱自动创建用户账户
- **安全性**: 验证码有效期5分钟，支持重发机制

### 3. 多租户架构
- **团队隔离**: 每个团队的数据完全隔离
- **角色权限**: 基于团队角色的细粒度权限控制
- **团队切换**: 支持用户在多个团队间无缝切换

## 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        前端应用                              │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │   登录页面   │  │  个人中心   │  │  团队管理   │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ AuthService │  │ TokenManager│  │ TokenUtils  │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │  路由守卫   │  │  权限控制   │  │  状态管理   │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
                              │
                              │ HTTP/HTTPS
                              │
┌─────────────────────────────────────────────────────────────┐
│                        后端服务                              │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ AuthController│ │TeamController│ │UserController│         │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ AuthService │  │ TeamService │  │ UserService │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ JWT工具类   │  │  邮件服务   │  │  权限验证   │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │   用户表    │  │   团队表    │  │  成员关系表  │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

## 核心组件

### 前端组件

#### 1. AuthService (认证服务)
- **位置**: `frontend/src/services/auth.ts`
- **职责**: 处理所有认证相关的API调用
- **主要方法**:
  - `sendVerificationCode()`: 发送验证码
  - `login()`: 用户登录
  - `selectTeam()`: 选择团队
  - `refreshToken()`: 刷新Token
  - `logout()`: 用户登出

#### 2. TokenManager (Token管理器)
- **位置**: `frontend/src/utils/request.ts`
- **职责**: 管理JWT Token的存储和生命周期
- **功能**:
  - Token的localStorage存储
  - 请求拦截器中的Token注入
  - 响应拦截器中的Token更新
  - Token过期处理

#### 3. TokenUtils (Token工具)
- **位置**: `frontend/src/utils/tokenUtils.ts`
- **职责**: JWT Token的解析和信息提取
- **功能**:
  - JWT payload解析
  - 用户信息提取
  - 团队信息提取
  - Token有效性检查

#### 4. 路由守卫
- **位置**: `frontend/src/app.tsx`
- **职责**: 页面访问权限控制
- **功能**:
  - 用户登录状态检查
  - 团队选择状态验证
  - 自动重定向处理

#### 5. 权限控制
- **位置**: `frontend/src/access.ts`
- **职责**: 基于角色的权限判断
- **功能**:
  - 角色权限映射
  - 动态权限检查
  - 权限级别比较

### 后端组件

#### 1. AuthController (认证控制器)
- **职责**: 处理认证相关的HTTP请求
- **端点**:
  - `POST /auth/send-code`: 发送验证码
  - `POST /auth/login`: 用户登录
  - `POST /auth/select-team`: 选择团队
  - `POST /auth/refresh`: 刷新Token
  - `POST /auth/logout`: 用户登出

#### 2. AuthService (认证服务)
- **职责**: 实现认证业务逻辑
- **功能**:
  - 验证码生成和验证
  - 用户认证和自动注册
  - JWT Token生成和管理
  - 团队权限验证

#### 3. JWT工具类
- **职责**: JWT Token的生成、解析和验证
- **功能**:
  - Token生成和签名
  - Token解析和验证
  - Claims提取和处理

## 数据模型

### JWT Token结构
```json
{
  "sub": "用户ID",
  "teamId": "团队ID",
  "role": "用户角色",
  "iat": "签发时间",
  "exp": "过期时间",
  "jti": "Token唯一标识"
}
```

### 用户状态模型
```typescript
interface UserState {
  currentUser?: UserProfileResponse;
  currentTeam?: TeamDetailResponse;
  isLoggedIn: boolean;
  hasTeam: boolean;
}
```

### 权限模型
```typescript
interface Permissions {
  canManageTeam: boolean;
  canManageMembers: boolean;
  canAccessData: boolean;
  isTeamCreator: boolean;
  isTeamMember: boolean;
}
```

## 安全特性

### 1. Token安全
- **HTTPS传输**: 所有Token传输使用HTTPS加密
- **短期有效**: Token有效期较短，定期刷新
- **安全存储**: 使用localStorage存储，支持自动清理

### 2. 权限控制
- **最小权限原则**: 用户只能访问有权限的资源
- **角色隔离**: 不同角色的权限严格隔离
- **团队隔离**: 团队间数据完全隔离

### 3. 会话管理
- **服务端验证**: 每个请求都验证Token有效性
- **自动登出**: Token过期自动登出
- **并发控制**: 支持多设备登录管理

## 扩展性设计

### 1. 角色扩展
- 支持新增更多团队角色
- 权限级别可配置
- 角色权限映射可扩展

### 2. 认证方式扩展
- 支持添加其他认证方式（手机号、第三方登录等）
- 认证流程可插拔设计
- 多因素认证支持

### 3. 多租户扩展
- 支持更复杂的组织结构
- 跨团队权限管理
- 企业级功能扩展

---

*最后更新时间: 2025-07-31*
