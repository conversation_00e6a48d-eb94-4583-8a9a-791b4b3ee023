globalThis.makoModuleHotUpdate('src/pages/personal-center/index.tsx', {
    modules: {
        "src/pages/personal-center/index.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/react/index.js"));
            var _FloatButton = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/components/FloatButton/index.tsx"));
            var _TeamListCard = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/TeamListCard.tsx"));
            var _TodoManagement = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/TodoManagement.tsx"));
            var _PersonalInfo = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/PersonalInfo.tsx"));
            var _DataOverview = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/DataOverview.tsx"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            /**
 * 个人中心页面组件
 *
 * 这是用户的个人中心主页面，提供用户信息管理、团队管理、待办事项等功能。
 * 是用户进行个人设置和团队操作的主要入口页面。
 *
 * 页面功能：
 * 1. 用户个人信息展示和编辑
 * 2. 团队列表显示和团队切换
 * 3. 个人待办事项管理
 * 4. 全局浮动操作按钮
 *
 * 页面结构：
 * - 左列：个人信息、团队列表（响应式布局）
 * - 右列：待办事项管理（响应式布局）
 * - 数据概览：独立的水平卡片组件，位于个人信息下方
 * - 浮动：全局操作按钮
 *
 * 权限控制：
 * - 需要用户登录才能访问
 * - 自动检查登录状态并重定向
 * - 支持登录状态变化的实时响应
 *
 * 响应式设计：
 * - 移动端：垂直堆叠布局
 * - 桌面端：左右分栏布局
 * - 自适应不同屏幕尺寸
 */ const PersonalCenterPage = ()=>{
                _s();
                /**
   * 全局状态管理
   *
   * 从UmiJS全局状态中获取用户信息和加载状态：
   * - initialState: 包含用户和团队信息的全局状态
   * - loading: 全局状态的加载状态
   */ const { initialState, loading } = (0, _max.useModel)('@@initialState');
                /**
   * 加载状态处理
   *
   * 当全局状态正在初始化时，显示加载界面。
   * 这确保了用户在状态加载完成前看到友好的加载提示。
   */ if (loading) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        minHeight: '100vh',
                        background: '#f5f8ff',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center'
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                            size: "large"
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/index.tsx",
                            lineNumber: 68,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginLeft: 16
                            },
                            children: "正在加载用户信息..."
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/index.tsx",
                            lineNumber: 69,
                            columnNumber: 9
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/index.tsx",
                    lineNumber: 59,
                    columnNumber: 7
                }, this);
                /**
   * 登录状态检查已由应用级路由守卫处理
   *
   * 移除页面级别的登录检查，避免与应用级路由守卫冲突。
   * 应用级路由守卫在 app.tsx 中的 onPageChange 函数已经处理了
   * 用户登录状态检查和重定向逻辑，无需在页面组件中重复检查。
   *
   * 这样可以避免登录成功后的状态更新时序问题，确保用户
   * 一次登录成功后能够正常访问个人中心页面。
   */ return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                minHeight: '100vh',
                                background: '#f5f8ff',
                                padding: '12px 12px 24px 12px'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                                style: {
                                    width: '100%',
                                    minHeight: 'calc(100vh - 48px)',
                                    borderRadius: '12px',
                                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                                },
                                bodyStyle: {
                                    padding: '24px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_DataOverview.default, {}, void 0, false, {
                                        fileName: "src/pages/personal-center/index.tsx",
                                        lineNumber: 116,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                        gutter: [
                                            24,
                                            12
                                        ],
                                        style: {
                                            margin: 0
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                xs: 24,
                                                sm: 24,
                                                md: 24,
                                                lg: 12,
                                                xl: 12,
                                                xxl: 12,
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_PersonalInfo.default, {}, void 0, false, {
                                                        fileName: "src/pages/personal-center/index.tsx",
                                                        lineNumber: 148,
                                                        columnNumber: 15
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamListCard.default, {}, void 0, false, {
                                                        fileName: "src/pages/personal-center/index.tsx",
                                                        lineNumber: 151,
                                                        columnNumber: 15
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/index.tsx",
                                                lineNumber: 139,
                                                columnNumber: 13
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                xs: 24,
                                                sm: 24,
                                                md: 24,
                                                lg: 12,
                                                xl: 12,
                                                xxl: 12,
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TodoManagement.default, {}, void 0, false, {
                                                    fileName: "src/pages/personal-center/index.tsx",
                                                    lineNumber: 170,
                                                    columnNumber: 15
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/index.tsx",
                                                lineNumber: 162,
                                                columnNumber: 13
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/index.tsx",
                                        lineNumber: 127,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/index.tsx",
                                lineNumber: 104,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/index.tsx",
                            lineNumber: 88,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_FloatButton.default, {}, void 0, false, {
                            fileName: "src/pages/personal-center/index.tsx",
                            lineNumber: 186,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true);
            };
            _s(PersonalCenterPage, "J38jDe63PMq+vZFAyaRULBMLxho=", false, function() {
                return [
                    _max.useModel
                ];
            });
            _c = PersonalCenterPage;
            var _default = PersonalCenterPage;
            var _c;
            $RefreshReg$(_c, "PersonalCenterPage");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/pages/personal-center/DataOverview.module.css?asmodule": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            "";
            var _default = {
                "errorAnimation": `errorAnimation-NZNd358D`,
                "vehicleCard": `vehicleCard-0mxlIPL1`,
                "fadeInUp": `fadeInUp-bFqfnaEw`,
                "statsCard": `statsCard-0TbvigJF`,
                "loadingCard": `loadingCard-GDzrCBhM`,
                "errorShake": `errorShake-wpgXoB3R`,
                "shimmer": `shimmer-dA25g4ud`,
                "fadeInDelay1": `fadeInDelay1-V7zK7ccQ`,
                "fadeInDelay2": `fadeInDelay2-ua4hNaTO`,
                "fadeInDelay3": `fadeInDelay3-maMANaNI`,
                "fadeInDelay4": `fadeInDelay4-pZVLL5xX`,
                "alertCard": `alertCard-6usHqP81`,
                "personnelCard": `personnelCard-Dq6Syc9G`,
                "warningCard": `warningCard-xiWnZil3`,
                "successAnimation": `successAnimation-hpyb3zGP`,
                "successPulse": `successPulse-9Z25n3ZT`
            };
        },
        "src/pages/personal-center/DataOverview.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _user = __mako_require__("src/services/user.ts");
            var _DataOverviewmodulecssasmodule = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/DataOverview.module.css?asmodule"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            /**
 * 数据概览卡片组件
 *
 * 显示用户的个人统计数据，采用单行四列的水平布局。
 * 包括车辆、人员、预警、告警等指标的统计卡片。
 *
 * 主要功能：
 * 1. 显示车辆数量统计
 * 2. 显示人员数量统计
 * 3. 显示预警数量统计
 * 4. 显示告警数量统计
 *
 * 数据来源：
 * - 个人统计数据：通过UserService.getUserPersonalStats()获取
 *
 * 布局特点：
 * - 单行四列水平排列
 * - 每个统计项独立的卡片设计
 * - 响应式布局适配不同屏幕
 */ const DataOverview = ()=>{
                _s();
                /**
   * 个人统计数据状态管理
   */ const [personalStats, setPersonalStats] = (0, _react.useState)({
                    vehicles: 0,
                    personnel: 0,
                    warnings: 0,
                    alerts: 0
                });
                const [statsLoading, setStatsLoading] = (0, _react.useState)(true);
                const [statsError, setStatsError] = (0, _react.useState)(null);
                // 获取统计数据
                (0, _react.useEffect)(()=>{
                    const fetchStatsData = async ()=>{
                        try {
                            const stats = await _user.UserService.getUserPersonalStats();
                            setPersonalStats(stats);
                            setStatsError(null);
                        } catch (error) {
                            console.error('获取统计数据失败:', error);
                            setStatsError('获取统计数据失败，请稍后重试');
                        } finally{
                            setStatsLoading(false);
                        }
                    };
                    fetchStatsData();
                }, []);
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                    title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            display: 'flex',
                            alignItems: 'center',
                            gap: 8
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.BarChartOutlined, {
                                style: {
                                    fontSize: 16,
                                    color: '#1890ff'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                lineNumber: 77,
                                columnNumber: 11
                            }, void 0),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                children: "数据概览"
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                lineNumber: 78,
                                columnNumber: 11
                            }, void 0)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/DataOverview.tsx",
                        lineNumber: 76,
                        columnNumber: 9
                    }, void 0),
                    style: {
                        marginBottom: 16,
                        borderRadius: 12,
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                    },
                    headStyle: {
                        borderBottom: '1px solid #f0f0f0',
                        paddingBottom: 12
                    },
                    bodyStyle: {
                        padding: '20px'
                    },
                    hoverable: true,
                    children: statsError ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                        message: "数据概览加载失败",
                        description: statsError,
                        type: "error",
                        showIcon: true,
                        style: {
                            borderRadius: 8
                        }
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/DataOverview.tsx",
                        lineNumber: 96,
                        columnNumber: 9
                    }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                        spinning: statsLoading,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                            gutter: [
                                16,
                                16
                            ],
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 12,
                                    sm: 6,
                                    md: 6,
                                    lg: 6,
                                    xl: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        className: `${_DataOverviewmodulecssasmodule.default.statsCard} ${_DataOverviewmodulecssasmodule.default.vehicleCard} ${_DataOverviewmodulecssasmodule.default.fadeInDelay1}`,
                                        styles: {
                                            body: {
                                                padding: '20px 16px',
                                                textAlign: 'center'
                                            }
                                        },
                                        hoverable: true,
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    marginBottom: 12
                                                },
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CarOutlined, {
                                                    style: {
                                                        fontSize: 24,
                                                        color: '#1890ff',
                                                        marginBottom: 8
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                                    lineNumber: 122,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 121,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    fontSize: 32,
                                                    fontWeight: 700,
                                                    color: '#1890ff',
                                                    lineHeight: 1,
                                                    marginBottom: 8
                                                },
                                                children: personalStats.vehicles
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 130,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    fontSize: 14,
                                                    color: '#1890ff',
                                                    fontWeight: 600,
                                                    opacity: 0.8
                                                },
                                                children: "车辆"
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 141,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                        lineNumber: 111,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                    lineNumber: 110,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 12,
                                    sm: 6,
                                    md: 6,
                                    lg: 6,
                                    xl: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        className: `${_DataOverviewmodulecssasmodule.default.statsCard} ${_DataOverviewmodulecssasmodule.default.personnelCard} ${_DataOverviewmodulecssasmodule.default.fadeInDelay2}`,
                                        styles: {
                                            body: {
                                                padding: '20px 16px',
                                                textAlign: 'center'
                                            }
                                        },
                                        hoverable: true,
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    marginBottom: 12
                                                },
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UsergroupAddOutlined, {
                                                    style: {
                                                        fontSize: 24,
                                                        color: '#52c41a',
                                                        marginBottom: 8
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                                    lineNumber: 167,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 166,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    fontSize: 32,
                                                    fontWeight: 700,
                                                    color: '#52c41a',
                                                    lineHeight: 1,
                                                    marginBottom: 8
                                                },
                                                children: personalStats.personnel
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 175,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    fontSize: 14,
                                                    color: '#52c41a',
                                                    fontWeight: 600,
                                                    opacity: 0.8
                                                },
                                                children: "人员"
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 186,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                        lineNumber: 156,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                    lineNumber: 155,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 12,
                                    sm: 6,
                                    md: 6,
                                    lg: 6,
                                    xl: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        className: `${_DataOverviewmodulecssasmodule.default.statsCard} ${_DataOverviewmodulecssasmodule.default.warningCard} ${_DataOverviewmodulecssasmodule.default.fadeInDelay3}`,
                                        styles: {
                                            body: {
                                                padding: '20px 16px',
                                                textAlign: 'center'
                                            }
                                        },
                                        hoverable: true,
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    marginBottom: 12
                                                },
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                                                    style: {
                                                        fontSize: 24,
                                                        color: '#faad14',
                                                        marginBottom: 8
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                                    lineNumber: 212,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 211,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    fontSize: 32,
                                                    fontWeight: 700,
                                                    color: '#faad14',
                                                    lineHeight: 1,
                                                    marginBottom: 8
                                                },
                                                children: personalStats.warnings
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 220,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    fontSize: 14,
                                                    color: '#faad14',
                                                    fontWeight: 600,
                                                    opacity: 0.8
                                                },
                                                children: "预警"
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 231,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                        lineNumber: 201,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                    lineNumber: 200,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 12,
                                    sm: 6,
                                    md: 6,
                                    lg: 6,
                                    xl: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        className: `${_DataOverviewmodulecssasmodule.default.statsCard} ${_DataOverviewmodulecssasmodule.default.alertCard} ${_DataOverviewmodulecssasmodule.default.fadeInDelay4}`,
                                        styles: {
                                            body: {
                                                padding: '20px 16px',
                                                textAlign: 'center'
                                            }
                                        },
                                        hoverable: true,
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    marginBottom: 12
                                                },
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.AlertOutlined, {
                                                    style: {
                                                        fontSize: 24,
                                                        color: '#ff4d4f',
                                                        marginBottom: 8
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                                    lineNumber: 257,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 256,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    fontSize: 32,
                                                    fontWeight: 700,
                                                    color: '#ff4d4f',
                                                    lineHeight: 1,
                                                    marginBottom: 8
                                                },
                                                children: personalStats.alerts
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 265,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    fontSize: 14,
                                                    color: '#ff4d4f',
                                                    fontWeight: 600,
                                                    opacity: 0.8
                                                },
                                                children: "告警"
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 276,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                        lineNumber: 246,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                    lineNumber: 245,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/personal-center/DataOverview.tsx",
                            lineNumber: 108,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/DataOverview.tsx",
                        lineNumber: 106,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/personal-center/DataOverview.tsx",
                    lineNumber: 74,
                    columnNumber: 5
                }, this);
            };
            _s(DataOverview, "V/g64g42m1EsCCdbexpcUQrP3JA=");
            _c = DataOverview;
            var _default = DataOverview;
            var _c;
            $RefreshReg$(_c, "DataOverview");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/pages/personal-center/DataOverview.module.css?modules": function(module, exports, __mako_require__) {}
    }
}, function(runtime) {
    runtime._h = '8437007308745731899';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/settings/index.tsx": [
            "p__settings__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_personal-center_index_tsx-async.9203350349926654237.hot-update.js.map