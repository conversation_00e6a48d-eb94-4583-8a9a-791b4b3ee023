import { useModel } from '@umijs/max';
import { Col, Row, Spin } from 'antd';
import { ProCard } from '@ant-design/pro-components';
import React from 'react';
import UserFloatButton from '@/components/FloatButton';
import TeamListCard from './TeamListCard';
import TodoManagement from './TodoManagement';
import PersonalInfo from './PersonalInfo';
import DataOverview from './DataOverview';

/**
 * 个人中心页面组件
 *
 * 这是用户的个人中心主页面，提供用户信息管理、团队管理、待办事项等功能。
 * 是用户进行个人设置和团队操作的主要入口页面。
 *
 * 页面功能：
 * 1. 用户个人信息展示和编辑
 * 2. 团队列表显示和团队切换
 * 3. 个人待办事项管理
 * 4. 全局浮动操作按钮
 *
 * 页面结构：
 * - 左列：个人信息、团队列表（响应式布局）
 * - 右列：待办事项管理（响应式布局）
 * - 数据概览：独立的水平卡片组件，位于个人信息下方
 * - 浮动：全局操作按钮
 *
 * 权限控制：
 * - 需要用户登录才能访问
 * - 自动检查登录状态并重定向
 * - 支持登录状态变化的实时响应
 *
 * 响应式设计：
 * - 移动端：垂直堆叠布局
 * - 桌面端：左右分栏布局
 * - 自适应不同屏幕尺寸
 */
const PersonalCenterPage: React.FC = () => {
  /**
   * 全局状态管理
   *
   * 从UmiJS全局状态中获取用户信息和加载状态：
   * - initialState: 包含用户和团队信息的全局状态
   * - loading: 全局状态的加载状态
   */
  const { initialState, loading } = useModel('@@initialState');



  /**
   * 加载状态处理
   *
   * 当全局状态正在初始化时，显示加载界面。
   * 这确保了用户在状态加载完成前看到友好的加载提示。
   */
  if (loading) {
    return (
      <div
        style={{
          minHeight: '100vh',
          background: '#f5f8ff',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Spin size="large" />
        <div style={{ marginLeft: 16 }}>正在加载用户信息...</div>
      </div>
    );
  }

  /**
   * 登录状态检查已由应用级路由守卫处理
   *
   * 移除页面级别的登录检查，避免与应用级路由守卫冲突。
   * 应用级路由守卫在 app.tsx 中的 onPageChange 函数已经处理了
   * 用户登录状态检查和重定向逻辑，无需在页面组件中重复检查。
   *
   * 这样可以避免登录成功后的状态更新时序问题，确保用户
   * 一次登录成功后能够正常访问个人中心页面。
   */

  return (
    <>
      {/* 页面主容器 */}
      <div
        style={{
          minHeight: '100vh',
          background: '#f5f8ff', // 浅蓝色背景，营造清新的视觉效果
          padding: '12px 12px 24px 12px', // 移动端优化：减少左右边距，增加底部边距
        }}
      >
        {/*
         * 主内容卡片容器
         *
         * 使用Card组件作为主要内容的容器，提供：
         * 1. 统一的视觉边界和阴影效果
         * 2. 响应式的内边距设置
         * 3. 圆角设计提升视觉体验
         * 4. 全高度布局适配不同屏幕
         */}
        <ProCard
          style={{
            width: '100%',
            minHeight: 'calc(100vh - 48px)', // 减去外层padding的高度
            borderRadius: '12px', // 圆角设计
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)', // 轻微阴影效果
          }}
          bodyStyle={{
            padding: '24px', // 内容区域的内边距
          }}
        >
          {/*
           * 响应式两列布局
           *
           * 使用Ant Design的Row/Col组件实现响应式两列布局：
           * - 移动端：垂直堆叠，所有组件占满宽度
           * - 桌面端：左列包含个人信息、数据概览、团队列表；右列包含待办事项
           * - gutter: 组件间距设置
           * - margin: 0: 避免Row组件的默认负边距影响布局
           */}
          <Row gutter={[24, 12]} style={{ margin: 0 }}>
            {/*
             * 左列：个人信息区域
             *
             * 包含以下组件（按顺序）：
             * 1. 个人信息部分（用户详情、姓名、登录信息等）
             * 2. 数据概览部分（车辆、人员、预警、告警统计）
             * 3. 团队列表部分（用户所属团队列表）
             *
             * 响应式布局：
             * - 移动端(xs-md)：全宽显示
             * - 桌面端(lg+)：占据左半部分
             */}
            <Col
              xs={24}
              sm={24}
              md={24}
              lg={12}
              xl={12}
              xxl={12}
            >
              {/* 个人信息部分（包含用户详情、姓名、登录信息） */}
              <PersonalInfo />

              {/* 数据概览部分 - 移动到左列 */}
              <DataOverview />

              {/* 团队列表部分（用户所属团队列表） */}
              <TeamListCard />
            </Col>

            {/*
             * 右列：待办事项管理区域
             *
             * 个人待办事项的管理界面，支持添加、编辑、删除待办事项。
             * 响应式布局：
             * - 移动端(xs-md)：全宽显示
             * - 桌面端(lg+)：占据右半部分
             */}
            <Col
              xs={24}
              sm={24}
              md={24}
              lg={12}
              xl={12}
              xxl={12}
            >
              <TodoManagement />
            </Col>
          </Row>
        </ProCard>
      </div>

      {/*
       * 全局浮动操作按钮
       *
       * 提供快速访问常用功能的浮动按钮，如：
       * - 快速创建团队
       * - 用户设置
       * - 帮助信息
       *
       * 位置固定在页面右下角，不受页面滚动影响。
       */}
      <UserFloatButton />




    </>
  );
};

export default PersonalCenterPage;
