package com.teammanage.service;

import java.time.Duration;
import java.util.Set;

/**
 * 缓存服务接口
 * 提供统一的缓存操作接口，便于后续扩展和替换缓存实现
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface CacheService {

    /**
     * 存储键值对
     *
     * @param key   键
     * @param value 值
     */
    void set(String key, Object value);

    /**
     * 存储键值对并设置过期时间
     *
     * @param key      键
     * @param value    值
     * @param duration 过期时间
     */
    void set(String key, Object value, Duration duration);

    /**
     * 获取值
     *
     * @param key 键
     * @return 值
     */
    Object get(String key);

    /**
     * 获取值并转换为指定类型
     *
     * @param key   键
     * @param clazz 目标类型
     * @param <T>   类型参数
     * @return 转换后的值
     */
    <T> T get(String key, Class<T> clazz);

    /**
     * 删除键
     *
     * @param key 键
     */
    void delete(String key);

    /**
     * 检查键是否存在
     *
     * @param key 键
     * @return 是否存在
     */
    boolean exists(String key);

    /**
     * 向集合中添加元素
     *
     * @param key   集合键
     * @param value 元素值
     */
    void addToSet(String key, Object value);

    /**
     * 从集合中移除元素
     *
     * @param key   集合键
     * @param value 元素值
     */
    void removeFromSet(String key, Object value);

    /**
     * 获取集合中的所有元素
     *
     * @param key 集合键
     * @return 集合元素
     */
    Set<Object> getSetMembers(String key);

    /**
     * 获取集合大小
     *
     * @param key 集合键
     * @return 集合大小
     */
    long getSetSize(String key);

    /**
     * 根据模式匹配键
     *
     * @param pattern 匹配模式
     * @return 匹配的键集合
     */
    Set<String> keys(String pattern);

    /**
     * 清空所有缓存
     */
    void clear();

    /**
     * 获取缓存统计信息
     *
     * @return 统计信息字符串
     */
    String getStats();
}
