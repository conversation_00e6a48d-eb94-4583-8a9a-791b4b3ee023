# 身份验证系统文档

本目录包含团队管理应用的身份验证系统的完整文档。

## 文档结构

### 📋 核心文档

- **[系统架构](./architecture.md)** - 身份验证系统的整体架构设计和核心组件
- **[认证流程](./auth-flow.md)** - 详细的用户认证和团队切换流程文档
- **[团队切换](./team-switching.md)** - 团队切换功能的详细实现和状态管理
- **[Token管理](./token-management.md)** - JWT Token的生命周期管理和安全机制
- **[API集成](./api-integration.md)** - 前后端API集成方式和错误处理机制
- **[开发者指南](./developer-guide.md)** - 新开发者快速上手指南和最佳实践

### 🎯 文档导航

- **新开发者**: 建议按顺序阅读 [系统架构](./architecture.md) → [开发者指南](./developer-guide.md)
- **前端开发**: 重点关注 [API集成](./api-integration.md) 和 [Token管理](./token-management.md)
- **后端开发**: 重点关注 [系统架构](./architecture.md) 和 [认证流程](./auth-flow.md)
- **团队功能**: 查看 [团队切换](./team-switching.md) 了解多团队管理实现
- **问题排查**: 查看 [开发者指南](./developer-guide.md) 的调试和常见问题部分

## 快速开始

如果你是新加入的开发者，建议按以下顺序阅读文档：

1. **[系统架构](./architecture.md)** - 了解整体设计和核心组件
2. **[认证流程](./auth-flow.md)** - 理解认证机制和状态管理
3. **[开发者指南](./developer-guide.md)** - 开始开发工作和最佳实践
4. **[API集成](./api-integration.md)** - 了解API使用方式和错误处理
5. **[团队切换](./team-switching.md)** - 掌握多团队功能实现

## 技术栈

### 前端
- **框架**: React + UmiJS
- **状态管理**: UmiJS initialState + useModel
- **UI组件**: Ant Design
- **HTTP客户端**: Axios
- **路由**: UmiJS路由系统

### 后端
- **框架**: Spring Boot
- **认证**: JWT (JSON Web Token)
- **数据库**: MySQL
- **ORM**: MyBatis Plus

### 核心概念
- **单Token系统**: 一个JWT Token包含用户和团队信息
- **验证码认证**: 基于邮箱的6位数字验证码登录
- **多租户架构**: 支持用户在多个团队间切换
- **角色权限控制**: 基于团队角色的权限管理

## 文档完成状态

✅ **已完成的文档**:
- [x] [系统架构](./architecture.md) - 完整的架构设计和组件说明
- [x] [认证流程](./auth-flow.md) - 详细的认证和状态管理流程
- [x] [团队切换](./team-switching.md) - 团队切换功能的完整实现
- [x] [Token管理](./token-management.md) - JWT Token的完整生命周期管理
- [x] [API集成](./api-integration.md) - 前后端API集成的完整说明
- [x] [开发者指南](./developer-guide.md) - 新开发者的完整上手指南

## 联系方式

如果你在使用过程中遇到问题，可以：
- 查看[开发者指南](./developer-guide.md)中的调试和常见问题部分
- 联系开发团队
- 提交Issue或Pull Request

---

*最后更新时间: 2025-07-31*
