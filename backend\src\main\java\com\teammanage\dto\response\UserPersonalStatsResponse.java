package com.teammanage.dto.response;

/**
 * 用户个人统计数据响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class UserPersonalStatsResponse {

    /**
     * 车辆数量
     */
    private Integer vehicles;

    /**
     * 人员数量
     */
    private Integer personnel;

    /**
     * 预警数量
     */
    private Integer warnings;

    /**
     * 告警数量
     */
    private Integer alerts;

    // 构造函数
    public UserPersonalStatsResponse() {}

    public UserPersonalStatsResponse(Integer vehicles, Integer personnel, Integer warnings, Integer alerts) {
        this.vehicles = vehicles;
        this.personnel = personnel;
        this.warnings = warnings;
        this.alerts = alerts;
    }

    // Getter and Setter methods
    public Integer getVehicles() { return vehicles; }
    public void setVehicles(Integer vehicles) { this.vehicles = vehicles; }

    public Integer getPersonnel() { return personnel; }
    public void setPersonnel(Integer personnel) { this.personnel = personnel; }

    public Integer getWarnings() { return warnings; }
    public void setWarnings(Integer warnings) { this.warnings = warnings; }

    public Integer getAlerts() { return alerts; }
    public void setAlerts(Integer alerts) { this.alerts = alerts; }

    @Override
    public String toString() {
        return "UserPersonalStatsResponse{" +
                "vehicles=" + vehicles +
                ", personnel=" + personnel +
                ", warnings=" + warnings +
                ", alerts=" + alerts +
                '}';
    }
}
