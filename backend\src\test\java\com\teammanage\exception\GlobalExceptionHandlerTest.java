package com.teammanage.exception;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.AuthenticationException;

import com.teammanage.common.ApiResponse;

/**
 * 全局异常处理器测试
 * 
 * 验证所有异常都能正确处理并返回统一的响应格式
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class GlobalExceptionHandlerTest {

    private GlobalExceptionHandler globalExceptionHandler;

    @BeforeEach
    void setUp() {
        globalExceptionHandler = new GlobalExceptionHandler();
    }

    @Test
    void testHandleBusinessException() {
        BusinessException exception = new BusinessException(400, "业务异常测试");
        
        ApiResponse<Object> response = globalExceptionHandler.handleBusinessException(exception);
        
        assertNotNull(response);
        assertEquals(400, response.getCode());
        assertEquals("业务异常测试", response.getMessage());
        assertNotNull(response.getTimestamp());
    }

    @Test
    void testHandleResourceNotFoundException() {
        ResourceNotFoundException exception = new ResourceNotFoundException("资源不存在");
        
        ApiResponse<Object> response = globalExceptionHandler.handleResourceNotFoundException(exception);
        
        assertNotNull(response);
        assertEquals(404, response.getCode());
        assertEquals("资源不存在", response.getMessage());
        assertNotNull(response.getTimestamp());
    }

    @Test
    void testHandleInsufficientPermissionException() {
        InsufficientPermissionException exception = new InsufficientPermissionException("权限不足");
        
        ApiResponse<Object> response = globalExceptionHandler.handleInsufficientPermissionException(exception);
        
        assertNotNull(response);
        assertEquals(403, response.getCode());
        assertEquals("权限不足", response.getMessage());
        assertNotNull(response.getTimestamp());
    }

    @Test
    void testHandleAuthenticationException() {
        AuthenticationException exception = new AuthenticationException("认证失败") {};
        
        ApiResponse<Object> response = globalExceptionHandler.handleAuthenticationException(exception);
        
        assertNotNull(response);
        assertEquals(401, response.getCode());
        assertEquals("认证失败，请重新登录", response.getMessage());
        assertNotNull(response.getTimestamp());
    }

    @Test
    void testHandleTeamAccessDeniedException() {
        TeamAccessDeniedException exception = TeamAccessDeniedException.userDisabled(1L, 2L);
        
        ApiResponse<Object> response = globalExceptionHandler.handleTeamAccessDeniedException(exception);
        
        assertNotNull(response);
        assertEquals(403, response.getCode());
        assertEquals("您的账户已在此团队中被停用", response.getMessage());
        assertNotNull(response.getTimestamp());
    }

    @Test
    void testHandleAccessDeniedException() {
        AccessDeniedException exception = new AccessDeniedException("访问被拒绝");
        
        ApiResponse<Object> response = globalExceptionHandler.handleAccessDeniedException(exception);
        
        assertNotNull(response);
        assertEquals(403, response.getCode());
        assertEquals("访问被拒绝，权限不足", response.getMessage());
        assertNotNull(response.getTimestamp());
    }

    @Test
    void testHandleIllegalArgumentException() {
        IllegalArgumentException exception = new IllegalArgumentException("非法参数");
        
        ApiResponse<Object> response = globalExceptionHandler.handleIllegalArgumentException(exception);
        
        assertNotNull(response);
        assertEquals(400, response.getCode());
        assertEquals("参数错误: 非法参数", response.getMessage());
        assertNotNull(response.getTimestamp());
    }

    @Test
    void testHandleNullPointerException() {
        NullPointerException exception = new NullPointerException("空指针异常");
        
        ApiResponse<Object> response = globalExceptionHandler.handleNullPointerException(exception);
        
        assertNotNull(response);
        assertEquals(500, response.getCode());
        assertEquals("系统内部错误，请联系管理员", response.getMessage());
        assertNotNull(response.getTimestamp());
    }

    @Test
    void testHandleGenericException() {
        Exception exception = new Exception("未知异常");
        
        ApiResponse<Object> response = globalExceptionHandler.handleException(exception);
        
        assertNotNull(response);
        assertEquals(500, response.getCode());
        assertEquals("系统内部错误，请联系管理员", response.getMessage());
        assertNotNull(response.getTimestamp());
    }
}
