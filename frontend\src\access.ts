import type { TeamDetailResponse, UserProfileResponse } from '@/types/api';
import { TeamRole } from '@/types/api';

/**
 * 角色权限检查工具函数集合
 *
 * 这个对象包含了所有与团队角色相关的权限检查函数。
 * 提供了细粒度的权限控制，支持不同角色的权限验证。
 *
 * 权限设计原则：
 * 1. 团队创建者拥有最高权限
 * 2. 团队成员拥有基础权限
 * 3. 权限检查基于角色枚举值
 * 4. 支持权限级别比较
 *
 * 角色层级：
 * - TEAM_CREATOR (100)：团队创建者，拥有所有权限
 * - TEAM_MEMBER (10)：团队成员，拥有基础权限
 */
const rolePermissions = {
  /**
   * 检查是否可以管理团队
   *
   * 团队管理权限包括：修改团队信息、删除团队、团队设置等。
   * 只有团队创建者才拥有这个权限。
   *
   * @param role 用户在团队中的角色
   * @returns boolean 是否拥有团队管理权限
   */
  canManageTeam: (role?: TeamRole) => role === TeamRole.TEAM_CREATOR,

  /**
   * 检查是否可以管理团队成员
   *
   * 成员管理权限包括：邀请新成员、移除成员、修改成员角色等。
   * 只有团队创建者才拥有这个权限。
   *
   * @param role 用户在团队中的角色
   * @returns boolean 是否拥有成员管理权限
   */
  canManageMembers: (role?: TeamRole) => role === TeamRole.TEAM_CREATOR,

  /**
   * 检查是否可以访问团队数据
   *
   * 数据访问权限包括：查看团队数据、使用团队功能等。
   * 团队创建者和团队成员都拥有这个权限。
   *
   * @param role 用户在团队中的角色
   * @returns boolean 是否拥有数据访问权限
   */
  canAccessData: (role?: TeamRole) => role === TeamRole.TEAM_CREATOR || role === TeamRole.TEAM_MEMBER,

  /**
   * 检查用户权限级别是否满足要求
   *
   * 通过权限级别数值比较来判断用户是否拥有足够的权限。
   * 支持灵活的权限级别检查，便于扩展新的角色。
   *
   * 权限级别定义：
   * - TEAM_CREATOR: 100（最高权限）
   * - TEAM_MEMBER: 10（基础权限）
   *
   * @param userRole 用户当前角色
   * @param requiredRole 所需的最低角色
   * @returns boolean 用户权限是否满足要求
   */
  hasPermissionLevel: (userRole?: TeamRole, requiredRole?: TeamRole) => {
    if (!userRole || !requiredRole) return false;

    // 定义角色权限级别映射
    const levels = {
      [TeamRole.TEAM_CREATOR]: 100,
      [TeamRole.TEAM_MEMBER]: 10,
    };

    // 比较权限级别，用户级别大于等于所需级别即可
    return levels[userRole] >= levels[requiredRole];
  },
};

/**
 * UmiJS 权限控制函数
 *
 * 这是UmiJS应用的权限控制核心函数，基于全局状态中的用户和团队信息
 * 计算当前用户的各种权限状态。返回的权限对象可以在整个应用中使用。
 *
 * 权限控制架构：
 * 1. 基于用户登录状态的基础权限
 * 2. 基于团队选择状态的团队权限
 * 3. 基于用户角色的细粒度权限
 * 4. 向后兼容的权限检查
 *
 * 使用场景：
 * - 路由权限控制：在路由配置中使用权限检查
 * - 组件权限控制：在组件中根据权限显示/隐藏功能
 * - 菜单权限控制：根据权限动态生成菜单
 * - 按钮权限控制：根据权限启用/禁用操作按钮
 *
 * 权限计算逻辑：
 * 1. 从全局状态获取用户和团队信息
 * 2. 确定用户在当前团队中的角色
 * 3. 基于角色计算各种权限状态
 * 4. 返回完整的权限对象
 *
 * @param initialState 全局初始化状态，包含用户和团队信息
 * @returns 权限对象，包含各种权限检查结果
 *
 * @see https://umijs.org/docs/max/access#access
 */
export default function access(
  initialState:
    | {
        currentUser?: UserProfileResponse;
        currentTeam?: TeamDetailResponse;
      }
    | undefined,
) {
  // 从全局状态中提取用户和团队信息
  const { currentUser, currentTeam } = initialState ?? {};

  /**
   * 角色确定逻辑
   *
   * 优先使用团队信息中的role字段，如果不存在则根据isCreator字段推断。
   * 这种设计保证了向后兼容性，同时支持新的角色系统。
   *
   * 角色推断规则：
   * - 有role字段：直接使用role字段的值
   * - 无role字段但isCreator为true：推断为TEAM_CREATOR
   * - 无role字段且isCreator为false：推断为TEAM_MEMBER
   */
  const currentRole = currentTeam?.role || (currentTeam?.isCreator ? TeamRole.TEAM_CREATOR : TeamRole.TEAM_MEMBER);

  return {
    /**
     * 基于角色的权限控制
     *
     * 这些权限基于用户在团队中的角色进行计算，提供细粒度的权限控制。
     * 需要同时满足用户已登录、已选择团队和拥有相应角色的条件。
     */

    /** 团队管理权限：修改团队信息、删除团队等 */
    canManageTeam: currentUser && currentTeam && rolePermissions.canManageTeam(currentRole),

    /** 成员管理权限：邀请成员、移除成员、修改角色等 */
    canManageMembers: currentUser && currentTeam && rolePermissions.canManageMembers(currentRole),

    /** 数据访问权限：查看和使用团队数据 */
    canAccessData: currentUser && currentTeam && rolePermissions.canAccessData(currentRole),

    /**
     * 向后兼容的权限控制
     *
     * 保留原有的权限检查方式，确保现有代码的正常运行。
     * 基于isCreator字段进行权限判断。
     */
    canAdmin: currentUser && currentTeam && currentTeam.isCreator,

    /**
     * 基础权限状态
     *
     * 这些是最基本的权限状态，用于判断用户的基本状态。
     */

    /** 用户是否已登录 */
    isLoggedIn: !!currentUser,

    /** 用户是否已选择团队 */
    hasTeam: !!currentTeam,

    /**
     * 角色状态检查
     *
     * 直接检查用户在当前团队中的具体角色。
     */

    /** 用户是否为团队创建者 */
    isTeamCreator: currentRole === TeamRole.TEAM_CREATOR,

    /** 用户是否为团队成员 */
    isTeamMember: currentRole === TeamRole.TEAM_MEMBER,

    /**
     * 权限级别检查函数
     *
     * 提供动态的权限级别检查，支持在运行时检查用户是否拥有特定级别的权限。
     *
     * @param requiredRole 所需的最低角色级别
     * @returns boolean 用户权限是否满足要求
     *
     * @example
     * ```typescript
     * // 在组件中使用
     * const { hasPermissionLevel } = useAccess();
     * if (hasPermissionLevel(TeamRole.TEAM_CREATOR)) {
     *   // 显示管理员功能
     * }
     * ```
     */
    hasPermissionLevel: (requiredRole: TeamRole) =>
      rolePermissions.hasPermissionLevel(currentRole, requiredRole),
  };
}
