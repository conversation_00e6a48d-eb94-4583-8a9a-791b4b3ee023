import {
  MailOutlined,
  PhoneOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import {
  Popover,
  Space,
  Typography,
  Divider,
} from 'antd';
import React from 'react';
import type { UserProfileDetailResponse } from '@/types/api';
import styles from './UserInfoPopover.module.css';

const { Text } = Typography;

interface UserInfoPopoverProps {
  userInfo: UserProfileDetailResponse;
  children: React.ReactNode;
}

/**
 * 用户信息气泡卡片组件
 *
 * 在用户名上显示详细的用户信息，包括电话、邮箱、注册时间等。
 * 采用Popover组件实现悬浮显示效果。
 *
 * 主要功能：
 * 1. 显示用户邮箱
 * 2. 显示用户电话
 * 3. 显示注册时间
 * 4. 显示最后登录时间
 * 5. 显示最后登录团队
 *
 * 使用方式：
 * <UserInfoPopover userInfo={userInfo}>
 *   <span>用户名</span>
 * </UserInfoPopover>
 */
const UserInfoPopover: React.FC<UserInfoPopoverProps> = ({
  userInfo,
  children,
}) => {
  const popoverContent = (
    <div className={styles.popoverContent}>
      <Space direction="vertical" size={12} style={{ width: '100%' }}>
        {/* 联系信息 */}
        {userInfo.email && (
          <div className={`${styles.infoItem} ${styles.email}`}>
            <div className={styles.iconWrapper}>
              <MailOutlined className={styles.icon} style={{ color: '#1890ff' }} />
            </div>
            <div className={styles.infoContent}>
              <Text type="secondary" className={styles.label}>
                邮箱
              </Text>
              <Text className={styles.value} copyable>
                {userInfo.email}
              </Text>
            </div>
          </div>
        )}

        {userInfo.telephone && (
          <div className={`${styles.infoItem} ${styles.phone}`}>
            <div className={styles.iconWrapper}>
              <PhoneOutlined className={styles.icon} style={{ color: '#52c41a' }} />
            </div>
            <div className={styles.infoContent}>
              <Text type="secondary" className={styles.label}>
                电话
              </Text>
              <Text className={styles.value} copyable>
                {userInfo.telephone}
              </Text>
            </div>
          </div>
        )}

        {(userInfo.email || userInfo.telephone) && userInfo.registerDate && (
          <Divider className={styles.divider} />
        )}

        {/* 时间信息 */}
        {userInfo.registerDate && (
          <div className={`${styles.infoItem} ${styles.register}`}>
            <div className={styles.iconWrapper}>
              <CalendarOutlined className={styles.icon} style={{ color: '#722ed1' }} />
            </div>
            <div className={styles.infoContent}>
              <Text type="secondary" className={styles.label}>
                注册时间
              </Text>
              <Text className={styles.value}>
                {userInfo.registerDate}
              </Text>
            </div>
          </div>
        )}

        {userInfo.lastLoginTime && (
          <div className={`${styles.infoItem} ${styles.lastLogin}`}>
            <div className={styles.iconWrapper}>
              <ClockCircleOutlined className={styles.icon} style={{ color: '#fa8c16' }} />
            </div>
            <div className={styles.infoContent}>
              <Text type="secondary" className={styles.label}>
                最后登录
              </Text>
              <Text className={styles.value}>
                {userInfo.lastLoginTime}
              </Text>
            </div>
          </div>
        )}

        {userInfo.lastLoginTeam && (
          <div className={`${styles.infoItem} ${styles.team}`}>
            <div className={styles.iconWrapper}>
              <TeamOutlined className={styles.icon} style={{ color: '#13c2c2' }} />
            </div>
            <div className={styles.infoContent}>
              <Text type="secondary" className={styles.label}>
                登录团队
              </Text>
              <Text className={styles.value}>
                {userInfo.lastLoginTeam}
              </Text>
            </div>
          </div>
        )}
      </Space>
    </div>
  );

  return (
    <Popover
      content={popoverContent}
      title={
        <div className={styles.popoverTitle}>
          <Text strong>用户详细信息</Text>
        </div>
      }
      trigger={["hover", "click"]}
      placement="bottomLeft"
      styles={{
        body: {
          padding: '16px 20px',
          borderRadius: '12px',
          background: '#ffffff',
          maxWidth: '380px',
          boxShadow: '0 12px 32px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08)',
          border: '1px solid rgba(0, 0, 0, 0.06)',
        },
      }}
      arrow={{
        pointAtCenter: true,
      }}
      mouseEnterDelay={0.3}
      mouseLeaveDelay={0.1}
      fresh={false}
      zIndex={1060}
    >
      <span className={styles.trigger}>
        {children}
      </span>
    </Popover>
  );
};

export default UserInfoPopover;
