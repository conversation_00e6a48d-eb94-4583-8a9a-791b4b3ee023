import {
  MailOutlined,
  PhoneOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import {
  Popover,
  Space,
  Typography,
  Divider,
} from 'antd';
import React from 'react';
import type { UserProfileDetailResponse } from '@/types/api';

const { Text } = Typography;

interface UserInfoPopoverProps {
  userInfo: UserProfileDetailResponse;
  children: React.ReactNode;
}

/**
 * 用户信息气泡卡片组件
 *
 * 在用户名上显示详细的用户信息，包括电话、邮箱、注册时间等。
 * 采用Popover组件实现悬浮显示效果。
 *
 * 主要功能：
 * 1. 显示用户邮箱
 * 2. 显示用户电话
 * 3. 显示注册时间
 * 4. 显示最后登录时间
 * 5. 显示最后登录团队
 *
 * 使用方式：
 * <UserInfoPopover userInfo={userInfo}>
 *   <span>用户名</span>
 * </UserInfoPopover>
 */
const UserInfoPopover: React.FC<UserInfoPopoverProps> = ({
  userInfo,
  children,
}) => {
  // 定义内联样式对象
  const styles = {
    popoverContent: {
      padding: '4px 0',
      minWidth: '280px',
    },
    popoverTitle: {
      padding: '4px 0',
      borderBottom: '1px solid #f0f0f0',
      marginBottom: '8px',
    },
    infoItem: {
      display: 'flex',
      alignItems: 'flex-start',
      gap: '12px',
      padding: '8px 0',
      transition: 'all 0.2s ease',
    },
    iconWrapper: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      width: '24px',
      height: '24px',
      borderRadius: '50%',
      background: 'rgba(0, 0, 0, 0.04)',
      flexShrink: 0,
      marginTop: '2px',
    },
    icon: {
      fontSize: '12px',
    },
    infoContent: {
      flex: 1,
      minWidth: 0,
    },
    label: {
      display: 'block',
      fontSize: '12px',
      lineHeight: 1.2,
      marginBottom: '2px',
      opacity: 0.7,
    },
    value: {
      display: 'block',
      fontSize: '13px',
      fontWeight: 500,
      lineHeight: 1.3,
      wordBreak: 'break-all',
    },
    trigger: {
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      borderRadius: '4px',
      padding: '2px 4px',
      margin: '-2px -4px',
    },
  };
  const popoverContent = (
    <div style={styles.popoverContent}>
      <Space direction="vertical" size={12} style={{ width: '100%' }}>
        {/* 联系信息 */}
        {userInfo.email && (
          <div style={styles.infoItem}>
            <div style={styles.iconWrapper}>
              <MailOutlined style={{ ...styles.icon, color: '#1890ff' }} />
            </div>
            <div style={styles.infoContent}>
              <Text type="secondary" style={styles.label}>
                邮箱
              </Text>
              <Text style={styles.value} copyable>
                {userInfo.email}
              </Text>
            </div>
          </div>
        )}

        {userInfo.telephone && (
          <div style={styles.infoItem}>
            <div style={styles.iconWrapper}>
              <PhoneOutlined style={{ ...styles.icon, color: '#52c41a' }} />
            </div>
            <div style={styles.infoContent}>
              <Text type="secondary" style={styles.label}>
                电话
              </Text>
              <Text style={styles.value} copyable>
                {userInfo.telephone}
              </Text>
            </div>
          </div>
        )}

        {(userInfo.email || userInfo.telephone) && userInfo.registerDate && (
          <Divider style={{ margin: '8px 0' }} />
        )}

        {/* 时间信息 */}
        {userInfo.registerDate && (
          <div style={styles.infoItem}>
            <div style={styles.iconWrapper}>
              <CalendarOutlined style={{ ...styles.icon, color: '#722ed1' }} />
            </div>
            <div style={styles.infoContent}>
              <Text type="secondary" style={styles.label}>
                注册时间
              </Text>
              <Text style={styles.value}>
                {userInfo.registerDate}
              </Text>
            </div>
          </div>
        )}

        {userInfo.lastLoginTime && (
          <div style={styles.infoItem}>
            <div style={styles.iconWrapper}>
              <ClockCircleOutlined style={{ ...styles.icon, color: '#fa8c16' }} />
            </div>
            <div style={styles.infoContent}>
              <Text type="secondary" style={styles.label}>
                最后登录
              </Text>
              <Text style={styles.value}>
                {userInfo.lastLoginTime}
              </Text>
            </div>
          </div>
        )}

        {userInfo.lastLoginTeam && (
          <div style={styles.infoItem}>
            <div style={styles.iconWrapper}>
              <TeamOutlined style={{ ...styles.icon, color: '#13c2c2' }} />
            </div>
            <div style={styles.infoContent}>
              <Text type="secondary" style={styles.label}>
                登录团队
              </Text>
              <Text style={styles.value}>
                {userInfo.lastLoginTeam}
              </Text>
            </div>
          </div>
        )}
      </Space>
    </div>
  );

  return (
    <Popover
      content={popoverContent}
      title={
        <div style={styles.popoverTitle}>
          <Text strong>用户详细信息</Text>
        </div>
      }
      trigger="hover"
      placement="bottomLeft"
      styles={{
        body: {
          maxWidth: 320,
          zIndex: 1050,
        },
      }}
    >
      <span style={styles.trigger}>
        {children}
      </span>
    </Popover>
  );
};

export default UserInfoPopover;
