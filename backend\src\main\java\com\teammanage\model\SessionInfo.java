package com.teammanage.model;



import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 会话信息模型
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

public class SessionInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long accountId;

    /**
     * Token哈希
     */
    private String tokenHash;

    /**
     * 设备信息
     */
    private String deviceInfo;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 登录时间
     */
    private LocalDateTime loginTime;

    /**
     * 最后活动时间
     */
    private LocalDateTime lastActivityTime;

    /**
     * 会话状态
     */
    private Boolean isActive;

    // 构造函数
    public SessionInfo() {}

    public SessionInfo(Long accountId, String tokenHash, String deviceInfo, String ipAddress,
                      String userAgent, LocalDateTime loginTime, LocalDateTime lastActivityTime, Boolean isActive) {
        this.accountId = accountId;
        this.tokenHash = tokenHash;
        this.deviceInfo = deviceInfo;
        this.ipAddress = ipAddress;
        this.userAgent = userAgent;
        this.loginTime = loginTime;
        this.lastActivityTime = lastActivityTime;
        this.isActive = isActive;
    }

    // Builder模式的静态方法
    public static SessionInfoBuilder builder() {
        return new SessionInfoBuilder();
    }

    public static class SessionInfoBuilder {
        private Long accountId;
        private String tokenHash;
        private String deviceInfo;
        private String ipAddress;
        private String userAgent;
        private LocalDateTime loginTime;
        private LocalDateTime lastActivityTime;
        private Boolean isActive;

        public SessionInfoBuilder accountId(Long accountId) {
            this.accountId = accountId;
            return this;
        }

        public SessionInfoBuilder tokenHash(String tokenHash) {
            this.tokenHash = tokenHash;
            return this;
        }

        public SessionInfoBuilder deviceInfo(String deviceInfo) {
            this.deviceInfo = deviceInfo;
            return this;
        }

        public SessionInfoBuilder ipAddress(String ipAddress) {
            this.ipAddress = ipAddress;
            return this;
        }

        public SessionInfoBuilder userAgent(String userAgent) {
            this.userAgent = userAgent;
            return this;
        }

        public SessionInfoBuilder loginTime(LocalDateTime loginTime) {
            this.loginTime = loginTime;
            return this;
        }

        public SessionInfoBuilder lastActivityTime(LocalDateTime lastActivityTime) {
            this.lastActivityTime = lastActivityTime;
            return this;
        }

        public SessionInfoBuilder isActive(Boolean isActive) {
            this.isActive = isActive;
            return this;
        }

        public SessionInfo build() {
            return new SessionInfo(accountId, tokenHash, deviceInfo, ipAddress, userAgent,
                                 loginTime, lastActivityTime, isActive);
        }
    }

    // 手动添加getter/setter方法
    public Long getAccountId() { return accountId; }
    public void setAccountId(Long accountId) { this.accountId = accountId; }

    public String getTokenHash() { return tokenHash; }
    public void setTokenHash(String tokenHash) { this.tokenHash = tokenHash; }

    public String getDeviceInfo() { return deviceInfo; }
    public void setDeviceInfo(String deviceInfo) { this.deviceInfo = deviceInfo; }

    public String getIpAddress() { return ipAddress; }
    public void setIpAddress(String ipAddress) { this.ipAddress = ipAddress; }

    public String getUserAgent() { return userAgent; }
    public void setUserAgent(String userAgent) { this.userAgent = userAgent; }

    public LocalDateTime getLoginTime() { return loginTime; }
    public void setLoginTime(LocalDateTime loginTime) { this.loginTime = loginTime; }

    public LocalDateTime getLastActivityTime() { return lastActivityTime; }
    public void setLastActivityTime(LocalDateTime lastActivityTime) { this.lastActivityTime = lastActivityTime; }

    public Boolean getIsActive() { return isActive; }
    public void setIsActive(Boolean isActive) { this.isActive = isActive; }





}
