package com.teammanage.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.teammanage.entity.TeamInvitation;

import java.time.LocalDateTime;

/**
 * 邀请信息响应DTO
 * 用于获取邀请详情（不包含敏感信息）
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class InvitationInfoResponse {

    /**
     * 处理结果
     */
    private Boolean success;

    /**
     * 团队ID
     */
    private Long teamId;

    /**
     * 团队名称
     */
    private String teamName;

    /**
     * 邀请人姓名
     */
    private String inviterName;

    /**
     * 邀请消息
     */
    private String message;

    /**
     * 邀请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime invitedAt;

    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiresAt;

    /**
     * 是否已过期
     */
    private Boolean isExpired;

    /**
     * 是否可以响应
     */
    private Boolean canBeResponded;

    /**
     * 错误消息（如果失败）
     */
    private String errorMessage;

    // 构造函数
    public InvitationInfoResponse() {}

    // Getter 和 Setter 方法
    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public String getInviterName() {
        return inviterName;
    }

    public void setInviterName(String inviterName) {
        this.inviterName = inviterName;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public LocalDateTime getInvitedAt() {
        return invitedAt;
    }

    public void setInvitedAt(LocalDateTime invitedAt) {
        this.invitedAt = invitedAt;
    }

    public LocalDateTime getExpiresAt() {
        return expiresAt;
    }

    public void setExpiresAt(LocalDateTime expiresAt) {
        this.expiresAt = expiresAt;
    }

    public Boolean getIsExpired() {
        return isExpired;
    }

    public void setIsExpired(Boolean isExpired) {
        this.isExpired = isExpired;
    }

    public Boolean getCanBeResponded() {
        return canBeResponded;
    }

    public void setCanBeResponded(Boolean canBeResponded) {
        this.canBeResponded = canBeResponded;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
