package com.teammanage.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.teammanage.entity.TeamInvitation;

import java.time.LocalDateTime;

/**
 * 团队邀请响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class TeamInvitationResponse {

    /**
     * 邀请ID
     */
    private Long id;

    /**
     * 团队ID
     */
    private Long teamId;

    /**
     * 团队名称
     */
    private String teamName;

    /**
     * 邀请人ID
     */
    private Long inviterId;

    /**
     * 邀请人姓名
     */
    private String inviterName;

    /**
     * 邀请人邮箱
     */
    private String inviterEmail;

    /**
     * 被邀请人邮箱
     */
    private String inviteeEmail;

    /**
     * 被邀请人ID
     */
    private Long inviteeId;

    /**
     * 被邀请人姓名
     */
    private String inviteeName;

    /**
     * 邀请状态
     */
    private TeamInvitation.InvitationStatus status;

    /**
     * 邀请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime invitedAt;

    /**
     * 响应时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime respondedAt;

    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiresAt;

    /**
     * 邀请消息
     */
    private String message;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 是否已过期
     */
    private Boolean isExpired;

    /**
     * 是否可以响应
     */
    private Boolean canBeResponded;

    /**
     * 是否可以取消
     */
    private Boolean canBeCancelled;

    // Getter and Setter methods
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getTeamId() { return teamId; }
    public void setTeamId(Long teamId) { this.teamId = teamId; }

    public String getTeamName() { return teamName; }
    public void setTeamName(String teamName) { this.teamName = teamName; }

    public Long getInviterId() { return inviterId; }
    public void setInviterId(Long inviterId) { this.inviterId = inviterId; }

    public String getInviterName() { return inviterName; }
    public void setInviterName(String inviterName) { this.inviterName = inviterName; }

    public String getInviterEmail() { return inviterEmail; }
    public void setInviterEmail(String inviterEmail) { this.inviterEmail = inviterEmail; }

    public String getInviteeEmail() { return inviteeEmail; }
    public void setInviteeEmail(String inviteeEmail) { this.inviteeEmail = inviteeEmail; }

    public Long getInviteeId() { return inviteeId; }
    public void setInviteeId(Long inviteeId) { this.inviteeId = inviteeId; }

    public String getInviteeName() { return inviteeName; }
    public void setInviteeName(String inviteeName) { this.inviteeName = inviteeName; }

    public TeamInvitation.InvitationStatus getStatus() { return status; }
    public void setStatus(TeamInvitation.InvitationStatus status) { this.status = status; }

    public LocalDateTime getInvitedAt() { return invitedAt; }
    public void setInvitedAt(LocalDateTime invitedAt) { this.invitedAt = invitedAt; }

    public LocalDateTime getRespondedAt() { return respondedAt; }
    public void setRespondedAt(LocalDateTime respondedAt) { this.respondedAt = respondedAt; }

    public LocalDateTime getExpiresAt() { return expiresAt; }
    public void setExpiresAt(LocalDateTime expiresAt) { this.expiresAt = expiresAt; }

    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    public Boolean getIsExpired() { return isExpired; }
    public void setIsExpired(Boolean isExpired) { this.isExpired = isExpired; }

    public Boolean getCanBeResponded() { return canBeResponded; }
    public void setCanBeResponded(Boolean canBeResponded) { this.canBeResponded = canBeResponded; }

    public Boolean getCanBeCancelled() { return canBeCancelled; }
    public void setCanBeCancelled(Boolean canBeCancelled) { this.canBeCancelled = canBeCancelled; }
}
