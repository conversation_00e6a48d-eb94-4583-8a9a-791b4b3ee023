globalThis.makoModuleHotUpdate('src/pages/personal-center/index.tsx', {
    modules: {
        "src/pages/personal-center/PersonalInfo.module.css?modules": function(module, exports, __mako_require__) {},
        "src/pages/personal-center/PersonalInfo.module.css?asmodule": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            "";
            var _default = {
                "userBasicInfo": `userBasicInfo-hoOMII6q`,
                "contentArea": `contentArea-_n2x4BDb`,
                "personalInfoContent": `personalInfoContent-j3z_Qmb_`,
                "loginInfoItem": `loginInfoItem-74tohz9t`,
                "contactCard": `contactCard-iYaWi3ca`,
                "statsCard": `statsCard-RbylBLDK`,
                "errorShake": `errorShake-RkVI4HkN`,
                "settingsButton": `settingsButton-mrJVLRGb`,
                "shimmer": `shimmer-lCzub6k8`,
                "fadeInDelay2": `fadeInDelay2-ZracY-tQ`,
                "decorativeCircle2": `decorativeCircle2-t8HUdx07`,
                "fadeInDelay3": `fadeInDelay3-nbwSZP8A`,
                "fadeInDelay4": `fadeInDelay4-TJToLvzm`,
                "skeletonCard": `skeletonCard-fH7lshhh`,
                "additionalInfo": `additionalInfo-SWj22vN7`,
                "personalInfoCard": `personalInfoCard-GMTUvuoL`,
                "onlineIndicator": `onlineIndicator-dZLd46X5`,
                "titleBar": `titleBar-GUpHpaO-`,
                "errorAnimation": `errorAnimation-SVfCef80`,
                "fadeInUp": `fadeInUp-LmQACtmh`,
                "userInfoSection": `userInfoSection-ge4WE5-U`,
                "float": `float-DAEgOouD`,
                "title": `title-OR5wN18v`,
                "pulse": `pulse-DmGLSG7j`,
                "fadeInDelay1": `fadeInDelay1-kMZ2-Q5v`,
                "loginInfoSection": `loginInfoSection-p7RLKcXS`,
                "avatar": `avatar-_T1D7bf2`,
                "decorativeCircle1": `decorativeCircle1-HEEi2j9f`,
                "userName": `userName-MACou0NP`,
                "successAnimation": `successAnimation-UGiJBscn`,
                "avatarContainer": `avatarContainer-DSdWVJ9f`,
                "successPulse": `successPulse-xvCPq0dm`,
                "loadingContainer": `loadingContainer-lZKULeb8`
            };
        }
    }
}, function(runtime) {
    runtime._h = '3211841515215066945';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/settings/index.tsx": [
            "p__settings__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_personal-center_index_tsx-async.12369353205775444207.hot-update.js.map