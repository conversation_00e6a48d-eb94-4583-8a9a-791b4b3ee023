import { render, screen, fireEvent } from '@testing-library/react';
import { history } from '@umijs/max';
import SettingsPage from '../index';

// Mock UmiJS dependencies
jest.mock('@umijs/max', () => ({
  useModel: jest.fn(() => ({
    initialState: {
      currentUser: { id: 1, name: 'Test User' },
      currentTeam: { id: 1, name: 'Test Team' }
    },
    loading: false
  })),
  history: {
    go: jest.fn(),
    push: jest.fn(),
  }
}));

// Mock components
jest.mock('@/components/FloatButton', () => {
  return function MockUserFloatButton() {
    return <div data-testid="float-button">Float Button</div>;
  };
});

jest.mock('../TeamManagementCard', () => {
  return function MockTeamManagementCard() {
    return <div data-testid="team-management-card">Team Management</div>;
  };
});

jest.mock('../UserProfileCard', () => {
  return function MockUserProfileCard() {
    return <div data-testid="user-profile-card">User Profile</div>;
  };
});

describe('Settings Page - Go Back Functionality', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should call history.go(-1) when there is browser history', () => {
    // Mock window.history.length to simulate having history
    Object.defineProperty(window, 'history', {
      value: { length: 3 },
      writable: true
    });

    render(<SettingsPage />);
    
    const backButton = screen.getByRole('button', { name: /返回/i });
    fireEvent.click(backButton);

    expect(history.go).toHaveBeenCalledWith(-1);
    expect(history.push).not.toHaveBeenCalled();
  });

  it('should navigate to dashboard when there is no browser history', () => {
    // Mock window.history.length to simulate no history
    Object.defineProperty(window, 'history', {
      value: { length: 1 },
      writable: true
    });

    render(<SettingsPage />);
    
    const backButton = screen.getByRole('button', { name: /返回/i });
    fireEvent.click(backButton);

    expect(history.push).toHaveBeenCalledWith('/dashboard');
    expect(history.go).not.toHaveBeenCalled();
  });

  it('should render the back button with correct icon and text', () => {
    render(<SettingsPage />);
    
    const backButton = screen.getByRole('button', { name: /返回/i });
    expect(backButton).toBeInTheDocument();
    expect(backButton).toHaveTextContent('返回');
  });

  it('should render settings page title and description', () => {
    render(<SettingsPage />);
    
    expect(screen.getByText('设置中心')).toBeInTheDocument();
    expect(screen.getByText('管理您的个人信息、团队设置和账户偏好')).toBeInTheDocument();
  });
});
