package com.teammanage.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.teammanage.context.TeamContextHolder;
import com.teammanage.dto.request.CreateTeamRequest;
import com.teammanage.dto.request.InviteMembersRequest;
import com.teammanage.dto.request.UpdateTeamRequest;
import com.teammanage.dto.response.TeamDetailResponse;
import com.teammanage.dto.response.TeamMemberResponse;
import com.teammanage.entity.Account;
import com.teammanage.entity.Team;
import com.teammanage.entity.TeamMember;
import com.teammanage.enums.TeamRole;
import com.teammanage.exception.BusinessException;
import com.teammanage.exception.InsufficientPermissionException;
import com.teammanage.exception.ResourceNotFoundException;
import com.teammanage.mapper.AccountMapper;
import com.teammanage.mapper.TeamMapper;
import com.teammanage.mapper.TeamMemberMapper;
import com.teammanage.util.TeamPermissionChecker;
import com.teammanage.util.InputSanitizer;
import com.teammanage.service.RateLimitService;
import com.teammanage.exception.DatabaseException;
import com.teammanage.exception.NetworkException;
import com.teammanage.exception.RateLimitException;

/**
 * 团队管理服务
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class TeamService {

    private static final Logger log = LoggerFactory.getLogger(TeamService.class);

    @Autowired
    private TeamMapper teamMapper;

    @Autowired
    private TeamMemberMapper teamMemberMapper;

    @Autowired
    private AccountMapper accountMapper;

    @Autowired
    private TeamPermissionChecker permissionChecker;

    @Autowired
    private TeamLimitService teamLimitService;

    @Autowired
    private TeamInvitationService teamInvitationService;

    @Autowired
    private TeamMemberAccessValidator teamMemberAccessValidator;

    @Autowired
    private InputSanitizer inputSanitizer;

    @Autowired
    private RateLimitService rateLimitService;

    @Value("${app.team.max-members:100}")
    private int maxMembers;

    /**
     * 创建团队
     *
     * @param request 创建团队请求
     * @param creatorId 创建者ID
     * @return 团队详情
     */
    @Transactional
    public TeamDetailResponse createTeam(CreateTeamRequest request, Long creatorId) {
        try {
            // 检查速率限制
            rateLimitService.checkTeamCreationLimit(creatorId);

            // 输入验证和清理
            String cleanedName = inputSanitizer.sanitizeTeamName(request.getName());
            String cleanedDescription = inputSanitizer.sanitizeTeamDescription(request.getDescription());

            // 检查用户订阅限制 - 验证可创建的团队数量
            int teamLimit = teamLimitService.getUserTeamLimit(creatorId);
            int currentTeamCount = getUserCreatedTeamCount(creatorId);

            if (currentTeamCount >= teamLimit) {
                throw new BusinessException("已达到订阅套餐的团队创建数量限制，当前限制：" + teamLimit + "个团队");
            }

            // 检查团队名称是否已存在
            if (teamMapper.existsByName(cleanedName)) {
                throw new BusinessException("团队名称已存在");
            }

            // 创建团队
            Team team = new Team();
            team.setName(cleanedName);
            team.setDescription(cleanedDescription);
            team.setCreatedBy(creatorId);
            team.setIsDeleted(false);

            teamMapper.insert(team);

            // 添加创建者为团队成员，使用新的角色系统
            TeamMember creatorMember = new TeamMember();
            creatorMember.setTeamId(team.getId());
            creatorMember.setAccountId(creatorId);
            // 使用新的角色系统设置为团队创建者
            creatorMember.setRole(TeamRole.TEAM_CREATOR);
            creatorMember.setAssignedAt(LocalDateTime.now());
            creatorMember.setLastAccessTime(LocalDateTime.now());
            creatorMember.setIsActive(true);
            creatorMember.setIsDeleted(false);

            teamMemberMapper.insert(creatorMember);

            log.info("团队创建成功: teamId={}, name={}, creatorId={}, currentCount={}/{}",
                    team.getId(), team.getName(), creatorId, currentTeamCount + 1, teamLimit);

            return getTeamDetail(team.getId(), creatorId);

        } catch (Exception e) {
            // 数据库操作异常处理
            if (e instanceof BusinessException) {
                throw e; // 重新抛出业务异常
            }
            log.error("团队创建失败: creatorId={}, teamName={}", creatorId, request.getName(), e);
            throw DatabaseException.transactionRollback("CREATE_TEAM", e);
        }
    }

    /**
     * 获取团队详情
     *
     * @param teamId 团队ID
     * @return 团队详情
     */
    public TeamDetailResponse getTeamDetail(Long teamId) {
        return getTeamDetail(teamId, null);
    }

    /**
     * 获取团队详情（支持指定用户ID）
     *
     * @param teamId 团队ID
     * @param userId 用户ID，如果为null则使用团队上下文
     * @return 团队详情
     */
    public TeamDetailResponse getTeamDetail(Long teamId, Long userId) {
        Team team = teamMapper.selectById(teamId);
        if (team == null || team.getIsDeleted()) {
            throw new ResourceNotFoundException("团队不存在");
        }

        // 统计成员数量
        int memberCount = teamMemberMapper.countByTeamId(teamId);

        TeamDetailResponse response = new TeamDetailResponse();
        response.setId(team.getId());
        response.setName(team.getName());
        response.setDescription(team.getDescription());
        response.setCreatedBy(team.getCreatedBy());
        response.setMemberCount(memberCount);
        response.setCreatedAt(team.getCreatedAt());
        response.setUpdatedAt(team.getUpdatedAt());

        // 设置角色和isCreator字段：检查指定用户或当前用户是否为团队创建者
        boolean isCreator = false;
        TeamRole userRole = TeamRole.TEAM_MEMBER; // 默认角色

        if (userId != null) {
            // 使用指定的用户ID检查
            isCreator = team.getCreatedBy().equals(userId);
            userRole = TeamRole.fromIsCreator(isCreator);
        } else if (TeamContextHolder.hasTeamContext()) {
            // 使用团队上下文中的信息
            userRole = TeamContextHolder.getCurrentUserRole();
            isCreator = userRole.toIsCreator();
        }

        // 设置新的角色字段和向后兼容的isCreator字段
        response.setRole(userRole);
        response.setIsCreator(isCreator);

        return response;
    }

    /**
     * 获取当前团队详情
     *
     * @return 团队详情
     */
    public TeamDetailResponse getCurrentTeamDetail() {
        Long teamId = TeamContextHolder.getCurrentTeamId();
        Long userId = TeamContextHolder.getCurrentUserId();

        // 验证用户是否可以访问该团队
        teamMemberAccessValidator.validateTeamAccess(teamId, userId);

        return getTeamDetail(teamId);
    }

    /**
     * 更新团队信息
     *
     * @param request 更新请求
     * @return 更新后的团队详情
     */
    @Transactional
    public TeamDetailResponse updateTeam(UpdateTeamRequest request) {
        Long teamId = TeamContextHolder.getCurrentTeamId();
        Long userId = TeamContextHolder.getCurrentUserId();

        // 验证用户是否可以访问该团队
        teamMemberAccessValidator.validateTeamAccess(teamId, userId);

        // 检查权限
        permissionChecker.checkTeamManagePermission();
        Team team = teamMapper.selectById(teamId);
        if (team == null || team.getIsDeleted()) {
            throw new ResourceNotFoundException("团队不存在");
        }

        // 检查团队名称是否已被其他团队使用
        if (!team.getName().equals(request.getName()) && teamMapper.existsByName(request.getName())) {
            throw new BusinessException("团队名称已存在");
        }

        // 更新团队信息
        team.setName(request.getName());
        team.setDescription(request.getDescription());
        teamMapper.updateById(team);

        log.info("团队信息更新成功: teamId={}, name={}", teamId, request.getName());

        return getTeamDetail(teamId);
    }

    /**
     * 获取团队成员列表
     *
     * @param page 页码
     * @param size 页大小
     * @return 成员列表
     */
    public Page<TeamMemberResponse> getTeamMembers(int page, int size) {
        Long teamId = TeamContextHolder.getCurrentTeamId();
        Long userId = TeamContextHolder.getCurrentUserId();

        // 验证用户是否可以访问该团队
        teamMemberAccessValidator.validateTeamAccess(teamId, userId);

        QueryWrapper<TeamMember> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("team_id", teamId)
                   .eq("is_active", true)
                   .eq("is_deleted", false)
                   .orderByDesc("is_creator")
                   .orderByDesc("assigned_at");

        Page<TeamMember> memberPage = teamMemberMapper.selectPage(new Page<>(page, size), queryWrapper);
        
        // 转换为响应DTO
        List<TeamMemberResponse> memberResponses = memberPage.getRecords().stream()
                .map(this::convertToMemberResponse)
                .collect(Collectors.toList());

        Page<TeamMemberResponse> responsePage = new Page<>(page, size);
        responsePage.setRecords(memberResponses);
        responsePage.setTotal(memberPage.getTotal());
        responsePage.setPages(memberPage.getPages());

        return responsePage;
    }

    /**
     * 邀请成员（新的邀请-确认模式）
     *
     * @param request 邀请请求
     */
    @Transactional
    public void inviteMembers(InviteMembersRequest request) {
        Long teamId = TeamContextHolder.getCurrentTeamId();
        Long inviterId = TeamContextHolder.getCurrentUserId();

        try {
            // 检查速率限制
            rateLimitService.checkInvitationLimit(inviterId, request.getEmails().size());

            // 输入验证和清理
            List<String> cleanedEmails = inputSanitizer.sanitizeEmails(request.getEmails());
            String cleanedMessage = inputSanitizer.sanitizeInvitationMessage(request.getMessage());

            // 验证用户是否可以访问该团队
            teamMemberAccessValidator.validateTeamAccess(teamId, inviterId);

            // 检查权限
            permissionChecker.checkMemberManagePermission();

            // 检查团队成员数量限制
            int currentMemberCount = teamMemberMapper.selectCount(
                new QueryWrapper<TeamMember>()
                    .eq("team_id", teamId)
                    .eq("is_active", true)
                    .eq("is_deleted", false)
            ).intValue();

            if (currentMemberCount + cleanedEmails.size() > maxMembers) {
                throw new BusinessException("邀请成员数量超过团队最大成员限制（" + maxMembers + "人）");
            }

            // 使用新的邀请服务创建邀请记录
            teamInvitationService.createInvitations(teamId, inviterId, cleanedEmails, cleanedMessage);

            log.info("批量邀请成员完成: teamId={}, inviterCount={}", teamId, cleanedEmails.size());

        } catch (Exception e) {
            if (e instanceof BusinessException || e instanceof RateLimitException) {
                throw e; // 重新抛出业务异常和速率限制异常
            }
            log.error("邀请成员失败: teamId={}, inviterId={}, emailCount={}",
                    teamId, inviterId, request.getEmails().size(), e);
            throw DatabaseException.transactionRollback("INVITE_MEMBERS", e);
        }
    }

    /**
     * 获取团队邀请列表
     *
     * @param teamId 团队ID
     * @return 邀请列表
     */
    public List<com.teammanage.dto.response.TeamInvitationResponse> getTeamInvitations(Long teamId) {
        return teamInvitationService.getTeamInvitations(teamId);
    }





    /**
     * 移除团队成员
     * 
     * @param memberId 成员ID
     */
    @Transactional
    public void removeMember(Long memberId) {
        // 检查权限
        permissionChecker.checkMemberManagePermission();

        Long teamId = TeamContextHolder.getCurrentTeamId();
        Long currentUserId = TeamContextHolder.getCurrentUserId();

        TeamMember member = teamMemberMapper.selectById(memberId);
        if (member == null || member.getIsDeleted() || !member.getTeamId().equals(teamId)) {
            throw new ResourceNotFoundException("团队成员不存在");
        }

        // 不能移除自己
        if (member.getAccountId().equals(currentUserId)) {
            throw new BusinessException("不能移除自己");
        }

        // 不能移除其他创建者
        if (member.getIsCreator()) {
            throw new BusinessException("不能移除团队创建者");
        }

        // 软删除成员
        member.setIsDeleted(true);
        member.setIsActive(false);
        teamMemberMapper.updateById(member);

        log.info("团队成员移除成功: teamId={}, memberId={}, accountId={}",
                teamId, memberId, member.getAccountId());
    }

    /**
     * 更新团队成员状态
     *
     * @param memberId 成员ID
     * @param isActive 是否启用
     */
    @Transactional
    public void updateMemberStatus(Long memberId, Boolean isActive) {
        // 检查权限
        permissionChecker.checkMemberManagePermission();

        Long teamId = TeamContextHolder.getCurrentTeamId();
        Long currentUserId = TeamContextHolder.getCurrentUserId();

        TeamMember member = teamMemberMapper.selectById(memberId);
        if (member == null || member.getIsDeleted() || !member.getTeamId().equals(teamId)) {
            throw new ResourceNotFoundException("团队成员不存在");
        }

        // 不能修改自己的状态
        if (member.getAccountId().equals(currentUserId)) {
            throw new BusinessException("不能修改自己的状态");
        }

        // 不能修改创建者的状态
        if (member.getIsCreator()) {
            throw new BusinessException("不能修改团队创建者的状态");
        }

        // 更新成员状态
        member.setIsActive(isActive);
        teamMemberMapper.updateById(member);

        log.info("团队成员状态更新成功: teamId={}, memberId={}, accountId={}, isActive={}",
                teamId, memberId, member.getAccountId(), isActive);
    }



    /**
     * 删除团队
     *
     * 业务逻辑：
     * 1. 检查当前用户是否为团队创建者（通过权限检查器）
     * 2. 获取团队信息并验证团队存在且未被删除
     * 3. 双重验证：确保当前用户确实是团队创建者
     * 4. 软删除团队记录（设置is_deleted=true）
     * 5. 级联软删除所有团队成员关系
     * 6. 记录删除操作日志
     *
     * 注意：此操作使用软删除，数据不会从数据库中物理删除
     *
     * @throws InsufficientPermissionException 当用户不是团队创建者时
     * @throws ResourceNotFoundException 当团队不存在或已被删除时
     */
    @Transactional
    public void deleteTeam() {
        Long teamId = TeamContextHolder.getCurrentTeamId();
        Long currentUserId = TeamContextHolder.getCurrentUserId();

        // 验证用户是否可以访问该团队
        teamMemberAccessValidator.validateTeamAccess(teamId, currentUserId);

        // 检查权限 - 只有团队创建者可以删除团队
        permissionChecker.checkTeamManagePermission();

        // 获取团队信息
        Team team = teamMapper.selectById(teamId);
        if (team == null || team.getIsDeleted()) {
            throw new ResourceNotFoundException("团队不存在");
        }

        // 双重检查：确保当前用户是团队创建者
        if (!team.getCreatedBy().equals(currentUserId)) {
            throw new InsufficientPermissionException("只有团队创建者可以删除团队");
        }

        // 软删除团队
        team.setIsDeleted(true);
        teamMapper.updateById(team);

        // 软删除所有团队成员关系
        QueryWrapper<TeamMember> memberQueryWrapper = new QueryWrapper<>();
        memberQueryWrapper.eq("team_id", teamId)
                         .eq("is_deleted", false);

        List<TeamMember> teamMembers = teamMemberMapper.selectList(memberQueryWrapper);
        for (TeamMember member : teamMembers) {
            member.setIsDeleted(true);
            member.setIsActive(false);
            teamMemberMapper.updateById(member);
        }

        log.info("团队删除成功: teamId={}, teamName={}, creatorId={}, memberCount={}",
                teamId, team.getName(), currentUserId, teamMembers.size());
    }

    /**
     * 获取用户参与的团队列表
     *
     * @param userId 用户ID
     * @return 团队列表
     */
    public List<TeamDetailResponse> getUserTeams(Long userId) {
        List<TeamMember> teamMembers = teamMemberMapper.findByAccountId(userId);

        return teamMembers.stream()
                .map(member -> {
                    TeamDetailResponse response = getTeamDetail(member.getTeamId(), userId);
                    response.setLastAccessTime(member.getLastAccessTime());
                    response.setAssignedAt(member.getAssignedAt());
                    response.setIsActive(member.getIsActive());
                    return response;
                })
                .collect(Collectors.toList());
    }

    /**
     * 转换团队成员为响应DTO
     */
    private TeamMemberResponse convertToMemberResponse(TeamMember member) {
        Account account = accountMapper.selectById(member.getAccountId());
        
        TeamMemberResponse response = new TeamMemberResponse();
        response.setId(member.getId());
        response.setAccountId(member.getAccountId());
        response.setEmail(account != null ? account.getEmail() : "");
        response.setName(account != null ? account.getName() : "");
        // 设置角色信息（新）和isCreator（向后兼容）
        response.setRole(member.getRole());
        response.setIsCreator(member.getIsCreator());
        response.setAssignedAt(member.getAssignedAt());
        response.setLastAccessTime(member.getLastAccessTime());
        response.setIsActive(member.getIsActive());
        
        return response;
    }

    /**
     * 获取团队成员数量
     *
     * @param teamId 团队ID
     * @return 成员数量
     */
    public int getMemberCount(Long teamId) {
        return teamMemberMapper.countByTeamId(teamId);
    }

    /**
     * 获取用户创建的团队数量
     *
     * @param userId 用户ID
     * @return 创建的团队数量
     */
    public int getUserCreatedTeamCount(Long userId) {
        QueryWrapper<Team> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("created_by", userId)
                   .eq("is_deleted", false);
        return Math.toIntExact(teamMapper.selectCount(queryWrapper));
    }

}
