package com.teammanage.dto.request;

import jakarta.validation.constraints.NotNull;

/**
 * 响应邀请请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class RespondInvitationRequest {

    /**
     * 是否接受邀请
     */
    @NotNull(message = "请选择是否接受邀请")
    private Boolean accept;

    /**
     * 响应消息（可选）
     */
    private String message;

    // Getter and Setter methods
    public Boolean getAccept() { return accept; }
    public void setAccept(Boolean accept) { this.accept = accept; }

    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }
}
