package com.teammanage.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.teammanage.common.ApiResponse;
import com.teammanage.context.TeamContextHolder;
import com.teammanage.dto.request.UpdateUserProfileRequest;
import com.teammanage.dto.request.ValidatePasswordRequest;
import com.teammanage.dto.response.TeamDetailResponse;
import com.teammanage.dto.response.UserPersonalStatsResponse;
import com.teammanage.dto.response.UserProfileDetailResponse;
import com.teammanage.dto.response.UserProfileResponse;
import com.teammanage.entity.Account;
import com.teammanage.service.TeamService;
import com.teammanage.service.UserService;
import com.teammanage.util.SecurityUtil;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

/**
 * 用户控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/users")
@Tag(name = "用户管理", description = "用户信息管理相关接口")
@SecurityRequirement(name = "bearerAuth")
public class UserController {

    private static final Logger log = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserService userService;

    @Autowired
    private TeamService teamService;



    /**
     * 获取当前用户资料
     *
     * <p>获取当前登录用户的个人资料信息，包括基本信息、联系方式等。</p>
     *
     * <h3>返回信息包括：</h3>
     * <ul>
     *   <li>用户基本信息：姓名、邮箱、电话</li>
     *   <li>账户信息：注册时间、最后更新时间</li>
     *   <li>状态信息：账户状态、是否激活</li>
     * </ul>
     *
     * <h3>权限要求：</h3>
     * <ul>
     *   <li>需要有效的Account Token</li>
     *   <li>只能获取当前用户自己的资料</li>
     * </ul>
     *
     * @return 用户资料响应，包含用户的详细信息
     * @throws RuntimeException 当用户不存在或Token无效时抛出
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    @GetMapping("/profile")
    @Operation(summary = "获取用户资料", description = "获取当前用户的个人资料")
    public ApiResponse<UserProfileResponse> getUserProfile() {
        Long userId = SecurityUtil.getCurrentUserId();
        UserProfileResponse response = userService.getUserProfile(userId);
        return ApiResponse.success(response);
    }

    /**
     * 更新用户资料
     */
    @PutMapping("/profile")
    @Operation(summary = "更新用户资料", description = "更新当前用户的个人资料")
    public ApiResponse<UserProfileResponse> updateUserProfile(@Valid @RequestBody UpdateUserProfileRequest request) {
        Long userId = SecurityUtil.getCurrentUserId();
        UserProfileResponse response = userService.updateUserProfile(userId, request);
        return ApiResponse.success("用户资料更新成功", response);
    }





    /**
     * 验证密码
     */
    @PostMapping("/validate-password")
    @Operation(summary = "验证密码", description = "验证当前用户的密码是否正确")
    public ApiResponse<Boolean> validatePassword(@RequestBody @Valid ValidatePasswordRequest request) {
        Long userId = SecurityUtil.getCurrentUserId();
        boolean isValid = userService.validatePassword(userId, request.getPassword());
        return ApiResponse.success(isValid);
    }

    /**
     * 获取用户个人统计数据
     */
    @GetMapping("/personal-stats")
    @Operation(summary = "获取用户个人统计数据", description = "获取当前用户的车辆、人员、预警、告警统计数据")
    public ApiResponse<Map<String, Object>> getUserPersonalStats() {
        Long userId = SecurityUtil.getCurrentUserId();

        try {
            // 获取用户团队信息来计算统计数据
            List<TeamDetailResponse> userTeams = teamService.getUserTeams(userId);

            // 计算统计数据（这里使用简单的计算逻辑，实际应该从相关业务表查询）
            int vehicles = 0;
            int personnel = 0;
            int warnings = 0;
            int alerts = 0;

            // 遍历用户的团队，累计统计数据
            for (TeamDetailResponse team : userTeams) {
                if (team.getStats() != null) {
                    Map<String, Object> teamStats = team.getStats();
                    vehicles += (Integer) teamStats.getOrDefault("vehicles", 0);
                    personnel += (Integer) teamStats.getOrDefault("personnel", 0);
                    warnings += (Integer) teamStats.getOrDefault("expiring", 0);
                    alerts += (Integer) teamStats.getOrDefault("overdue", 0);
                }
            }

            Map<String, Object> stats = new HashMap<>();
            stats.put("vehicles", vehicles);
            stats.put("personnel", personnel);
            stats.put("warnings", warnings);
            stats.put("alerts", alerts);

            return ApiResponse.success(stats);
        } catch (Exception e) {
            log.error("获取用户个人统计数据失败", e);
            // 如果获取失败，返回默认的模拟数据
            Map<String, Object> stats = new HashMap<>();
            stats.put("vehicles", 48);
            stats.put("personnel", 16);
            stats.put("warnings", 5);
            stats.put("alerts", 3);
            return ApiResponse.success(stats);
        }
    }

    /**
     * 获取用户详细信息
     */
    @GetMapping("/profile-detail")
    @Operation(summary = "获取用户详细信息", description = "获取当前用户的详细个人信息")
    public ApiResponse<Map<String, Object>> getUserProfileDetail() {
        Long userId = SecurityUtil.getCurrentUserId();

        try {
            // 获取用户基本信息
            Account account = userService.getUserById(userId);

            // 获取用户团队统计信息
            List<TeamDetailResponse> userTeams = teamService.getUserTeams(userId);
            int teamCount = userTeams.size();

            // 获取最后登录的团队信息
            String lastLoginTeam = "暂无团队";
            if (!userTeams.isEmpty()) {
                // 简单取第一个团队作为最后登录团队，实际应该从用户会话中获取
                lastLoginTeam = userTeams.get(0).getName();
            }

            Map<String, Object> profileDetail = new HashMap<>();
            profileDetail.put("name", account.getName());
            profileDetail.put("position", "团队成员"); // 可以根据用户在团队中的角色动态设置
            profileDetail.put("email", account.getEmail());
            profileDetail.put("telephone", account.getTelephone() != null ? account.getTelephone() : ""); // 使用telephone字段
            profileDetail.put("registerDate", account.getCreatedAt().toLocalDate().toString());
            profileDetail.put("lastLoginTime", account.getUpdatedAt().toString());
            profileDetail.put("lastLoginTeam", lastLoginTeam);
            profileDetail.put("teamCount", teamCount);
            profileDetail.put("avatar", ""); // 头像URL，暂时为空

            return ApiResponse.success(profileDetail);
        } catch (Exception e) {
            log.error("获取用户详细信息失败", e);
            return ApiResponse.error(500, "获取用户详细信息失败");
        }
    }

}
