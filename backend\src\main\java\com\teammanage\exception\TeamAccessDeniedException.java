package com.teammanage.exception;

/**
 * 团队访问被拒绝异常
 * 
 * 当用户尝试访问团队但由于以下原因被拒绝时抛出：
 * - 用户在团队中的状态为已禁用/已停用
 * - 用户已不是团队成员
 * - 其他团队访问限制
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class TeamAccessDeniedException extends RuntimeException {

    private final Integer code;
    private final Long teamId;
    private final Long userId;
    private final String reason;

    /**
     * 构造函数
     * 
     * @param message 错误消息
     */
    public TeamAccessDeniedException(String message) {
        super(message);
        this.code = 403;
        this.teamId = null;
        this.userId = null;
        this.reason = null;
    }

    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param teamId 团队ID
     * @param userId 用户ID
     */
    public TeamAccessDeniedException(String message, Long teamId, Long userId) {
        super(message);
        this.code = 403;
        this.teamId = teamId;
        this.userId = userId;
        this.reason = null;
    }

    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param teamId 团队ID
     * @param userId 用户ID
     * @param reason 拒绝原因
     */
    public TeamAccessDeniedException(String message, Long teamId, Long userId, String reason) {
        super(message);
        this.code = 403;
        this.teamId = teamId;
        this.userId = userId;
        this.reason = reason;
    }

    /**
     * 构造函数
     * 
     * @param code 错误码
     * @param message 错误消息
     * @param teamId 团队ID
     * @param userId 用户ID
     * @param reason 拒绝原因
     */
    public TeamAccessDeniedException(Integer code, String message, Long teamId, Long userId, String reason) {
        super(message);
        this.code = code;
        this.teamId = teamId;
        this.userId = userId;
        this.reason = reason;
    }

    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param cause 原因
     */
    public TeamAccessDeniedException(String message, Throwable cause) {
        super(message, cause);
        this.code = 403;
        this.teamId = null;
        this.userId = null;
        this.reason = null;
    }

    // Getter 方法

    public Integer getCode() {
        return code;
    }

    public Long getTeamId() {
        return teamId;
    }

    public Long getUserId() {
        return userId;
    }

    public String getReason() {
        return reason;
    }

    // 静态工厂方法，用于创建常见的异常情况

    /**
     * 创建用户被禁用的异常
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 异常实例
     */
    public static TeamAccessDeniedException userDisabled(Long teamId, Long userId) {
        return new TeamAccessDeniedException(
            "您的账户已在此团队中被停用",
            teamId,
            userId,
            "USER_DISABLED"
        );
    }

    /**
     * 创建用户被停用的异常
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 异常实例
     */
    public static TeamAccessDeniedException userDeactivated(Long teamId, Long userId) {
        return new TeamAccessDeniedException(
            "您的账户已在此团队中被停用",
            teamId,
            userId,
            "USER_DEACTIVATED"
        );
    }

    /**
     * 创建用户不是团队成员的异常
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 异常实例
     */
    public static TeamAccessDeniedException notTeamMember(Long teamId, Long userId) {
        return new TeamAccessDeniedException(
            "您不是该团队的成员",
            teamId,
            userId,
            "NOT_TEAM_MEMBER"
        );
    }

    /**
     * 创建用户已被移除的异常
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 异常实例
     */
    public static TeamAccessDeniedException userRemoved(Long teamId, Long userId) {
        return new TeamAccessDeniedException(
            "您已不是该团队的成员",
            teamId,
            userId,
            "USER_REMOVED"
        );
    }

    /**
     * 创建通用的团队访问被拒绝异常
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @param customMessage 自定义消息
     * @return 异常实例
     */
    public static TeamAccessDeniedException accessDenied(Long teamId, Long userId, String customMessage) {
        return new TeamAccessDeniedException(
            customMessage,
            teamId,
            userId,
            "ACCESS_DENIED"
        );
    }

    @Override
    public String toString() {
        return "TeamAccessDeniedException{" +
                "code=" + code +
                ", teamId=" + teamId +
                ", userId=" + userId +
                ", reason='" + reason + '\'' +
                ", message='" + getMessage() + '\'' +
                '}';
    }
}
