# 团队选择逻辑优化

## 问题描述

在个人中心页面的团队列表中，"当前"标签的显示逻辑存在问题：
- 用户刚登录时，即使没有主动选择过任何团队，如果Token中有团队信息（可能是之前登录的残留），就会显示"当前"标签
- 无法区分"用户从未选择团队"和"用户已选择团队但返回个人中心"这两种状态

## 期望行为

1. **初始登录状态**：用户刚登录进入个人中心时，如果还没有选择过任何团队，团队列表中不应该显示"当前"标签
2. **选择团队后**：当用户点击进入某个团队（切换到团队环境）后，再返回个人中心时，应该在刚才进入的那个团队上显示"当前"标签

## 解决方案

### 1. 新增团队选择历史管理

创建了 `frontend/src/utils/teamSelectionUtils.ts` 工具文件，用于管理用户的团队选择历史：

- `recordTeamSelection(userId, teamId)`: 记录用户选择了某个团队
- `hasUserSelectedTeam(userId, teamId)`: 检查用户是否曾经选择过某个团队
- `getUserTeamSelectionHistory(userId)`: 获取用户的团队选择历史
- `clearUserTeamSelectionHistory(userId)`: 清除用户的团队选择历史

### 2. 修改当前团队判断逻辑

在 `TeamListCard.tsx` 中修改了 `hasRealCurrentTeam` 的判断条件：

```typescript
const hasRealCurrentTeam = !!(
  hasTeamInToken &&                                    // Token中有团队信息
  currentTokenTeamId &&                               // Token中的团队ID有效
  currentTeam &&                                      // initialState中有团队信息
  currentTeam.id === currentTokenTeamId &&           // 两者的团队ID一致
  currentUserId &&                                    // 有用户ID
  hasUserSelectedTeam(currentUserId, currentTokenTeamId) // 用户曾经主动选择过这个团队
);
```

### 3. 在团队切换时记录选择历史

在以下位置添加了团队选择记录：

- `TeamListCard.tsx` - 个人中心团队切换
- `team-select/index.tsx` - 团队选择页面
- `TeamListContent.tsx` - 团队列表页面切换

当团队切换成功时，调用 `recordTeamSelection(userId, teamId)` 记录用户的选择。

### 4. 完善全局状态管理

在 `app.tsx` 中添加了 `fetchTeamInfo` 函数和 `currentTeam` 状态管理：

```typescript
const fetchTeamInfo = async (): Promise<TeamDetailResponse | undefined> => {
  try {
    if (!hasTeamInCurrentToken()) {
      return undefined;
    }
    return await TeamService.getCurrentTeamDetail();
  } catch (error) {
    console.error('获取团队信息失败:', error);
    return undefined;
  }
};
```

## 技术实现细节

### 数据存储

使用 localStorage 存储用户的团队选择历史：
- 键格式：`user_team_selection_history_{userId}`
- 值格式：JSON数组，包含用户选择过的团队ID列表

### 状态同步

确保以下状态的一致性：
1. Token中的团队信息（最新的认证状态）
2. initialState中的团队信息（全局状态）
3. 用户的团队选择历史（本地存储）

### 错误处理

- 所有localStorage操作都包含try-catch错误处理
- 在获取团队信息失败时提供降级处理
- 添加了详细的调试日志

## 测试

更新了 `TeamListCard.test.tsx`，添加了以下测试用例：
- 测试用户未主动选择团队时不显示"当前"标签
- 测试用户主动选择团队后显示"当前"标签

## 使用示例

```typescript
import { recordTeamSelection, hasUserSelectedTeam } from '@/utils/teamSelectionUtils';

// 记录用户选择团队
recordTeamSelection(123, 456);

// 检查用户是否选择过团队
const hasSelected = hasUserSelectedTeam(123, 456); // true
```

## 注意事项

1. 团队选择历史存储在localStorage中，用户清除浏览器数据会丢失
2. 不同用户的选择历史是独立存储的
3. 该逻辑只影响"当前"标签的显示，不影响实际的团队切换功能
4. 在用户注销时可以考虑清除团队选择历史
