package com.teammanage.dto.response;

/**
 * 用户详细信息响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class UserProfileDetailResponse {

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 职位
     */
    private String position;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 电话
     */
    private String telephone;

    /**
     * 注册日期
     */
    private String registerDate;

    /**
     * 最后登录时间
     */
    private String lastLoginTime;

    /**
     * 最后登录团队
     */
    private String lastLoginTeam;

    /**
     * 团队数量
     */
    private Integer teamCount;

    /**
     * 头像URL
     */
    private String avatar;

    // 构造函数
    public UserProfileDetailResponse() {}

    // Getter and Setter methods
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getPosition() { return position; }
    public void setPosition(String position) { this.position = position; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public String getTelephone() { return telephone; }
    public void setTelephone(String telephone) { this.telephone = telephone; }

    public String getRegisterDate() { return registerDate; }
    public void setRegisterDate(String registerDate) { this.registerDate = registerDate; }

    public String getLastLoginTime() { return lastLoginTime; }
    public void setLastLoginTime(String lastLoginTime) { this.lastLoginTime = lastLoginTime; }

    public String getLastLoginTeam() { return lastLoginTeam; }
    public void setLastLoginTeam(String lastLoginTeam) { this.lastLoginTeam = lastLoginTeam; }

    public Integer getTeamCount() { return teamCount; }
    public void setTeamCount(Integer teamCount) { this.teamCount = teamCount; }

    public String getAvatar() { return avatar; }
    public void setAvatar(String avatar) { this.avatar = avatar; }

    @Override
    public String toString() {
        return "UserProfileDetailResponse{" +
                "name='" + name + '\'' +
                ", position='" + position + '\'' +
                ", email='" + email + '\'' +
                ", telephone='" + telephone + '\'' +
                ", registerDate='" + registerDate + '\'' +
                ", lastLoginTime='" + lastLoginTime + '\'' +
                ", lastLoginTeam='" + lastLoginTeam + '\'' +
                ", teamCount=" + teamCount +
                ", avatar='" + avatar + '\'' +
                '}';
    }
}
