/**
 * 团队管理模态框组件
 *
 * 功能特性：
 * - 团队成员列表和管理
 * - 团队邀请功能
 * - 团队信息编辑
 * - 团队删除功能
 * - 权限控制，只有团队创建者可以访问管理功能
 */

import React, { useState, useEffect } from 'react';
import {
  Modal,
  Card,
  Button,
  Space,
  Input,
  Tag,
  Typography,
  Popconfirm,
  Select,
  Tooltip,
  Row,
  Col,
  Form,
  message,
  Avatar,
  Dropdown,
  Alert,
  Spin,
  Tabs
} from 'antd';
import { ProTable } from '@ant-design/pro-components';
import {
  DeleteOutlined,
  SearchOutlined,
  MailOutlined,
  UserOutlined,
  CrownOutlined,
  PlusOutlined,
  TeamOutlined,
  EditOutlined,
  SaveOutlined,
  ExclamationCircleOutlined,
  UserAddOutlined,
  MoreOutlined
} from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import dayjs from 'dayjs';

// 导入服务和类型
import { TeamService } from '@/services/team';
import { InvitationService } from '@/services/invitation';
import type { 
  TeamDetailResponse, 
  TeamMemberResponse, 
  TeamInvitationResponse,
  UpdateTeamRequest,
  InviteMembersRequest
} from '@/types/api';
import { InvitationStatus } from '@/types/api';
import InvitationStatusComponent from '@/components/InvitationStatus';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { TabPane } = Tabs;

interface TeamManagementModalProps {
  visible: boolean;
  onCancel: () => void;
  team: TeamDetailResponse | null;
  onRefresh: () => void;
}

const TeamManagementModal: React.FC<TeamManagementModalProps> = ({
  visible,
  onCancel,
  team,
  onRefresh
}) => {
  const [loading, setLoading] = useState(false);
  const [members, setMembers] = useState<TeamMemberResponse[]>([]);
  const [invitations, setInvitations] = useState<TeamInvitationResponse[]>([]);
  const [searchText, setSearchText] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  
  // 团队编辑相关状态
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [form] = Form.useForm();
  
  // 团队删除相关状态
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState('');
  const [deleting, setDeleting] = useState(false);
  
  // 邀请成员相关状态
  const [inviteModalVisible, setInviteModalVisible] = useState(false);
  const [inviteForm] = Form.useForm();

  useEffect(() => {
    if (visible && team) {
      fetchMembers();
      fetchInvitations();
    }
  }, [visible, team]);

  // 获取团队成员列表
  const fetchMembers = async () => {
    if (!team) return;
    
    try {
      setLoading(true);
      const response = await TeamService.getTeamMembers();
      setMembers(response.list || []);
    } catch (error) {
      console.error('获取团队成员失败:', error);
      message.error('获取团队成员失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取邀请列表
  const fetchInvitations = async () => {
    if (!team) return;

    try {
      const response = await InvitationService.getCurrentTeamInvitations();
      setInvitations(response || []);
    } catch (error) {
      console.error('获取邀请列表失败:', error);
    }
  };

  // 移除团队成员
  const handleRemoveMember = async (memberId: number, memberName: string) => {
    try {
      await TeamService.removeMember(memberId);
      message.success(`已移除成员 ${memberName}`);
      fetchMembers();
      onRefresh();
    } catch (error) {
      console.error('移除成员失败:', error);
      message.error('移除成员失败');
    }
  };

  // 邀请成员
  const handleInviteMembers = async (values: { emails: string; message?: string }) => {
    try {
      const emailList = values.emails.split(',').map(email => email.trim()).filter(email => email);
      const request: InviteMembersRequest = {
        emails: emailList,
        message: values.message
      };
      
      await TeamService.inviteMembers(request);
      message.success('邀请已发送');
      setInviteModalVisible(false);
      inviteForm.resetFields();
      fetchInvitations();
    } catch (error) {
      console.error('邀请成员失败:', error);
      message.error('邀请成员失败');
    }
  };

  // 更新团队信息
  const handleUpdateTeam = async (values: UpdateTeamRequest) => {
    if (!team) return;
    
    try {
      setUpdating(true);
      await TeamService.updateCurrentTeam(values);
      message.success('团队信息更新成功');
      setEditModalVisible(false);
      form.resetFields();
      onRefresh();
    } catch (error) {
      console.error('更新团队信息失败:', error);
      message.error('更新团队信息失败');
    } finally {
      setUpdating(false);
    }
  };

  // 删除团队
  const handleDeleteTeam = async () => {
    if (!team || deleteConfirmText !== team.name) {
      message.error('请输入正确的团队名称');
      return;
    }
    
    try {
      setDeleting(true);
      await TeamService.deleteCurrentTeam();
      message.success('团队已删除');
      setDeleteModalVisible(false);
      onCancel();
      onRefresh();
    } catch (error) {
      console.error('删除团队失败:', error);
      message.error('删除团队失败');
    } finally {
      setDeleting(false);
    }
  };

  // 权限检查
  const hasManagePermission = team?.isCreator || false;

  if (!team) {
    return null;
  }

  // 成员表格列定义
  const memberColumns: ProColumns<TeamMemberResponse>[] = [
    {
      title: '成员信息',
      dataIndex: 'name',
      key: 'name',
      render: (_, record) => (
        <Space>
          <Avatar size="small" icon={<UserOutlined />}>
            {record.name?.charAt(0)?.toUpperCase()}
          </Avatar>
          <div>
            <div style={{ fontWeight: 500 }}>{record.name}</div>
            <div style={{ fontSize: 12, color: '#666' }}>{record.email}</div>
          </div>
        </Space>
      ),
    },
    {
      title: '角色',
      dataIndex: 'isCreator',
      key: 'role',
      render: (_, record) => (
        <Tag
          icon={record.isCreator ? <CrownOutlined /> : <UserOutlined />}
          color={record.isCreator ? 'gold' : 'blue'}
        >
          {record.isCreator ? '管理员' : '成员'}
        </Tag>
      ),
    },
    {
      title: '加入时间',
      dataIndex: 'assignedAt',
      key: 'assignedAt',
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '最后访问',
      dataIndex: 'lastAccessTime',
      key: 'lastAccessTime',
      render: (text) => text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '-',
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive) => (
        <Tag color={isActive ? 'success' : 'error'}>
          {isActive ? '启用' : '停用'}
        </Tag>
      ),
    },
  ];

  if (hasManagePermission) {
    memberColumns.push({
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          {!record.isCreator && (
            <Popconfirm
              title="确定要移除这个成员吗？"
              onConfirm={() => handleRemoveMember(record.id, record.name)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="text"
                danger
                size="small"
                icon={<DeleteOutlined />}
              >
                移除
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    });
  }

  // 邀请记录表格列定义
  const invitationColumns: ProColumns<TeamInvitationResponse>[] = [
    {
      title: '邀请信息',
      dataIndex: 'inviteeEmail',
      key: 'inviteeEmail',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.inviteeEmail}</div>
          {record.inviteeName && (
            <div style={{ fontSize: 12, color: '#666' }}>{record.inviteeName}</div>
          )}
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <InvitationStatusComponent status={status} />
      ),
    },
    {
      title: '邀请时间',
      dataIndex: 'invitedAt',
      key: 'invitedAt',
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '过期时间',
      dataIndex: 'expiresAt',
      key: 'expiresAt',
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '邀请人',
      dataIndex: 'inviterName',
      key: 'inviterName',
    },
  ];

  if (hasManagePermission) {
    invitationColumns.push({
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          {record.canBeCancelled && (
            <Popconfirm
              title="确定要取消这个邀请吗？"
              onConfirm={() => handleCancelInvitation(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="text"
                danger
                size="small"
                icon={<DeleteOutlined />}
              >
                取消
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    });
  }

  // 取消邀请
  const handleCancelInvitation = async (invitationId: number) => {
    try {
      await InvitationService.cancelInvitation(invitationId);
      message.success('邀请已取消');
      fetchInvitations();
    } catch (error) {
      console.error('取消邀请失败:', error);
      message.error('取消邀请失败');
    }
  };

  return (
    <>
      <Modal
        title={
          <Space>
            <TeamOutlined />
            <span>团队管理 - {team.name}</span>
          </Space>
        }
        open={visible}
        onCancel={onCancel}
        width={1200}
        footer={null}
        destroyOnClose
      >
        <Tabs defaultActiveKey="members">
          <TabPane tab="团队成员" key="members">
            <Card
              title="团队成员列表"
              extra={
                hasManagePermission && (
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => setInviteModalVisible(true)}
                  >
                    邀请成员
                  </Button>
                )
              }
            >
              <ProTable
                columns={memberColumns}
                dataSource={members}
                rowKey="id"
                loading={loading}
                search={false}
                pagination={{
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total) => `共 ${total} 名成员`,
                  pageSize: 10,
                }}
                options={{
                  reload: () => fetchMembers(),
                  setting: false,
                  density: false,
                }}
                size="small"
              />
            </Card>
          </TabPane>
          
          <TabPane tab="邀请记录" key="invitations">
            <Card title="邀请记录">
              <ProTable
                columns={invitationColumns}
                dataSource={invitations}
                rowKey="id"
                loading={loading}
                search={false}
                pagination={{
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total) => `共 ${total} 条邀请`,
                  pageSize: 10,
                }}
                options={{
                  reload: () => fetchInvitations(),
                  setting: false,
                  density: false,
                }}
                size="small"
              />
            </Card>
          </TabPane>
          
          {hasManagePermission && (
            <TabPane tab="团队设置" key="settings">
              <Card title="团队设置">
                <Space direction="vertical" style={{ width: '100%' }} size="large">
                  <div>
                    <Title level={5}>基本信息</Title>
                    <Space>
                      <Button
                        icon={<EditOutlined />}
                        onClick={() => {
                          form.setFieldsValue({
                            name: team.name,
                            description: team.description
                          });
                          setEditModalVisible(true);
                        }}
                      >
                        编辑团队信息
                      </Button>
                    </Space>
                  </div>
                  
                  <div>
                    <Title level={5} type="danger">危险操作</Title>
                    <Alert
                      message="删除团队"
                      description="删除团队后，所有团队数据将无法恢复，请谨慎操作。"
                      type="warning"
                      showIcon
                      action={
                        <Button
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => setDeleteModalVisible(true)}
                        >
                          删除团队
                        </Button>
                      }
                    />
                  </div>
                </Space>
              </Card>
            </TabPane>
          )}
        </Tabs>
      </Modal>

      {/* 邀请成员模态框 */}
      <Modal
        title="邀请成员"
        open={inviteModalVisible}
        onCancel={() => {
          setInviteModalVisible(false);
          inviteForm.resetFields();
        }}
        footer={null}
      >
        <Form
          form={inviteForm}
          layout="vertical"
          onFinish={handleInviteMembers}
        >
          <Form.Item
            name="emails"
            label="邮箱地址"
            rules={[{ required: true, message: '请输入邮箱地址' }]}
          >
            <TextArea
              rows={4}
              placeholder="请输入邮箱地址，多个邮箱用逗号分隔"
            />
          </Form.Item>
          
          <Form.Item
            name="message"
            label="邀请消息（可选）"
          >
            <TextArea
              rows={3}
              placeholder="输入邀请消息..."
            />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                发送邀请
              </Button>
              <Button onClick={() => {
                setInviteModalVisible(false);
                inviteForm.resetFields();
              }}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑团队信息模态框 */}
      <Modal
        title="编辑团队信息"
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          form.resetFields();
        }}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleUpdateTeam}
        >
          <Form.Item
            name="name"
            label="团队名称"
            rules={[{ required: true, message: '请输入团队名称' }]}
          >
            <Input placeholder="请输入团队名称" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="团队描述"
          >
            <TextArea
              rows={4}
              placeholder="请输入团队描述"
            />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={updating}>
                保存
              </Button>
              <Button onClick={() => {
                setEditModalVisible(false);
                form.resetFields();
              }}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 删除团队确认模态框 */}
      <Modal
        title="删除团队"
        open={deleteModalVisible}
        onCancel={() => {
          setDeleteModalVisible(false);
          setDeleteConfirmText('');
        }}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              setDeleteModalVisible(false);
              setDeleteConfirmText('');
            }}
          >
            取消
          </Button>,
          <Button
            key="delete"
            type="primary"
            danger
            loading={deleting}
            disabled={deleteConfirmText !== team.name}
            onClick={handleDeleteTeam}
          >
            确认删除
          </Button>,
        ]}
      >
        <Alert
          message="警告"
          description="删除团队是不可逆的操作，将会删除所有团队数据。"
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <div>
          <Text>请输入团队名称 <Text code>{team.name}</Text> 来确认删除：</Text>
          <Input
            value={deleteConfirmText}
            onChange={(e) => setDeleteConfirmText(e.target.value)}
            placeholder={`请输入 ${team.name}`}
            style={{ marginTop: 8 }}
          />
        </div>
      </Modal>
    </>
  );
};

export default TeamManagementModal;
