package com.teammanage.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * 订阅套餐实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

@TableName("subscription_plan")
public class SubscriptionPlan extends BaseEntity {

    /**
     * 套餐名称
     */
    private String name;

    /**
     * 套餐说明
     */
    private String description;

    /**
     * 数据数量上限
     */
    private Integer maxSize;

    /**
     * 价格(元/月)
     */
    private BigDecimal price;

    /**
     * 是否启用
     */
    private Boolean isActive;

    // 手动添加getter/setter方法
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public Integer getMaxSize() { return maxSize; }
    public void setMaxSize(Integer maxSize) { this.maxSize = maxSize; }

    public BigDecimal getPrice() { return price; }
    public void setPrice(BigDecimal price) { this.price = price; }

    public Boolean getIsActive() { return isActive; }
    public void setIsActive(Boolean isActive) { this.isActive = isActive; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        SubscriptionPlan that = (SubscriptionPlan) o;
        return Objects.equals(name, that.name) &&
               Objects.equals(description, that.description) &&
               Objects.equals(maxSize, that.maxSize) &&
               Objects.equals(price, that.price) &&
               Objects.equals(isActive, that.isActive);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), name, description, maxSize, price, isActive);
    }

}
