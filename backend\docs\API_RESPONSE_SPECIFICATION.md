# API响应规范说明

## 概述

本文档说明了团队管理系统后端API的统一响应规范，确保所有API响应都遵循一致的格式。

## 核心原则

### 1. HTTP状态码统一返回200

- **所有API响应的HTTP状态码都统一返回200 OK**
- 无论是成功还是异常情况，HTTP层面都返回200
- 这样可以避免前端因HTTP状态码导致的网络层异常处理

### 2. 业务状态码在响应体中处理

真正的成功/失败状态通过ApiResponse中的`code`字段来表示：

- **成功**：`code = 200`
- **业务异常**：`code = 400, 404, 403` 等错误状态码
- **系统异常**：`code = 500` 内部服务器错误

### 3. 统一响应格式

所有API响应都使用相同的JSON结构：

```json
{
  "code": 200,
  "message": "success",
  "data": {...},
  "timestamp": "2024-01-01 12:00:00"
}
```

## 响应字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| code | Integer | 业务状态码，200表示成功，其他表示各种错误 |
| message | String | 响应消息，成功时为"success"，失败时为具体错误信息 |
| data | Object | 响应数据，成功时包含实际数据，失败时通常为null |
| timestamp | String | 响应时间戳，格式为"yyyy-MM-dd HH:mm:ss" |

## 状态码定义

### 成功状态码
- `200`: 操作成功

### 客户端错误状态码
- `400`: 请求参数错误或业务逻辑错误
- `401`: 未认证，需要登录
- `403`: 权限不足，已认证但无权限访问
- `404`: 资源不存在

### 服务器错误状态码
- `500`: 服务器内部错误

## 异常处理机制

### 全局异常处理器

`GlobalExceptionHandler` 负责捕获所有异常并转换为统一的响应格式：

1. **业务异常** (`BusinessException`): 返回自定义错误码和消息
2. **资源不存在异常** (`ResourceNotFoundException`): 返回404状态码
3. **权限不足异常** (`InsufficientPermissionException`): 返回403状态码
4. **认证异常** (`AuthenticationException`): 返回401状态码
5. **团队访问被拒绝异常** (`TeamAccessDeniedException`): 返回403状态码
6. **参数验证异常**: 返回400状态码
7. **系统异常**: 返回500状态码

### Spring Security异常处理

- **认证入口点** (`JwtAuthenticationEntryPoint`): 处理未认证请求
- **访问拒绝处理器** (`JwtAccessDeniedHandler`): 处理权限不足请求

## 前端处理方式

前端应该：

1. **忽略HTTP状态码**：所有请求都会返回200，不需要处理HTTP错误
2. **检查响应体中的code字段**：根据code字段判断请求是否成功
3. **统一错误处理**：基于code字段进行错误处理和用户提示

### 前端示例代码

```typescript
// 请求拦截器 - 检查业务状态码
if (response.data.code !== 200) {
  // 处理业务错误
  switch (response.data.code) {
    case 401:
      // 处理认证失败
      break;
    case 403:
      // 处理权限不足
      break;
    case 404:
      // 处理资源不存在
      break;
    default:
      // 处理其他错误
      break;
  }
}
```

## 优势

1. **简化前端处理**：前端不需要处理复杂的HTTP状态码
2. **统一错误处理**：所有错误都通过相同的机制处理
3. **更好的调试体验**：错误信息更加明确和一致
4. **避免网络层异常**：减少因HTTP状态码导致的意外错误

## 注意事项

1. 所有Controller方法都应该返回`ApiResponse<T>`类型
2. Service层抛出的异常会被全局异常处理器自动捕获和转换
3. 自定义异常应该继承相应的基础异常类
4. 错误消息应该对用户友好，避免暴露系统内部信息
