package com.teammanage.service;

import com.teammanage.exception.BusinessException;
import com.teammanage.exception.DatabaseException;
import com.teammanage.exception.NetworkException;
import com.teammanage.exception.RateLimitException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 错误监控服务
 * 
 * 提供错误统计、监控和告警功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class ErrorMonitoringService {

    private static final Logger log = LoggerFactory.getLogger(ErrorMonitoringService.class);
    private static final Logger errorLog = LoggerFactory.getLogger("ERROR_MONITOR");
    
    // 错误统计计数器
    private final Map<String, AtomicLong> errorCounters = new ConcurrentHashMap<>();
    
    // 错误详情记录（最近100条）
    private final Map<String, ErrorRecord> recentErrors = new ConcurrentHashMap<>();
    private static final int MAX_RECENT_ERRORS = 100;
    
    @Autowired
    private RateLimitService rateLimitService;

    /**
     * 记录业务异常
     * 
     * @param exception 业务异常
     * @param userId 用户ID
     * @param operation 操作类型
     * @param additionalInfo 附加信息
     */
    public void recordBusinessException(BusinessException exception, Long userId, String operation, Map<String, Object> additionalInfo) {
        String errorType = "BUSINESS_ERROR";
        incrementErrorCounter(errorType);
        
        ErrorRecord record = new ErrorRecord(
            errorType,
            exception.getMessage(),
            userId,
            operation,
            exception.getCode(),
            additionalInfo
        );
        
        recordError(record);
        
        errorLog.warn("业务异常记录: userId={}, operation={}, code={}, message={}, additionalInfo={}", 
                userId, operation, exception.getCode(), exception.getMessage(), additionalInfo);
    }

    /**
     * 记录数据库异常
     * 
     * @param exception 数据库异常
     * @param userId 用户ID
     * @param operation 操作类型
     * @param additionalInfo 附加信息
     */
    public void recordDatabaseException(DatabaseException exception, Long userId, String operation, Map<String, Object> additionalInfo) {
        String errorType = "DATABASE_ERROR";
        incrementErrorCounter(errorType);
        
        Map<String, Object> extendedInfo = new HashMap<>(additionalInfo != null ? additionalInfo : new HashMap<>());
        extendedInfo.put("operation", exception.getOperation());
        extendedInfo.put("details", exception.getDetails());
        
        ErrorRecord record = new ErrorRecord(
            errorType,
            exception.getMessage(),
            userId,
            operation,
            exception.getCode(),
            extendedInfo
        );
        
        recordError(record);
        
        errorLog.error("数据库异常记录: userId={}, operation={}, dbOperation={}, details={}, message={}", 
                userId, operation, exception.getOperation(), exception.getDetails(), exception.getMessage(), exception);
    }

    /**
     * 记录网络异常
     * 
     * @param exception 网络异常
     * @param userId 用户ID
     * @param operation 操作类型
     * @param additionalInfo 附加信息
     */
    public void recordNetworkException(NetworkException exception, Long userId, String operation, Map<String, Object> additionalInfo) {
        String errorType = "NETWORK_ERROR";
        incrementErrorCounter(errorType);
        
        Map<String, Object> extendedInfo = new HashMap<>(additionalInfo != null ? additionalInfo : new HashMap<>());
        extendedInfo.put("service", exception.getService());
        extendedInfo.put("networkOperation", exception.getOperation());
        extendedInfo.put("timeout", exception.getTimeout());
        
        ErrorRecord record = new ErrorRecord(
            errorType,
            exception.getMessage(),
            userId,
            operation,
            exception.getCode(),
            extendedInfo
        );
        
        recordError(record);
        
        errorLog.error("网络异常记录: userId={}, operation={}, service={}, networkOperation={}, timeout={}, message={}", 
                userId, operation, exception.getService(), exception.getOperation(), exception.getTimeout(), exception.getMessage(), exception);
    }

    /**
     * 记录速率限制异常
     * 
     * @param exception 速率限制异常
     * @param userId 用户ID
     * @param operation 操作类型
     * @param additionalInfo 附加信息
     */
    public void recordRateLimitException(RateLimitException exception, Long userId, String operation, Map<String, Object> additionalInfo) {
        String errorType = "RATE_LIMIT_ERROR";
        incrementErrorCounter(errorType);
        
        Map<String, Object> extendedInfo = new HashMap<>(additionalInfo != null ? additionalInfo : new HashMap<>());
        extendedInfo.put("limitType", exception.getLimitType());
        extendedInfo.put("retryAfter", exception.getRetryAfter());
        extendedInfo.put("currentCount", exception.getCurrentCount());
        extendedInfo.put("maxCount", exception.getMaxCount());
        
        ErrorRecord record = new ErrorRecord(
            errorType,
            exception.getMessage(),
            userId,
            operation,
            exception.getCode(),
            extendedInfo
        );
        
        recordError(record);
        
        errorLog.warn("速率限制异常记录: userId={}, operation={}, limitType={}, retryAfter={}, currentCount={}, maxCount={}, message={}", 
                userId, operation, exception.getLimitType(), exception.getRetryAfter(), 
                exception.getCurrentCount(), exception.getMaxCount(), exception.getMessage());
    }

    /**
     * 记录系统异常
     * 
     * @param exception 系统异常
     * @param userId 用户ID
     * @param operation 操作类型
     * @param additionalInfo 附加信息
     */
    public void recordSystemException(Exception exception, Long userId, String operation, Map<String, Object> additionalInfo) {
        String errorType = "SYSTEM_ERROR";
        incrementErrorCounter(errorType);
        
        ErrorRecord record = new ErrorRecord(
            errorType,
            exception.getMessage(),
            userId,
            operation,
            500,
            additionalInfo
        );
        
        recordError(record);
        
        errorLog.error("系统异常记录: userId={}, operation={}, message={}, additionalInfo={}", 
                userId, operation, exception.getMessage(), additionalInfo, exception);
    }

    /**
     * 获取错误统计信息
     * 
     * @return 错误统计信息
     */
    public Map<String, Object> getErrorStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // 错误计数统计
        Map<String, Long> errorCounts = new HashMap<>();
        errorCounters.forEach((key, value) -> errorCounts.put(key, value.get()));
        stats.put("errorCounts", errorCounts);
        
        // 总错误数
        long totalErrors = errorCounters.values().stream()
                .mapToLong(AtomicLong::get)
                .sum();
        stats.put("totalErrors", totalErrors);
        
        // 最近错误数量
        stats.put("recentErrorsCount", recentErrors.size());
        
        // 系统健康状态评估
        String healthStatus = assessSystemHealth(totalErrors);
        stats.put("healthStatus", healthStatus);
        
        return stats;
    }

    /**
     * 获取最近的错误记录
     * 
     * @param limit 限制数量
     * @return 最近的错误记录
     */
    public Map<String, ErrorRecord> getRecentErrors(int limit) {
        if (limit <= 0 || limit > MAX_RECENT_ERRORS) {
            limit = MAX_RECENT_ERRORS;
        }
        
        return new HashMap<>(recentErrors);
    }

    /**
     * 清除错误统计
     */
    public void clearErrorStatistics() {
        errorCounters.clear();
        recentErrors.clear();
        log.info("错误统计已清除");
    }

    /**
     * 增加错误计数器
     * 
     * @param errorType 错误类型
     */
    private void incrementErrorCounter(String errorType) {
        errorCounters.computeIfAbsent(errorType, k -> new AtomicLong(0)).incrementAndGet();
    }

    /**
     * 记录错误详情
     * 
     * @param record 错误记录
     */
    private void recordError(ErrorRecord record) {
        String key = record.getTimestamp() + "_" + record.getErrorType() + "_" + record.getUserId();
        
        // 如果超过最大记录数，移除最旧的记录
        if (recentErrors.size() >= MAX_RECENT_ERRORS) {
            String oldestKey = recentErrors.keySet().iterator().next();
            recentErrors.remove(oldestKey);
        }
        
        recentErrors.put(key, record);
    }

    /**
     * 评估系统健康状态
     * 
     * @param totalErrors 总错误数
     * @return 健康状态
     */
    private String assessSystemHealth(long totalErrors) {
        if (totalErrors == 0) {
            return "EXCELLENT";
        } else if (totalErrors < 10) {
            return "GOOD";
        } else if (totalErrors < 50) {
            return "WARNING";
        } else {
            return "CRITICAL";
        }
    }

    /**
     * 错误记录内部类
     */
    public static class ErrorRecord {
        private final String errorType;
        private final String message;
        private final Long userId;
        private final String operation;
        private final Integer code;
        private final Map<String, Object> additionalInfo;
        private final String timestamp;

        public ErrorRecord(String errorType, String message, Long userId, String operation, Integer code, Map<String, Object> additionalInfo) {
            this.errorType = errorType;
            this.message = message;
            this.userId = userId;
            this.operation = operation;
            this.code = code;
            this.additionalInfo = additionalInfo != null ? additionalInfo : new HashMap<>();
            this.timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }

        // Getters
        public String getErrorType() { return errorType; }
        public String getMessage() { return message; }
        public Long getUserId() { return userId; }
        public String getOperation() { return operation; }
        public Integer getCode() { return code; }
        public Map<String, Object> getAdditionalInfo() { return additionalInfo; }
        public String getTimestamp() { return timestamp; }
    }
}
