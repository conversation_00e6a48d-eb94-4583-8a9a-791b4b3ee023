package com.teammanage.dto.request;

import jakarta.validation.constraints.NotBlank;

/**
 * 验证密码请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class ValidatePasswordRequest {

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;

    // 手动添加getter/setter方法
    public String getPassword() { 
        return password; 
    }
    
    public void setPassword(String password) { 
        this.password = password; 
    }
}
