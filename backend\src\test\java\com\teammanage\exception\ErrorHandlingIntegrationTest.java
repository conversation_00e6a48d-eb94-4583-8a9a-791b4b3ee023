package com.teammanage.exception;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 错误处理框架测试
 * 测试自定义异常类的功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class ErrorHandlingIntegrationTest {

    @Test
    void testRateLimitException_ApiCallLimit() {
        // Given
        Long retryAfter = 60L;
        Integer currentCount = 5;
        Integer maxCount = 10;

        // When
        RateLimitException exception = RateLimitException.apiCallLimit(retryAfter, currentCount, maxCount);

        // Then
        assertNotNull(exception);
        assertEquals(429, exception.getCode());
        assertEquals("API_CALL", exception.getLimitType());
        assertEquals(retryAfter, exception.getRetryAfter());
        assertEquals(currentCount, exception.getCurrentCount());
        assertEquals(maxCount, exception.getMaxCount());
        assertTrue(exception.getMessage().contains("API调用频率过高"));
        assertTrue(exception.getMessage().contains("60秒后重试"));
    }

    @Test
    void testRateLimitException_InvitationLimit() {
        // Given
        Long retryAfter = 1800L;

        // When
        RateLimitException exception = RateLimitException.invitationLimit(retryAfter);

        // Then
        assertNotNull(exception);
        assertEquals(429, exception.getCode());
        assertEquals("INVITATION", exception.getLimitType());
        assertEquals(retryAfter, exception.getRetryAfter());
        assertNull(exception.getCurrentCount());
        assertNull(exception.getMaxCount());
        assertTrue(exception.getMessage().contains("邀请发送过于频繁"));
        assertTrue(exception.getMessage().contains("1800秒后重试"));
    }

    @Test
    void testRateLimitException_TeamCreationLimit() {
        // Given
        Long retryAfter = 86400L;

        // When
        RateLimitException exception = RateLimitException.teamCreationLimit(retryAfter);

        // Then
        assertNotNull(exception);
        assertEquals(429, exception.getCode());
        assertEquals("TEAM_CREATION", exception.getLimitType());
        assertEquals(retryAfter, exception.getRetryAfter());
        assertNull(exception.getCurrentCount());
        assertNull(exception.getMaxCount());
        assertTrue(exception.getMessage().contains("团队创建过于频繁"));
        assertTrue(exception.getMessage().contains("86400秒后重试"));
    }

    @Test
    void testRateLimitException_LoginAttemptLimit() {
        // Given
        Long retryAfter = 3600L;
        Integer currentCount = 10;
        Integer maxCount = 10;

        // When
        RateLimitException exception = RateLimitException.loginAttemptLimit(retryAfter, currentCount, maxCount);

        // Then
        assertNotNull(exception);
        assertEquals(429, exception.getCode());
        assertEquals("LOGIN_ATTEMPT", exception.getLimitType());
        assertEquals(retryAfter, exception.getRetryAfter());
        assertEquals(currentCount, exception.getCurrentCount());
        assertEquals(maxCount, exception.getMaxCount());
        assertTrue(exception.getMessage().contains("登录尝试次数过多"));
        assertTrue(exception.getMessage().contains("账号已锁定3600秒"));
    }

    @Test
    void testDatabaseException_ConnectionFailed() {
        // Given
        Throwable cause = new RuntimeException("Connection timeout");

        // When
        DatabaseException exception = DatabaseException.connectionFailed(cause);

        // Then
        assertNotNull(exception);
        assertTrue(exception.getMessage().contains("数据库连接失败"));
        assertEquals("CONNECTION", exception.getOperation());
        assertEquals("Database connection failed", exception.getDetails());
        assertEquals(cause, exception.getCause());
    }

    @Test
    void testDatabaseException_TransactionRollback() {
        // Given
        String operation = "CREATE_TEAM";
        Throwable cause = new RuntimeException("Deadlock detected");

        // When
        DatabaseException exception = DatabaseException.transactionRollback(operation, cause);

        // Then
        assertNotNull(exception);
        assertTrue(exception.getMessage().contains("事务失败"));
        assertEquals(operation, exception.getOperation());
        assertEquals("Transaction rollback occurred", exception.getDetails());
        assertEquals(cause, exception.getCause());
    }

    @Test
    void testNetworkException_EmailServiceError() {
        // Given
        String operation = "SEND_EMAIL";
        Throwable cause = new RuntimeException("SMTP connection failed");

        // When
        NetworkException exception = NetworkException.emailServiceError(operation, cause);

        // Then
        assertNotNull(exception);
        assertTrue(exception.getMessage().contains("邮件发送失败"));
        assertEquals("EMAIL_SERVICE", exception.getService());
        assertEquals(operation, exception.getOperation());
        assertEquals(cause, exception.getCause());
    }

    @Test
    void testBusinessException_Basic() {
        // Given
        String message = "业务逻辑错误";

        // When
        BusinessException exception = new BusinessException(message);

        // Then
        assertNotNull(exception);
        assertEquals(message, exception.getMessage());
    }

    @Test
    void testExceptionChaining() {
        // Given
        RuntimeException rootCause = new RuntimeException("Root cause");
        DatabaseException dbException = DatabaseException.connectionFailed(rootCause);

        // When & Then
        assertEquals(rootCause, dbException.getCause());
        assertNotNull(dbException.getMessage());
        assertTrue(dbException.getMessage().contains("数据库连接失败"));
    }
}
