2025-08-02 22:14:21.179 [background-preinit] INFO  o.h.validator.internal.util.Version -- HV000001: Hibernate Validator 8.0.2.Final
2025-08-02 22:14:21.222 [main] INFO  c.teammanage.TeamManageApplication -- Starting TeamManageApplication using Java 24.0.1 with PID 18268 (F:\Project\teamAuth\backend\target\classes started by X in F:\Project\teamAuth)
2025-08-02 22:14:21.223 [main] INFO  c.teammanage.TeamManageApplication -- No active profile set, falling back to 1 default profile: "default"
2025-08-02 22:14:22.715 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer -- <PERSON><PERSON> initialized with port 8080 (http)
2025-08-02 22:14:22.732 [main] INFO  o.a.coyote.http11.Http11NioProtocol -- Initializing ProtocolHandler ["http-nio-8080"]
2025-08-02 22:14:22.735 [main] INFO  o.a.catalina.core.StandardService -- Starting service [Tomcat]
2025-08-02 22:14:22.735 [main] INFO  o.a.catalina.core.StandardEngine -- Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-02 22:14:22.799 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] -- Initializing Spring embedded WebApplicationContext
2025-08-02 22:14:22.800 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext -- Root WebApplicationContext: initialization completed in 1517 ms
2025-08-02 22:14:23.765 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration -- 

Using generated security password: 2c765c3d-3c2c-4699-bbac-e62cc5a3e6e9

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-02 22:14:23.976 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware -- Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5b1f29fa
2025-08-02 22:14:24.148 [main] INFO  o.a.coyote.http11.Http11NioProtocol -- Starting ProtocolHandler ["http-nio-8080"]
2025-08-02 22:14:24.170 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer -- Tomcat started on port 8080 (http) with context path '/api/v1'
2025-08-02 22:14:24.182 [main] INFO  c.teammanage.TeamManageApplication -- Started TeamManageApplication in 3.617 seconds (process running for 3.995)
2025-08-02 22:14:24.189 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource -- HikariPool-1 - Starting...
2025-08-02 22:14:24.270 [scheduling-1] INFO  com.zaxxer.hikari.pool.HikariPool -- HikariPool-1 - Added connection org.mariadb.jdbc.Connection@10d9dd96
2025-08-02 22:14:24.272 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource -- HikariPool-1 - Start completed.
2025-08-02 22:14:24.302 [scheduling-1] DEBUG c.t.m.T.updateExpiredInvitations -- ==>  Preparing: UPDATE team_invitation SET status = 'EXPIRED', updated_at = ? WHERE status = 'PENDING' AND expires_at <= ?
2025-08-02 22:14:24.327 [scheduling-1] DEBUG c.t.m.T.updateExpiredInvitations -- ==> Parameters: 2025-08-02T22:14:24.278094(LocalDateTime), 2025-08-02T22:14:24.278094(LocalDateTime)
2025-08-02 22:14:24.328 [scheduling-1] DEBUG c.t.m.T.updateExpiredInvitations -- <==    Updates: 12
2025-08-02 22:14:24.329 [scheduling-1] INFO  c.t.service.TeamInvitationService -- 更新过期邀请状态完成: count=12
2025-08-02 22:14:24.333 [scheduling-1] INFO  c.t.task.InvitationCleanupTask -- 定时任务：更新过期邀请状态完成，更新数量: 12
2025-08-02 22:14:53.410 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[localhost].[/api/v1] -- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-02 22:14:53.410 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet -- Initializing Servlet 'dispatcherServlet'
2025-08-02 22:14:53.411 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet -- Completed initialization in 1 ms
2025-08-02 22:14:53.542 [http-nio-8080-exec-1] WARN  c.t.security.JwtAuthenticationFilter -- 会话无效或已过期: 3107e84d-fb31-42e5-ad42-b82937b20f00
2025-08-02 22:14:53.547 [http-nio-8080-exec-1] WARN  c.t.s.JwtAuthenticationEntryPoint -- 未认证的请求: GET /api/v1/users/profile
2025-08-02 22:14:59.929 [http-nio-8080-exec-2] INFO  c.t.service.VerificationCodeService -- 发送验证码: email=<EMAIL>, code=858110, type=login
2025-08-02 22:15:19.717 [http-nio-8080-exec-3] INFO  c.t.service.VerificationCodeService -- 验证码验证成功: email=<EMAIL>, type=login
2025-08-02 22:15:19.719 [http-nio-8080-exec-3] DEBUG c.t.mapper.AccountMapper.findByEmail -- ==>  Preparing: SELECT * FROM account WHERE email = ?
2025-08-02 22:15:19.720 [http-nio-8080-exec-3] DEBUG c.t.mapper.AccountMapper.findByEmail -- ==> Parameters: <EMAIL>(String)
2025-08-02 22:15:19.736 [http-nio-8080-exec-3] DEBUG c.t.mapper.AccountMapper.findByEmail -- <==      Total: 1
2025-08-02 22:15:19.755 [http-nio-8080-exec-3] INFO  c.t.service.UserSessionService -- 创建用户会话: accountId=8, tokenHash=49c6cada-4b2f-450f-a223-21e7f95bb0a2
2025-08-02 22:15:19.756 [http-nio-8080-exec-3] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 22:15:19.758 [http-nio-8080-exec-3] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 22:15:19.762 [http-nio-8080-exec-3] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 22:15:19.763 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 22:15:19.763 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 22:15:19.765 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 22:15:19.765 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 22:15:19.766 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 22:15:19.767 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 22:15:19.768 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 22:15:19.768 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 22:15:19.769 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 22:15:19.769 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 22:15:19.770 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 22:15:19.770 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 22:15:19.772 [http-nio-8080-exec-3] INFO  com.teammanage.service.AuthService -- 用户登录成功: userId=8, email=<EMAIL>, teamCount=2
2025-08-02 22:15:20.212 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 22:15:20.215 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 22:15:20.215 [http-nio-8080-exec-4] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 22:15:20.216 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 22:15:20.218 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 22:15:20.218 [http-nio-8080-exec-4] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 22:15:20.222 [http-nio-8080-exec-4] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 22:15:20.222 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 22:15:20.226 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 22:15:20.226 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 22:15:20.226 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 22:15:20.226 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 22:15:20.227 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 22:15:20.227 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 22:15:20.227 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 22:15:20.228 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 22:15:20.228 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 22:15:20.228 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 22:15:20.228 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 22:15:20.228 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 22:15:20.229 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 22:15:20.228 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 22:15:20.229 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 22:15:20.229 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 22:15:20.229 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 22:15:20.229 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 22:15:20.230 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 22:15:20.230 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 22:15:20.230 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 22:15:20.230 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 22:15:20.230 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 22:15:20.230 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 22:15:20.230 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 22:15:20.231 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 22:15:20.231 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 22:15:20.231 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 22:15:20.232 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 22:15:20.232 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 22:15:20.232 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 22:15:20.232 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 22:15:20.233 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 22:15:20.233 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 22:15:20.233 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 22:15:20.233 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 22:15:20.234 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 22:15:20.234 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 22:15:20.236 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 22:15:20.238 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 22:15:20.277 [http-nio-8080-exec-7] DEBUG c.t.mapper.TodoMapper.selectList -- ==>  Preparing: SELECT id,title,description,status,priority,user_id,created_at,updated_at FROM todos WHERE (user_id = ?) ORDER BY created_at DESC
2025-08-02 22:15:20.277 [http-nio-8080-exec-8] DEBUG c.t.mapper.TodoMapper.selectList -- ==>  Preparing: SELECT id,title,description,status,priority,user_id,created_at,updated_at FROM todos WHERE (user_id = ?)
2025-08-02 22:15:20.278 [http-nio-8080-exec-7] DEBUG c.t.mapper.TodoMapper.selectList -- ==> Parameters: 8(Long)
2025-08-02 22:15:20.278 [http-nio-8080-exec-8] DEBUG c.t.mapper.TodoMapper.selectList -- ==> Parameters: 8(Long)
2025-08-02 22:15:20.280 [http-nio-8080-exec-8] DEBUG c.t.mapper.TodoMapper.selectList -- <==      Total: 6
2025-08-02 22:15:20.280 [http-nio-8080-exec-7] DEBUG c.t.mapper.TodoMapper.selectList -- <==      Total: 6
2025-08-02 22:17:14.286 [background-preinit] INFO  o.h.validator.internal.util.Version -- HV000001: Hibernate Validator 8.0.2.Final
2025-08-02 22:17:14.334 [main] INFO  c.teammanage.TeamManageApplication -- Starting TeamManageApplication using Java 24.0.1 with PID 19692 (F:\Project\teamAuth\backend\target\classes started by X in F:\Project\teamAuth)
2025-08-02 22:17:14.336 [main] INFO  c.teammanage.TeamManageApplication -- No active profile set, falling back to 1 default profile: "default"
2025-08-02 22:17:16.156 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer -- Tomcat initialized with port 8080 (http)
2025-08-02 22:17:16.173 [main] INFO  o.a.coyote.http11.Http11NioProtocol -- Initializing ProtocolHandler ["http-nio-8080"]
2025-08-02 22:17:16.176 [main] INFO  o.a.catalina.core.StandardService -- Starting service [Tomcat]
2025-08-02 22:17:16.176 [main] INFO  o.a.catalina.core.StandardEngine -- Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-02 22:17:16.244 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] -- Initializing Spring embedded WebApplicationContext
2025-08-02 22:17:16.245 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext -- Root WebApplicationContext: initialization completed in 1842 ms
2025-08-02 22:17:17.235 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration -- 

Using generated security password: 908fd5f1-c115-4421-9bec-b2c0c956e38b

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-02 22:17:17.428 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware -- Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5b1f29fa
2025-08-02 22:17:17.603 [main] INFO  o.a.coyote.http11.Http11NioProtocol -- Starting ProtocolHandler ["http-nio-8080"]
2025-08-02 22:17:17.624 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer -- Tomcat started on port 8080 (http) with context path '/api/v1'
2025-08-02 22:17:17.636 [main] INFO  c.teammanage.TeamManageApplication -- Started TeamManageApplication in 3.891 seconds (process running for 4.217)
2025-08-02 22:17:17.644 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource -- HikariPool-1 - Starting...
2025-08-02 22:17:17.718 [scheduling-1] INFO  com.zaxxer.hikari.pool.HikariPool -- HikariPool-1 - Added connection org.mariadb.jdbc.Connection@5873029d
2025-08-02 22:17:17.720 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource -- HikariPool-1 - Start completed.
2025-08-02 22:17:17.745 [scheduling-1] DEBUG c.t.m.T.updateExpiredInvitations -- ==>  Preparing: UPDATE team_invitation SET status = 'EXPIRED', updated_at = ? WHERE status = 'PENDING' AND expires_at <= ?
2025-08-02 22:17:17.764 [scheduling-1] DEBUG c.t.m.T.updateExpiredInvitations -- ==> Parameters: 2025-08-02T22:17:17.724671500(LocalDateTime), 2025-08-02T22:17:17.724671500(LocalDateTime)
2025-08-02 22:17:17.766 [scheduling-1] DEBUG c.t.m.T.updateExpiredInvitations -- <==    Updates: 0
2025-08-02 22:52:19.373 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[localhost].[/api/v1] -- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-02 22:52:19.373 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet -- Initializing Servlet 'dispatcherServlet'
2025-08-02 22:52:19.375 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet -- Completed initialization in 1 ms
2025-08-02 22:52:19.487 [http-nio-8080-exec-1] WARN  c.t.security.JwtAuthenticationFilter -- 会话无效或已过期: 49c6cada-4b2f-450f-a223-21e7f95bb0a2
2025-08-02 22:52:19.492 [http-nio-8080-exec-1] WARN  c.t.s.JwtAuthenticationEntryPoint -- 未认证的请求: GET /api/v1/users/profile
2025-08-02 23:05:40.366 [http-nio-8080-exec-2] INFO  c.t.service.VerificationCodeService -- 发送验证码: email=<EMAIL>, code=203406, type=login
2025-08-02 23:05:50.470 [http-nio-8080-exec-3] INFO  c.t.service.VerificationCodeService -- 验证码验证成功: email=<EMAIL>, type=login
2025-08-02 23:05:50.472 [http-nio-8080-exec-3] DEBUG c.t.mapper.AccountMapper.findByEmail -- ==>  Preparing: SELECT * FROM account WHERE email = ?
2025-08-02 23:05:50.472 [http-nio-8080-exec-3] DEBUG c.t.mapper.AccountMapper.findByEmail -- ==> Parameters: <EMAIL>(String)
2025-08-02 23:05:50.490 [http-nio-8080-exec-3] DEBUG c.t.mapper.AccountMapper.findByEmail -- <==      Total: 1
2025-08-02 23:05:50.508 [http-nio-8080-exec-3] INFO  c.t.service.UserSessionService -- 创建用户会话: accountId=8, tokenHash=c5cfaeb8-77ca-489f-9e0c-160c18104069
2025-08-02 23:05:50.509 [http-nio-8080-exec-3] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:05:50.511 [http-nio-8080-exec-3] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:05:50.515 [http-nio-8080-exec-3] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:05:50.516 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:05:50.517 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:05:50.518 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:05:50.519 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:05:50.520 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:05:50.521 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:05:50.521 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:05:50.521 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:05:50.522 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:05:50.522 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:05:50.522 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:05:50.523 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:05:50.524 [http-nio-8080-exec-3] INFO  com.teammanage.service.AuthService -- 用户登录成功: userId=8, email=<EMAIL>, teamCount=2
2025-08-02 23:05:50.862 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:05:50.862 [http-nio-8080-exec-5] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 23:05:50.863 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:05:50.862 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:05:50.863 [http-nio-8080-exec-5] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:05:50.863 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:05:50.864 [http-nio-8080-exec-5] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 23:05:50.864 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:05:50.864 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:05:50.865 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:05:50.865 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:05:50.865 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:05:50.865 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:05:50.866 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:05:50.866 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:05:50.867 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:05:50.867 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:05:50.867 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:05:50.867 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:05:50.867 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:05:50.867 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:05:50.867 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:05:50.867 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:05:50.868 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:05:50.869 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:05:50.869 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:05:50.869 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:05:50.869 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:05:50.869 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:05:50.870 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:05:50.870 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:05:50.870 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:05:50.870 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:05:50.871 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:05:50.872 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:05:50.872 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:05:50.872 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:05:50.872 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:05:50.872 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:05:50.872 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:05:50.872 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:05:50.873 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:05:50.873 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:05:50.873 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:05:50.873 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:05:50.874 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:05:50.874 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:05:50.875 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:05:50.919 [http-nio-8080-exec-8] DEBUG c.t.mapper.TodoMapper.selectList -- ==>  Preparing: SELECT id,title,description,status,priority,user_id,created_at,updated_at FROM todos WHERE (user_id = ?)
2025-08-02 23:05:50.919 [http-nio-8080-exec-7] DEBUG c.t.mapper.TodoMapper.selectList -- ==>  Preparing: SELECT id,title,description,status,priority,user_id,created_at,updated_at FROM todos WHERE (user_id = ?) ORDER BY created_at DESC
2025-08-02 23:05:50.920 [http-nio-8080-exec-8] DEBUG c.t.mapper.TodoMapper.selectList -- ==> Parameters: 8(Long)
2025-08-02 23:05:50.920 [http-nio-8080-exec-7] DEBUG c.t.mapper.TodoMapper.selectList -- ==> Parameters: 8(Long)
2025-08-02 23:05:50.922 [http-nio-8080-exec-8] DEBUG c.t.mapper.TodoMapper.selectList -- <==      Total: 6
2025-08-02 23:05:50.922 [http-nio-8080-exec-7] DEBUG c.t.mapper.TodoMapper.selectList -- <==      Total: 6
2025-08-02 23:17:17.636 [scheduling-1] DEBUG c.t.m.T.updateExpiredInvitations -- ==>  Preparing: UPDATE team_invitation SET status = 'EXPIRED', updated_at = ? WHERE status = 'PENDING' AND expires_at <= ?
2025-08-02 23:17:17.637 [scheduling-1] DEBUG c.t.m.T.updateExpiredInvitations -- ==> Parameters: 2025-08-02T23:17:17.635350500(LocalDateTime), 2025-08-02T23:17:17.635350500(LocalDateTime)
2025-08-02 23:17:17.638 [scheduling-1] DEBUG c.t.m.T.updateExpiredInvitations -- <==    Updates: 0
2025-08-02 23:17:50.438 [http-nio-8080-exec-9] WARN  c.t.security.JwtAuthenticationFilter -- 会话无效或已过期: 79e24505-af71-49ff-846d-de05845a0fc0
2025-08-02 23:17:50.439 [http-nio-8080-exec-9] WARN  c.t.s.JwtAuthenticationEntryPoint -- 未认证的请求: GET /api/v1/users/profile
2025-08-02 23:17:56.222 [http-nio-8080-exec-10] INFO  c.t.service.VerificationCodeService -- 发送验证码: email=<EMAIL>, code=522080, type=login
2025-08-02 23:18:04.468 [http-nio-8080-exec-1] WARN  c.t.exception.GlobalExceptionHandler -- 业务异常: 验证码错误
2025-08-02 23:18:07.210 [http-nio-8080-exec-2] WARN  c.t.exception.GlobalExceptionHandler -- 业务异常: 验证码错误
2025-08-02 23:18:08.839 [http-nio-8080-exec-3] WARN  c.t.exception.GlobalExceptionHandler -- 业务异常: 验证码错误
2025-08-02 23:18:09.001 [http-nio-8080-exec-4] WARN  c.t.exception.GlobalExceptionHandler -- 业务异常: 验证码尝试次数过多，请重新获取
2025-08-02 23:18:09.200 [http-nio-8080-exec-5] WARN  c.t.exception.GlobalExceptionHandler -- 业务异常: 验证码不存在或已过期
2025-08-02 23:18:27.114 [http-nio-8080-exec-6] INFO  c.t.service.VerificationCodeService -- 发送验证码: email=<EMAIL>, code=655400, type=login
2025-08-02 23:18:35.430 [http-nio-8080-exec-7] INFO  c.t.service.VerificationCodeService -- 验证码验证成功: email=<EMAIL>, type=login
2025-08-02 23:18:35.431 [http-nio-8080-exec-7] DEBUG c.t.mapper.AccountMapper.findByEmail -- ==>  Preparing: SELECT * FROM account WHERE email = ?
2025-08-02 23:18:35.431 [http-nio-8080-exec-7] DEBUG c.t.mapper.AccountMapper.findByEmail -- ==> Parameters: <EMAIL>(String)
2025-08-02 23:18:35.432 [http-nio-8080-exec-7] DEBUG c.t.mapper.AccountMapper.findByEmail -- <==      Total: 1
2025-08-02 23:18:35.434 [http-nio-8080-exec-7] INFO  c.t.service.UserSessionService -- 创建用户会话: accountId=8, tokenHash=a35b716f-1a72-45cb-8901-80895b85cd31
2025-08-02 23:18:35.435 [http-nio-8080-exec-7] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:18:35.435 [http-nio-8080-exec-7] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:18:35.436 [http-nio-8080-exec-7] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:18:35.436 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:18:35.437 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:18:35.437 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:18:35.438 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:18:35.438 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:18:35.438 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:18:35.439 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:18:35.439 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:18:35.440 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:18:35.440 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:18:35.440 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:18:35.440 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:18:35.441 [http-nio-8080-exec-7] INFO  com.teammanage.service.AuthService -- 用户登录成功: userId=8, email=<EMAIL>, teamCount=2
2025-08-02 23:18:35.654 [http-nio-8080-exec-8] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 23:18:35.655 [http-nio-8080-exec-1] DEBUG c.t.mapper.TodoMapper.selectList -- ==>  Preparing: SELECT id,title,description,status,priority,user_id,created_at,updated_at FROM todos WHERE (user_id = ?) ORDER BY created_at DESC
2025-08-02 23:18:35.655 [http-nio-8080-exec-2] DEBUG c.t.mapper.TodoMapper.selectList -- ==>  Preparing: SELECT id,title,description,status,priority,user_id,created_at,updated_at FROM todos WHERE (user_id = ?)
2025-08-02 23:18:35.655 [http-nio-8080-exec-8] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:18:35.655 [http-nio-8080-exec-10] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:18:35.655 [http-nio-8080-exec-9] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:18:35.656 [http-nio-8080-exec-10] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:18:35.656 [http-nio-8080-exec-9] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:18:35.656 [http-nio-8080-exec-1] DEBUG c.t.mapper.TodoMapper.selectList -- ==> Parameters: 8(Long)
2025-08-02 23:18:35.656 [http-nio-8080-exec-2] DEBUG c.t.mapper.TodoMapper.selectList -- ==> Parameters: 8(Long)
2025-08-02 23:18:35.657 [http-nio-8080-exec-8] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 23:18:35.657 [http-nio-8080-exec-10] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:18:35.657 [http-nio-8080-exec-9] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:18:35.658 [http-nio-8080-exec-8] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:18:35.658 [http-nio-8080-exec-9] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:18:35.658 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:18:35.658 [http-nio-8080-exec-8] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:18:35.658 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:18:35.658 [http-nio-8080-exec-1] DEBUG c.t.mapper.TodoMapper.selectList -- <==      Total: 6
2025-08-02 23:18:35.658 [http-nio-8080-exec-9] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:18:35.659 [http-nio-8080-exec-2] DEBUG c.t.mapper.TodoMapper.selectList -- <==      Total: 6
2025-08-02 23:18:35.659 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:18:35.659 [http-nio-8080-exec-9] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:18:35.659 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:18:35.659 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:18:35.660 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:18:35.660 [http-nio-8080-exec-8] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:18:35.660 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:18:35.660 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:18:35.660 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:18:35.660 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:18:35.660 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:18:35.661 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:18:35.661 [http-nio-8080-exec-9] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:18:35.661 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:18:35.661 [http-nio-8080-exec-9] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:18:35.661 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:18:35.662 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:18:35.662 [http-nio-8080-exec-9] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:18:35.662 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:18:35.662 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:18:35.662 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:18:35.662 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:18:35.663 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:18:35.663 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:18:35.664 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:18:35.664 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:18:35.664 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:18:35.665 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:18:35.675 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:18:35.676 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:18:35.677 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:18:35.677 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:18:35.678 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:24:05.426 [http-nio-8080-exec-3] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:24:05.427 [http-nio-8080-exec-3] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:24:05.428 [http-nio-8080-exec-3] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:24:05.428 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:24:05.428 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:24:05.430 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:24:05.430 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:24:05.430 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:24:05.431 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:24:05.431 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:24:05.432 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:24:05.432 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:24:05.433 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:24:05.433 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:24:05.433 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:24:24.905 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:24:24.905 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:24:24.906 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:24:24.907 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:24:24.907 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:24:24.907 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:24:24.908 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:24:24.908 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:24:24.908 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:24:24.909 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:24:24.909 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:24:24.909 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:24:24.910 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:24:24.910 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:24:24.910 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:24:51.356 [http-nio-8080-exec-5] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 23:24:51.356 [http-nio-8080-exec-5] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:24:51.357 [http-nio-8080-exec-5] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 23:24:51.358 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:24:51.358 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:24:51.359 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:24:51.359 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:24:51.359 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:24:51.360 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:24:51.360 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:24:51.360 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:24:51.361 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:24:51.361 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:24:51.362 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:24:51.362 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:24:51.362 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:24:51.362 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:24:51.363 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:25:25.676 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:25:25.677 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:25:25.678 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:25:25.679 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:25:25.679 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:25:25.681 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:25:25.681 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:25:25.682 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:25:25.683 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:25:25.683 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:25:25.684 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:25:25.685 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:25:25.685 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:25:25.686 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:25:25.687 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:25:36.258 [http-nio-8080-exec-7] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:25:36.258 [http-nio-8080-exec-7] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:25:36.260 [http-nio-8080-exec-7] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:25:36.260 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:25:36.261 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:25:36.261 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:25:36.262 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:25:36.262 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:25:36.262 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:25:36.263 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:25:36.263 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:25:36.263 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:25:36.264 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:25:36.264 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:25:36.265 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:26:02.929 [http-nio-8080-exec-1] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:26:02.930 [http-nio-8080-exec-1] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:26:02.931 [http-nio-8080-exec-1] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:26:02.932 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:26:02.932 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:26:02.933 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:26:02.933 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:26:02.933 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:26:02.934 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:26:02.934 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:26:02.934 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:26:02.935 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:26:02.935 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:26:02.936 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:26:02.936 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:26:19.853 [http-nio-8080-exec-2] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 23:26:19.853 [http-nio-8080-exec-2] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:26:19.854 [http-nio-8080-exec-2] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 23:26:20.278 [http-nio-8080-exec-10] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:26:20.278 [http-nio-8080-exec-9] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 23:26:20.278 [http-nio-8080-exec-8] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:26:20.279 [http-nio-8080-exec-9] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:26:20.279 [http-nio-8080-exec-8] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:26:20.278 [http-nio-8080-exec-4] DEBUG c.t.mapper.TodoMapper.selectList -- ==>  Preparing: SELECT id,title,description,status,priority,user_id,created_at,updated_at FROM todos WHERE (user_id = ?)
2025-08-02 23:26:20.279 [http-nio-8080-exec-3] DEBUG c.t.mapper.TodoMapper.selectList -- ==>  Preparing: SELECT id,title,description,status,priority,user_id,created_at,updated_at FROM todos WHERE (user_id = ?) ORDER BY created_at DESC
2025-08-02 23:26:20.279 [http-nio-8080-exec-10] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:26:20.279 [http-nio-8080-exec-4] DEBUG c.t.mapper.TodoMapper.selectList -- ==> Parameters: 8(Long)
2025-08-02 23:26:20.279 [http-nio-8080-exec-3] DEBUG c.t.mapper.TodoMapper.selectList -- ==> Parameters: 8(Long)
2025-08-02 23:26:20.280 [http-nio-8080-exec-9] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 23:26:20.280 [http-nio-8080-exec-8] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:26:20.280 [http-nio-8080-exec-10] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:26:20.280 [http-nio-8080-exec-9] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:26:20.280 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:26:20.280 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:26:20.280 [http-nio-8080-exec-9] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:26:20.281 [http-nio-8080-exec-4] DEBUG c.t.mapper.TodoMapper.selectList -- <==      Total: 6
2025-08-02 23:26:20.281 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:26:20.281 [http-nio-8080-exec-3] DEBUG c.t.mapper.TodoMapper.selectList -- <==      Total: 6
2025-08-02 23:26:20.281 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:26:20.281 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:26:20.281 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:26:20.282 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:26:20.282 [http-nio-8080-exec-9] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:26:20.282 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:26:20.282 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:26:20.282 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:26:20.282 [http-nio-8080-exec-9] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:26:20.283 [http-nio-8080-exec-9] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:26:20.283 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:26:20.283 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:26:20.283 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:26:20.283 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:26:20.283 [http-nio-8080-exec-9] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:26:20.284 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:26:20.284 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:26:20.284 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:26:20.284 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:26:20.284 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:26:20.284 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:26:20.285 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:26:20.285 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:26:20.285 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:26:20.285 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:26:20.285 [http-nio-8080-exec-9] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:26:20.285 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:26:20.286 [http-nio-8080-exec-9] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:26:20.286 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:26:20.286 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:26:20.286 [http-nio-8080-exec-9] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:26:20.287 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:26:20.287 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:26:20.288 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:26:40.186 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:26:40.186 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:26:40.188 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:26:40.188 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:26:40.188 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:26:40.189 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:26:40.189 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:26:40.189 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:26:40.190 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:26:40.193 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:26:40.200 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:26:40.202 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:26:40.204 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:26:40.205 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:26:40.206 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:26:48.912 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByTeamIdAndAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE team_id = ? AND account_id = ? AND is_deleted = 0
2025-08-02 23:26:48.913 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByTeamIdAndAccountId -- ==> Parameters: 8(Long), 8(Long)
2025-08-02 23:26:48.913 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByTeamIdAndAccountId -- <==      Total: 1
2025-08-02 23:26:48.914 [http-nio-8080-exec-6] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 23:26:48.914 [http-nio-8080-exec-6] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:26:48.914 [http-nio-8080-exec-6] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 23:26:48.916 [http-nio-8080-exec-6] INFO  c.t.service.UserSessionService -- 会话已失效: accountId=8, jti=a35b716f-1a72-45cb-8901-80895b85cd31
2025-08-02 23:26:48.917 [http-nio-8080-exec-6] INFO  c.t.service.UserSessionService -- 创建用户会话: accountId=8, tokenHash=83aa481e-3e67-42a2-b0e2-cc61960d75e4
2025-08-02 23:26:48.920 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.updateById -- ==>  Preparing: UPDATE team_member SET team_id=?, account_id=?, is_creator=?, role=?, assigned_at=?, last_access_time=?, is_active=?, created_at=?, updated_at=? WHERE id=? AND is_deleted=0
2025-08-02 23:26:48.922 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.updateById -- ==> Parameters: 8(Long), 8(Long), true(Boolean), TEAM_CREATOR(String), 2025-07-23T12:07:51(LocalDateTime), 2025-08-02T23:26:48.917681(LocalDateTime), true(Boolean), 2025-07-23T12:07:51(LocalDateTime), 2025-07-29T22:22:34(LocalDateTime), 7(Long)
2025-08-02 23:26:48.924 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.updateById -- <==    Updates: 1
2025-08-02 23:26:48.925 [http-nio-8080-exec-6] INFO  com.teammanage.service.AuthService -- 团队选择成功: userId=8, teamId=8, isCreator=true
2025-08-02 23:26:48.953 [http-nio-8080-exec-7] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 23:26:48.953 [http-nio-8080-exec-1] DEBUG c.t.m.T.findByTeamIdAndAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE team_id = ? AND account_id = ? AND is_deleted = 0
2025-08-02 23:26:48.959 [http-nio-8080-exec-1] DEBUG c.t.m.T.findByTeamIdAndAccountId -- ==> Parameters: 8(Long), 8(Long)
2025-08-02 23:26:48.955 [http-nio-8080-exec-7] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:26:48.961 [http-nio-8080-exec-1] DEBUG c.t.m.T.findByTeamIdAndAccountId -- <==      Total: 1
2025-08-02 23:26:48.961 [http-nio-8080-exec-7] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 23:26:48.964 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:26:48.966 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:26:48.970 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:26:48.971 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:26:48.971 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:26:48.973 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:26:52.455 [http-nio-8080-exec-10] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:26:52.455 [http-nio-8080-exec-8] DEBUG c.t.mapper.TodoMapper.selectList -- ==>  Preparing: SELECT id,title,description,status,priority,user_id,created_at,updated_at FROM todos WHERE (user_id = ?) ORDER BY created_at DESC
2025-08-02 23:26:52.455 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:26:52.455 [http-nio-8080-exec-2] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 23:26:52.455 [http-nio-8080-exec-3] DEBUG c.t.mapper.TodoMapper.selectList -- ==>  Preparing: SELECT id,title,description,status,priority,user_id,created_at,updated_at FROM todos WHERE (user_id = ?)
2025-08-02 23:26:52.455 [http-nio-8080-exec-10] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:26:52.455 [http-nio-8080-exec-3] DEBUG c.t.mapper.TodoMapper.selectList -- ==> Parameters: 8(Long)
2025-08-02 23:26:52.455 [http-nio-8080-exec-8] DEBUG c.t.mapper.TodoMapper.selectList -- ==> Parameters: 8(Long)
2025-08-02 23:26:52.455 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:26:52.455 [http-nio-8080-exec-2] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:26:52.456 [http-nio-8080-exec-2] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 23:26:52.456 [http-nio-8080-exec-10] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:26:52.456 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:26:52.457 [http-nio-8080-exec-3] DEBUG c.t.mapper.TodoMapper.selectList -- <==      Total: 6
2025-08-02 23:26:52.457 [http-nio-8080-exec-2] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:26:52.457 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:26:52.457 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:26:52.457 [http-nio-8080-exec-2] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:26:52.457 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:26:52.457 [http-nio-8080-exec-8] DEBUG c.t.mapper.TodoMapper.selectList -- <==      Total: 6
2025-08-02 23:26:52.457 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:26:52.458 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:26:52.458 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:26:52.458 [http-nio-8080-exec-2] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:26:52.458 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:26:52.458 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:26:52.458 [http-nio-8080-exec-2] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:26:52.458 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:26:52.459 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:26:52.459 [http-nio-8080-exec-2] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:26:52.460 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:26:52.460 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:26:52.460 [http-nio-8080-exec-2] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:26:52.460 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:26:52.460 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:26:52.460 [http-nio-8080-exec-2] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:26:52.461 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:26:52.461 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:26:52.461 [http-nio-8080-exec-2] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:26:52.461 [http-nio-8080-exec-2] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:26:52.462 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:26:52.462 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:26:52.462 [http-nio-8080-exec-2] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:26:52.462 [http-nio-8080-exec-2] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:26:52.462 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:26:52.462 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:26:52.463 [http-nio-8080-exec-2] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:26:52.463 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:26:52.463 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:26:52.463 [http-nio-8080-exec-2] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:26:52.464 [http-nio-8080-exec-2] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:26:52.464 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:26:52.464 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:26:52.464 [http-nio-8080-exec-2] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:27:11.427 [http-nio-8080-exec-9] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 23:27:11.428 [http-nio-8080-exec-9] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:27:11.429 [http-nio-8080-exec-9] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 23:27:11.479 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByTeamIdAndAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE team_id = ? AND account_id = ? AND is_deleted = 0
2025-08-02 23:27:11.479 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByTeamIdAndAccountId -- ==> Parameters: 8(Long), 8(Long)
2025-08-02 23:27:11.480 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByTeamIdAndAccountId -- <==      Total: 1
2025-08-02 23:27:11.481 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:27:11.482 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:27:11.484 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:27:11.484 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:27:11.485 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:27:11.485 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:27:11.879 [http-nio-8080-exec-7] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:27:11.879 [http-nio-8080-exec-3] DEBUG c.t.mapper.TodoMapper.selectList -- ==>  Preparing: SELECT id,title,description,status,priority,user_id,created_at,updated_at FROM todos WHERE (user_id = ?) ORDER BY created_at DESC
2025-08-02 23:27:11.880 [http-nio-8080-exec-7] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:27:11.880 [http-nio-8080-exec-3] DEBUG c.t.mapper.TodoMapper.selectList -- ==> Parameters: 8(Long)
2025-08-02 23:27:11.881 [http-nio-8080-exec-1] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:27:11.881 [http-nio-8080-exec-8] DEBUG c.t.mapper.TodoMapper.selectList -- ==>  Preparing: SELECT id,title,description,status,priority,user_id,created_at,updated_at FROM todos WHERE (user_id = ?)
2025-08-02 23:27:11.881 [http-nio-8080-exec-1] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:27:11.881 [http-nio-8080-exec-8] DEBUG c.t.mapper.TodoMapper.selectList -- ==> Parameters: 8(Long)
2025-08-02 23:27:11.882 [http-nio-8080-exec-6] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 23:27:11.882 [http-nio-8080-exec-6] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:27:11.882 [http-nio-8080-exec-7] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:27:11.882 [http-nio-8080-exec-1] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:27:11.882 [http-nio-8080-exec-6] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 23:27:11.882 [http-nio-8080-exec-3] DEBUG c.t.mapper.TodoMapper.selectList -- <==      Total: 6
2025-08-02 23:27:11.882 [http-nio-8080-exec-8] DEBUG c.t.mapper.TodoMapper.selectList -- <==      Total: 6
2025-08-02 23:27:11.883 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:27:11.883 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:27:11.883 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:27:11.883 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:27:11.883 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:27:11.883 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:27:11.884 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:27:11.884 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:27:11.884 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:27:11.885 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:27:11.885 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:27:11.885 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:27:11.885 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:27:11.886 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:27:11.886 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:27:11.887 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:27:11.887 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:27:11.888 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:27:11.888 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:27:11.888 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:27:11.888 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:27:11.888 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:27:11.888 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:27:11.888 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:27:11.889 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:27:11.889 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:27:11.889 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:27:11.889 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:27:11.889 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:27:11.889 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:27:11.889 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:27:11.890 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:27:11.890 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:27:11.890 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:27:11.890 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:27:11.890 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:27:11.890 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:27:11.891 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:27:11.891 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:27:12.090 [http-nio-8080-exec-2] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 23:27:12.090 [http-nio-8080-exec-2] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:27:12.091 [http-nio-8080-exec-2] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 23:27:12.091 [http-nio-8080-exec-2] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:27:12.092 [http-nio-8080-exec-2] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:27:12.092 [http-nio-8080-exec-2] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:27:12.093 [http-nio-8080-exec-2] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:27:12.093 [http-nio-8080-exec-2] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:27:12.093 [http-nio-8080-exec-2] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:27:12.094 [http-nio-8080-exec-2] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:27:12.094 [http-nio-8080-exec-2] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:27:12.094 [http-nio-8080-exec-2] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:27:12.095 [http-nio-8080-exec-2] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:27:12.095 [http-nio-8080-exec-2] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:27:12.096 [http-nio-8080-exec-2] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:27:12.097 [http-nio-8080-exec-2] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:27:12.098 [http-nio-8080-exec-2] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:27:12.098 [http-nio-8080-exec-2] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:27:23.426 [http-nio-8080-exec-4] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 23:27:23.427 [http-nio-8080-exec-4] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:27:23.428 [http-nio-8080-exec-4] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 23:27:23.455 [http-nio-8080-exec-10] DEBUG c.t.m.T.findByTeamIdAndAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE team_id = ? AND account_id = ? AND is_deleted = 0
2025-08-02 23:27:23.455 [http-nio-8080-exec-10] DEBUG c.t.m.T.findByTeamIdAndAccountId -- ==> Parameters: 8(Long), 8(Long)
2025-08-02 23:27:23.457 [http-nio-8080-exec-10] DEBUG c.t.m.T.findByTeamIdAndAccountId -- <==      Total: 1
2025-08-02 23:27:23.459 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:27:23.459 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:27:23.460 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:27:23.461 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:27:23.461 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:27:23.461 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:27:23.843 [http-nio-8080-exec-3] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:27:23.843 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:27:23.843 [http-nio-8080-exec-7] DEBUG c.t.mapper.TodoMapper.selectList -- ==>  Preparing: SELECT id,title,description,status,priority,user_id,created_at,updated_at FROM todos WHERE (user_id = ?)
2025-08-02 23:27:23.843 [http-nio-8080-exec-9] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 23:27:23.843 [http-nio-8080-exec-8] DEBUG c.t.mapper.TodoMapper.selectList -- ==>  Preparing: SELECT id,title,description,status,priority,user_id,created_at,updated_at FROM todos WHERE (user_id = ?) ORDER BY created_at DESC
2025-08-02 23:27:23.843 [http-nio-8080-exec-3] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:27:23.843 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:27:23.843 [http-nio-8080-exec-7] DEBUG c.t.mapper.TodoMapper.selectList -- ==> Parameters: 8(Long)
2025-08-02 23:27:23.843 [http-nio-8080-exec-9] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:27:23.844 [http-nio-8080-exec-8] DEBUG c.t.mapper.TodoMapper.selectList -- ==> Parameters: 8(Long)
2025-08-02 23:27:23.844 [http-nio-8080-exec-9] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 23:27:23.845 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:27:23.845 [http-nio-8080-exec-3] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:27:23.845 [http-nio-8080-exec-7] DEBUG c.t.mapper.TodoMapper.selectList -- <==      Total: 6
2025-08-02 23:27:23.845 [http-nio-8080-exec-9] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:27:23.845 [http-nio-8080-exec-8] DEBUG c.t.mapper.TodoMapper.selectList -- <==      Total: 6
2025-08-02 23:27:23.845 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:27:23.845 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:27:23.845 [http-nio-8080-exec-9] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:27:23.845 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:27:23.845 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:27:23.846 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:27:23.847 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:27:23.847 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:27:23.847 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:27:23.847 [http-nio-8080-exec-9] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:27:23.848 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:27:23.848 [http-nio-8080-exec-9] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:27:23.848 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:27:23.848 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:27:23.848 [http-nio-8080-exec-9] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:27:23.849 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:27:23.849 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:27:23.849 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:27:23.850 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:27:23.850 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:27:23.850 [http-nio-8080-exec-9] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:27:23.850 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:27:23.851 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:27:23.851 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:27:23.851 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:27:23.851 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:27:23.851 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:27:23.851 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:27:23.852 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:27:23.852 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:27:23.852 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:27:23.852 [http-nio-8080-exec-9] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:27:23.852 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:27:23.852 [http-nio-8080-exec-9] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:27:23.853 [http-nio-8080-exec-9] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:27:23.854 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:27:23.854 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:27:23.854 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:27:31.543 [http-nio-8080-exec-1] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 23:27:31.543 [http-nio-8080-exec-1] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:27:31.544 [http-nio-8080-exec-1] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 23:27:31.545 [http-nio-8080-exec-1] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:27:31.545 [http-nio-8080-exec-1] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:27:31.546 [http-nio-8080-exec-1] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:27:31.547 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:27:31.547 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:27:31.548 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:27:31.549 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:27:31.550 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:27:31.551 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:27:31.551 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:27:31.552 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:27:31.552 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:27:31.553 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:27:31.553 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:27:31.554 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:28:14.510 [http-nio-8080-exec-6] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 23:28:14.510 [http-nio-8080-exec-6] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:28:14.511 [http-nio-8080-exec-6] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 23:28:14.512 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:28:14.512 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:28:14.513 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:28:14.513 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:28:14.514 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:28:14.515 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:28:14.515 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:28:14.516 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:28:14.520 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:28:14.520 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:28:14.520 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:28:14.521 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:28:14.522 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:28:14.522 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:28:14.522 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:28:24.674 [http-nio-8080-exec-2] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 23:28:24.674 [http-nio-8080-exec-2] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:28:24.675 [http-nio-8080-exec-2] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 23:28:24.675 [http-nio-8080-exec-2] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:28:24.676 [http-nio-8080-exec-2] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:28:24.676 [http-nio-8080-exec-2] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:28:24.677 [http-nio-8080-exec-2] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:28:24.679 [http-nio-8080-exec-2] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:28:24.681 [http-nio-8080-exec-2] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:28:24.682 [http-nio-8080-exec-2] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:28:24.683 [http-nio-8080-exec-2] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:28:24.683 [http-nio-8080-exec-2] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:28:24.684 [http-nio-8080-exec-2] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:28:24.684 [http-nio-8080-exec-2] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:28:24.685 [http-nio-8080-exec-2] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:28:24.686 [http-nio-8080-exec-2] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:28:24.687 [http-nio-8080-exec-2] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:28:24.688 [http-nio-8080-exec-2] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:28:33.245 [http-nio-8080-exec-4] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 23:28:33.246 [http-nio-8080-exec-4] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:28:33.246 [http-nio-8080-exec-4] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 23:28:33.247 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:28:33.247 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:28:33.248 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:28:33.248 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:28:33.248 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:28:33.249 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:28:33.250 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:28:33.250 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:28:33.250 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:28:33.251 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:28:33.251 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:28:33.251 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:28:33.252 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:28:33.252 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:28:33.252 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:28:53.822 [http-nio-8080-exec-10] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 23:28:53.823 [http-nio-8080-exec-10] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:28:53.823 [http-nio-8080-exec-10] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 23:28:53.838 [http-nio-8080-exec-7] DEBUG c.t.m.T.findByTeamIdAndAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE team_id = ? AND account_id = ? AND is_deleted = 0
2025-08-02 23:28:53.839 [http-nio-8080-exec-7] DEBUG c.t.m.T.findByTeamIdAndAccountId -- ==> Parameters: 8(Long), 8(Long)
2025-08-02 23:28:53.839 [http-nio-8080-exec-7] DEBUG c.t.m.T.findByTeamIdAndAccountId -- <==      Total: 1
2025-08-02 23:28:53.840 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:28:53.840 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:28:53.840 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:28:53.841 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:28:53.841 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:28:53.841 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:28:54.221 [http-nio-8080-exec-8] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 23:28:54.221 [http-nio-8080-exec-3] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:28:54.221 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:28:54.221 [http-nio-8080-exec-1] DEBUG c.t.mapper.TodoMapper.selectList -- ==>  Preparing: SELECT id,title,description,status,priority,user_id,created_at,updated_at FROM todos WHERE (user_id = ?)
2025-08-02 23:28:54.221 [http-nio-8080-exec-3] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:28:54.221 [http-nio-8080-exec-8] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:28:54.221 [http-nio-8080-exec-9] DEBUG c.t.mapper.TodoMapper.selectList -- ==>  Preparing: SELECT id,title,description,status,priority,user_id,created_at,updated_at FROM todos WHERE (user_id = ?) ORDER BY created_at DESC
2025-08-02 23:28:54.221 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:28:54.221 [http-nio-8080-exec-1] DEBUG c.t.mapper.TodoMapper.selectList -- ==> Parameters: 8(Long)
2025-08-02 23:28:54.222 [http-nio-8080-exec-9] DEBUG c.t.mapper.TodoMapper.selectList -- ==> Parameters: 8(Long)
2025-08-02 23:28:54.222 [http-nio-8080-exec-8] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 23:28:54.222 [http-nio-8080-exec-3] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:28:54.222 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:28:54.223 [http-nio-8080-exec-1] DEBUG c.t.mapper.TodoMapper.selectList -- <==      Total: 6
2025-08-02 23:28:54.223 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:28:54.223 [http-nio-8080-exec-8] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:28:54.223 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:28:54.223 [http-nio-8080-exec-9] DEBUG c.t.mapper.TodoMapper.selectList -- <==      Total: 6
2025-08-02 23:28:54.223 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:28:54.223 [http-nio-8080-exec-8] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:28:54.223 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:28:54.224 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:28:54.224 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:28:54.224 [http-nio-8080-exec-8] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:28:54.224 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:28:54.224 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:28:54.224 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:28:54.225 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:28:54.225 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:28:54.225 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:28:54.225 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:28:54.225 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:28:54.225 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:28:54.226 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:28:54.226 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:28:54.226 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:28:54.226 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:28:54.226 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:28:54.226 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:28:54.227 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:28:54.227 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:28:54.227 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:28:54.227 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:28:54.227 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:28:54.227 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:28:54.227 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:28:54.228 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:28:54.228 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:28:54.228 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:28:54.228 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:28:54.228 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:28:54.229 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:28:54.229 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:28:54.230 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:29:16.490 [http-nio-8080-exec-6] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 23:29:16.490 [http-nio-8080-exec-6] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:29:16.491 [http-nio-8080-exec-6] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 23:29:16.514 [http-nio-8080-exec-2] DEBUG c.t.m.T.findByTeamIdAndAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE team_id = ? AND account_id = ? AND is_deleted = 0
2025-08-02 23:29:16.515 [http-nio-8080-exec-2] DEBUG c.t.m.T.findByTeamIdAndAccountId -- ==> Parameters: 8(Long), 8(Long)
2025-08-02 23:29:16.517 [http-nio-8080-exec-2] DEBUG c.t.m.T.findByTeamIdAndAccountId -- <==      Total: 1
2025-08-02 23:29:16.518 [http-nio-8080-exec-2] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:29:16.518 [http-nio-8080-exec-2] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:29:16.520 [http-nio-8080-exec-2] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:29:16.523 [http-nio-8080-exec-2] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:29:16.524 [http-nio-8080-exec-2] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:29:16.525 [http-nio-8080-exec-2] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:29:16.890 [http-nio-8080-exec-4] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 23:29:16.891 [http-nio-8080-exec-4] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:29:16.892 [http-nio-8080-exec-4] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 23:29:16.893 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:29:16.893 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:29:16.894 [http-nio-8080-exec-10] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:29:16.894 [http-nio-8080-exec-7] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:29:16.894 [http-nio-8080-exec-10] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:29:16.894 [http-nio-8080-exec-7] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:29:16.894 [http-nio-8080-exec-1] DEBUG c.t.mapper.TodoMapper.selectList -- ==>  Preparing: SELECT id,title,description,status,priority,user_id,created_at,updated_at FROM todos WHERE (user_id = ?) ORDER BY created_at DESC
2025-08-02 23:29:16.894 [http-nio-8080-exec-9] DEBUG c.t.mapper.TodoMapper.selectList -- ==>  Preparing: SELECT id,title,description,status,priority,user_id,created_at,updated_at FROM todos WHERE (user_id = ?)
2025-08-02 23:29:16.894 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:29:16.895 [http-nio-8080-exec-1] DEBUG c.t.mapper.TodoMapper.selectList -- ==> Parameters: 8(Long)
2025-08-02 23:29:16.895 [http-nio-8080-exec-9] DEBUG c.t.mapper.TodoMapper.selectList -- ==> Parameters: 8(Long)
2025-08-02 23:29:16.895 [http-nio-8080-exec-7] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:29:16.895 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:29:16.895 [http-nio-8080-exec-10] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:29:16.895 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:29:16.895 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:29:16.896 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:29:16.896 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:29:16.896 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:29:16.896 [http-nio-8080-exec-9] DEBUG c.t.mapper.TodoMapper.selectList -- <==      Total: 6
2025-08-02 23:29:16.896 [http-nio-8080-exec-1] DEBUG c.t.mapper.TodoMapper.selectList -- <==      Total: 6
2025-08-02 23:29:16.896 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:29:16.896 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:29:16.896 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:29:16.897 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:29:16.897 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:29:16.897 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:29:16.897 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:29:16.897 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:29:16.897 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:29:16.898 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:29:16.898 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:29:16.898 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:29:16.898 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:29:16.898 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:29:16.898 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:29:16.898 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:29:16.898 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:29:16.898 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:29:16.899 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:29:16.899 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:29:16.899 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:29:16.900 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:29:16.900 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:29:16.900 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:29:16.900 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:29:16.900 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:29:16.900 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:29:16.901 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:29:16.901 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:29:16.901 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:38:32.858 [http-nio-8080-exec-5] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 23:38:32.858 [http-nio-8080-exec-5] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:38:32.859 [http-nio-8080-exec-5] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 23:52:16.540 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:52:16.540 [http-nio-8080-exec-8] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:52:16.540 [http-nio-8080-exec-1] DEBUG c.t.mapper.TodoMapper.selectList -- ==>  Preparing: SELECT id,title,description,status,priority,user_id,created_at,updated_at FROM todos WHERE (user_id = ?)
2025-08-02 23:52:16.540 [http-nio-8080-exec-2] DEBUG c.t.mapper.TodoMapper.selectList -- ==>  Preparing: SELECT id,title,description,status,priority,user_id,created_at,updated_at FROM todos WHERE (user_id = ?) ORDER BY created_at DESC
2025-08-02 23:52:16.540 [http-nio-8080-exec-3] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 23:52:16.540 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:52:16.540 [http-nio-8080-exec-8] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:52:16.540 [http-nio-8080-exec-1] DEBUG c.t.mapper.TodoMapper.selectList -- ==> Parameters: 8(Long)
2025-08-02 23:52:16.540 [http-nio-8080-exec-2] DEBUG c.t.mapper.TodoMapper.selectList -- ==> Parameters: 8(Long)
2025-08-02 23:52:16.540 [http-nio-8080-exec-3] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:52:16.541 [http-nio-8080-exec-3] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 23:52:16.541 [http-nio-8080-exec-8] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:52:16.541 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:52:16.542 [http-nio-8080-exec-1] DEBUG c.t.mapper.TodoMapper.selectList -- <==      Total: 6
2025-08-02 23:52:16.542 [http-nio-8080-exec-2] DEBUG c.t.mapper.TodoMapper.selectList -- <==      Total: 6
2025-08-02 23:52:16.542 [http-nio-8080-exec-3] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:52:16.542 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:52:16.542 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:52:16.542 [http-nio-8080-exec-3] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:52:16.542 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:52:16.543 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:52:16.543 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:52:16.543 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:52:16.544 [http-nio-8080-exec-3] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:52:16.544 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:52:16.544 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:52:16.544 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:52:16.544 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:52:16.545 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:52:16.545 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:52:16.546 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:52:16.547 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:52:16.547 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:52:16.548 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:52:16.548 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:52:16.548 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:52:16.548 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:52:16.548 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:52:16.549 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:52:16.549 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:52:16.549 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:52:16.549 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:52:16.550 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:52:16.550 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:52:16.550 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:52:16.550 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:52:16.550 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:52:16.550 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:52:16.551 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:52:16.552 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:52:16.553 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:52:16.554 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:52:16.554 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:52:16.555 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:55:47.622 [http-nio-8080-exec-9] DEBUG c.t.m.T.findByTeamIdAndAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE team_id = ? AND account_id = ? AND is_deleted = 0
2025-08-02 23:55:47.623 [http-nio-8080-exec-9] DEBUG c.t.m.T.findByTeamIdAndAccountId -- ==> Parameters: 8(Long), 8(Long)
2025-08-02 23:55:47.623 [http-nio-8080-exec-9] DEBUG c.t.m.T.findByTeamIdAndAccountId -- <==      Total: 1
2025-08-02 23:55:47.624 [http-nio-8080-exec-9] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 23:55:47.624 [http-nio-8080-exec-9] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:55:47.624 [http-nio-8080-exec-9] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 23:55:47.626 [http-nio-8080-exec-9] INFO  c.t.service.UserSessionService -- 会话已失效: accountId=8, jti=c5cfaeb8-77ca-489f-9e0c-160c18104069
2025-08-02 23:55:47.626 [http-nio-8080-exec-9] INFO  c.t.service.UserSessionService -- 创建用户会话: accountId=8, tokenHash=9eaec3b0-0553-44fb-9198-4e181cc3543b
2025-08-02 23:55:47.627 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.updateById -- ==>  Preparing: UPDATE team_member SET team_id=?, account_id=?, is_creator=?, role=?, assigned_at=?, last_access_time=?, is_active=?, created_at=?, updated_at=? WHERE id=? AND is_deleted=0
2025-08-02 23:55:47.627 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.updateById -- ==> Parameters: 8(Long), 8(Long), true(Boolean), TEAM_CREATOR(String), 2025-07-23T12:07:51(LocalDateTime), 2025-08-02T23:55:47.*********(LocalDateTime), true(Boolean), 2025-07-23T12:07:51(LocalDateTime), 2025-07-29T22:22:34(LocalDateTime), 7(Long)
2025-08-02 23:55:47.628 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.updateById -- <==    Updates: 1
2025-08-02 23:55:47.629 [http-nio-8080-exec-9] INFO  com.teammanage.service.AuthService -- 团队选择成功: userId=8, teamId=8, isCreator=true
2025-08-02 23:55:47.648 [http-nio-8080-exec-10] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 23:55:47.648 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByTeamIdAndAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE team_id = ? AND account_id = ? AND is_deleted = 0
2025-08-02 23:55:47.648 [http-nio-8080-exec-10] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:55:47.648 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByTeamIdAndAccountId -- ==> Parameters: 8(Long), 8(Long)
2025-08-02 23:55:47.649 [http-nio-8080-exec-10] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 23:55:47.649 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByTeamIdAndAccountId -- <==      Total: 1
2025-08-02 23:55:47.650 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:55:47.651 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:55:47.652 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:55:47.653 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:55:47.653 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:55:47.654 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:55:49.793 [http-nio-8080-exec-7] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-02 23:55:49.793 [http-nio-8080-exec-1] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:55:49.793 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:55:49.794 [http-nio-8080-exec-8] DEBUG c.t.mapper.TodoMapper.selectList -- ==>  Preparing: SELECT id,title,description,status,priority,user_id,created_at,updated_at FROM todos WHERE (user_id = ?)
2025-08-02 23:55:49.794 [http-nio-8080-exec-7] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:55:49.795 [http-nio-8080-exec-1] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:55:49.795 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:55:49.795 [http-nio-8080-exec-8] DEBUG c.t.mapper.TodoMapper.selectList -- ==> Parameters: 8(Long)
2025-08-02 23:55:49.796 [http-nio-8080-exec-7] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-02 23:55:49.796 [http-nio-8080-exec-2] DEBUG c.t.mapper.TodoMapper.selectList -- ==>  Preparing: SELECT id,title,description,status,priority,user_id,created_at,updated_at FROM todos WHERE (user_id = ?) ORDER BY created_at DESC
2025-08-02 23:55:49.796 [http-nio-8080-exec-1] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:55:49.796 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:55:49.797 [http-nio-8080-exec-7] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:55:49.797 [http-nio-8080-exec-8] DEBUG c.t.mapper.TodoMapper.selectList -- <==      Total: 6
2025-08-02 23:55:49.797 [http-nio-8080-exec-2] DEBUG c.t.mapper.TodoMapper.selectList -- ==> Parameters: 8(Long)
2025-08-02 23:55:49.797 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:55:49.797 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:55:49.798 [http-nio-8080-exec-7] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:55:49.798 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:55:49.799 [http-nio-8080-exec-2] DEBUG c.t.mapper.TodoMapper.selectList -- <==      Total: 6
2025-08-02 23:55:49.799 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:55:49.800 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:55:49.800 [http-nio-8080-exec-7] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:55:49.800 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:55:49.801 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:55:49.801 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:55:49.801 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:55:49.802 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:55:49.802 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:55:49.802 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:55:49.803 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:55:49.803 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:55:49.803 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:55:49.803 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:55:49.803 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:55:49.803 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:55:49.803 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:55:49.803 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:55:49.803 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:55:49.804 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:55:49.804 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:55:49.804 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:55:49.804 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:55:49.804 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:55:49.804 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:55:49.804 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:55:49.804 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:55:49.805 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:55:49.805 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:55:49.805 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:55:49.805 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:55:49.805 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:55:49.805 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:55:49.806 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:58:59.208 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:58:59.210 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:58:59.212 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:58:59.213 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:58:59.213 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:58:59.214 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:58:59.215 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:58:59.215 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:58:59.215 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:58:59.216 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:58:59.216 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:58:59.217 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:58:59.217 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:58:59.217 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:58:59.218 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:59:15.427 [http-nio-8080-exec-3] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:59:15.427 [http-nio-8080-exec-3] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:59:15.428 [http-nio-8080-exec-3] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:59:15.428 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:59:15.429 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:59:15.430 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:59:15.430 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:59:15.431 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:59:15.431 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:59:15.431 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:59:15.432 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:59:15.432 [http-nio-8080-exec-3] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:59:15.433 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:59:15.433 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:59:15.433 [http-nio-8080-exec-3] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:59:34.916 [http-nio-8080-exec-9] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:59:34.916 [http-nio-8080-exec-9] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:59:34.918 [http-nio-8080-exec-9] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:59:34.919 [http-nio-8080-exec-9] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:59:34.919 [http-nio-8080-exec-9] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:59:34.920 [http-nio-8080-exec-9] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:59:34.920 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:59:34.921 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:59:34.921 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:59:34.921 [http-nio-8080-exec-9] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:59:34.922 [http-nio-8080-exec-9] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:59:34.922 [http-nio-8080-exec-9] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:59:34.922 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:59:34.922 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:59:34.923 [http-nio-8080-exec-9] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:59:52.250 [http-nio-8080-exec-10] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-02 23:59:52.251 [http-nio-8080-exec-10] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-02 23:59:52.252 [http-nio-8080-exec-10] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-02 23:59:52.252 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:59:52.253 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-02 23:59:52.254 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:59:52.254 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:59:52.254 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-02 23:59:52.255 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-02 23:59:52.256 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-02 23:59:52.256 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-02 23:59:52.257 [http-nio-8080-exec-10] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-02 23:59:52.257 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-02 23:59:52.257 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-02 23:59:52.258 [http-nio-8080-exec-10] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
