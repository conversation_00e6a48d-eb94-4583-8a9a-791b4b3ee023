package com.teammanage.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teammanage.entity.Team;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 团队Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface TeamMapper extends BaseMapper<Team> {

    /**
     * 根据创建者ID查询团队列表
     * 
     * @param createdBy 创建者ID
     * @return 团队列表
     */
    @Select("SELECT * FROM team WHERE created_by = #{createdBy} AND is_deleted = 0")
    List<Team> findByCreatedBy(@Param("createdBy") Long createdBy);

    /**
     * 检查团队名称是否存在
     * 
     * @param name 团队名称
     * @return 是否存在
     */
    @Select("SELECT COUNT(1) FROM team WHERE name = #{name} AND is_deleted = 0")
    boolean existsByName(@Param("name") String name);

}
