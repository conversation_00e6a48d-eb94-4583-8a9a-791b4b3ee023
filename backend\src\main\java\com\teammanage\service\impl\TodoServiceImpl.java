package com.teammanage.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.teammanage.dto.request.CreateTodoRequest;
import com.teammanage.dto.request.UpdateTodoRequest;
import com.teammanage.dto.response.TodoResponse;
import com.teammanage.entity.Todo;
import com.teammanage.exception.BusinessException;
import com.teammanage.exception.ResourceNotFoundException;
import com.teammanage.mapper.TodoMapper;
import com.teammanage.service.TodoService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * TODO服务实现
 */
@Service
public class TodoServiceImpl implements TodoService {

    @Autowired
    private TodoMapper todoMapper;

    @Override
    public List<TodoResponse> getUserTodos(Long userId) {
        QueryWrapper<Todo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .orderByDesc("created_at");
        
        List<Todo> todos = todoMapper.selectList(queryWrapper);
        
        return todos.stream().map(this::convertToResponse).collect(Collectors.toList());
    }

    @Override
    public TodoResponse createTodo(CreateTodoRequest request, Long userId) {
        Todo todo = new Todo();
        todo.setTitle(request.getTitle());
        todo.setDescription(request.getDescription());
        todo.setPriority(request.getPriority());
        todo.setUserId(userId);
        todo.setStatus(0); // 默认未完成
        todo.setCreatedAt(LocalDateTime.now());
        todo.setUpdatedAt(LocalDateTime.now());
        
        todoMapper.insert(todo);
        
        return convertToResponse(todo);
    }

    @Override
    public TodoResponse updateTodo(Long todoId, UpdateTodoRequest request, Long userId) {
        Todo todo = todoMapper.selectById(todoId);
        if (todo == null) {
            throw new ResourceNotFoundException("TODO不存在");
        }
        if (!todo.getUserId().equals(userId)) {
            throw new BusinessException(403, "无权限访问此TODO");
        }
        
        if (request.getTitle() != null) {
            todo.setTitle(request.getTitle());
        }
        if (request.getDescription() != null) {
            todo.setDescription(request.getDescription());
        }
        if (request.getStatus() != null) {
            todo.setStatus(request.getStatus());
        }
        if (request.getPriority() != null) {
            todo.setPriority(request.getPriority());
        }
        todo.setUpdatedAt(LocalDateTime.now());
        
        todoMapper.updateById(todo);
        
        return convertToResponse(todo);
    }

    @Override
    public void deleteTodo(Long todoId, Long userId) {
        Todo todo = todoMapper.selectById(todoId);
        if (todo == null) {
            throw new ResourceNotFoundException("TODO不存在");
        }
        if (!todo.getUserId().equals(userId)) {
            throw new BusinessException(403, "无权限删除此TODO");
        }
        
        todoMapper.deleteById(todoId);
    }

    @Override
    public Map<String, Object> getTodoStats(Long userId) {
        QueryWrapper<Todo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        
        List<Todo> todos = todoMapper.selectList(queryWrapper);
        
        Map<String, Object> stats = new HashMap<>();
        
        // 按优先级统计未完成任务
        long highPriorityCount = todos.stream()
            .filter(t -> t.getStatus() == 0 && t.getPriority() == 3)
            .count();
        long mediumPriorityCount = todos.stream()
            .filter(t -> t.getStatus() == 0 && t.getPriority() == 2)
            .count();
        long lowPriorityCount = todos.stream()
            .filter(t -> t.getStatus() == 0 && t.getPriority() == 1)
            .count();
        
        stats.put("highPriorityCount", highPriorityCount);
        stats.put("mediumPriorityCount", mediumPriorityCount);
        stats.put("lowPriorityCount", lowPriorityCount);
        
        // 计算完成百分比
        long totalCount = todos.size();
        long completedCount = todos.stream().filter(t -> t.getStatus() == 1).count();
        int completionPercentage = totalCount > 0 ? (int) ((completedCount * 100) / totalCount) : 0;
        
        stats.put("totalCount", totalCount);
        stats.put("completedCount", completedCount);
        stats.put("completionPercentage", completionPercentage);
        
        return stats;
    }

    private TodoResponse convertToResponse(Todo todo) {
        TodoResponse response = new TodoResponse();
        BeanUtils.copyProperties(todo, response);
        return response;
    }
}
