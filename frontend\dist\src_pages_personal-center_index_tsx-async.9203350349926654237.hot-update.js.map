{"version": 3, "sources": ["src_pages_personal-center_index_tsx-async.9203350349926654237.hot-update.js", "src/pages/personal-center/index.tsx", "F:\\Project\\teamAuth\\frontend\\src\\pages\\personal-center\\DataOverview.module.css?asmodule", "src/pages/personal-center/DataOverview.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/personal-center/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='8437007308745731899';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/settings/index.tsx\":[\"p__settings__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import { useModel } from '@umijs/max';\nimport { Col, Row, Spin } from 'antd';\nimport { ProCard } from '@ant-design/pro-components';\nimport React from 'react';\nimport UserFloatButton from '@/components/FloatButton';\nimport TeamListCard from './TeamListCard';\nimport TodoManagement from './TodoManagement';\nimport PersonalInfo from './PersonalInfo';\nimport DataOverview from './DataOverview';\n\n/**\n * 个人中心页面组件\n *\n * 这是用户的个人中心主页面，提供用户信息管理、团队管理、待办事项等功能。\n * 是用户进行个人设置和团队操作的主要入口页面。\n *\n * 页面功能：\n * 1. 用户个人信息展示和编辑\n * 2. 团队列表显示和团队切换\n * 3. 个人待办事项管理\n * 4. 全局浮动操作按钮\n *\n * 页面结构：\n * - 左列：个人信息、团队列表（响应式布局）\n * - 右列：待办事项管理（响应式布局）\n * - 数据概览：独立的水平卡片组件，位于个人信息下方\n * - 浮动：全局操作按钮\n *\n * 权限控制：\n * - 需要用户登录才能访问\n * - 自动检查登录状态并重定向\n * - 支持登录状态变化的实时响应\n *\n * 响应式设计：\n * - 移动端：垂直堆叠布局\n * - 桌面端：左右分栏布局\n * - 自适应不同屏幕尺寸\n */\nconst PersonalCenterPage: React.FC = () => {\n  /**\n   * 全局状态管理\n   *\n   * 从UmiJS全局状态中获取用户信息和加载状态：\n   * - initialState: 包含用户和团队信息的全局状态\n   * - loading: 全局状态的加载状态\n   */\n  const { initialState, loading } = useModel('@@initialState');\n\n\n\n  /**\n   * 加载状态处理\n   *\n   * 当全局状态正在初始化时，显示加载界面。\n   * 这确保了用户在状态加载完成前看到友好的加载提示。\n   */\n  if (loading) {\n    return (\n      <div\n        style={{\n          minHeight: '100vh',\n          background: '#f5f8ff',\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n        }}\n      >\n        <Spin size=\"large\" />\n        <div style={{ marginLeft: 16 }}>正在加载用户信息...</div>\n      </div>\n    );\n  }\n\n  /**\n   * 登录状态检查已由应用级路由守卫处理\n   *\n   * 移除页面级别的登录检查，避免与应用级路由守卫冲突。\n   * 应用级路由守卫在 app.tsx 中的 onPageChange 函数已经处理了\n   * 用户登录状态检查和重定向逻辑，无需在页面组件中重复检查。\n   *\n   * 这样可以避免登录成功后的状态更新时序问题，确保用户\n   * 一次登录成功后能够正常访问个人中心页面。\n   */\n\n  return (\n    <>\n      {/* 页面主容器 */}\n      <div\n        style={{\n          minHeight: '100vh',\n          background: '#f5f8ff', // 浅蓝色背景，营造清新的视觉效果\n          padding: '12px 12px 24px 12px', // 移动端优化：减少左右边距，增加底部边距\n        }}\n      >\n        {/*\n         * 主内容卡片容器\n         *\n         * 使用Card组件作为主要内容的容器，提供：\n         * 1. 统一的视觉边界和阴影效果\n         * 2. 响应式的内边距设置\n         * 3. 圆角设计提升视觉体验\n         * 4. 全高度布局适配不同屏幕\n         */}\n        <ProCard\n          style={{\n            width: '100%',\n            minHeight: 'calc(100vh - 48px)', // 减去外层padding的高度\n            borderRadius: '12px', // 圆角设计\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)', // 轻微阴影效果\n          }}\n          bodyStyle={{\n            padding: '24px', // 内容区域的内边距\n          }}\n        >\n          {/* 数据概览部分 - 全宽显示 */}\n          <DataOverview />\n\n          {/*\n           * 响应式两列布局\n           *\n           * 使用Ant Design的Row/Col组件实现响应式两列布局：\n           * - 移动端：垂直堆叠，所有组件占满宽度\n           * - 桌面端：左列包含个人信息、团队列表；右列包含待办事项\n           * - gutter: 组件间距设置\n           * - margin: 0: 避免Row组件的默认负边距影响布局\n           */}\n          <Row gutter={[24, 12]} style={{ margin: 0 }}>\n            {/*\n             * 左列：个人信息区域\n             *\n             * 包含以下组件（按顺序）：\n             * 1. 个人信息部分（用户详情、头像、姓名、登录信息等）\n             * 2. 团队列表部分（用户所属团队列表）\n             *\n             * 响应式布局：\n             * - 移动端(xs-md)：全宽显示\n             * - 桌面端(lg+)：占据左半部分\n             */}\n            <Col\n              xs={24}\n              sm={24}\n              md={24}\n              lg={12}\n              xl={12}\n              xxl={12}\n            >\n              {/* 个人信息部分（包含用户详情、姓名、登录信息） */}\n              <PersonalInfo />\n\n              {/* 团队列表部分（用户所属团队列表） */}\n              <TeamListCard />\n            </Col>\n\n            {/*\n             * 右列：待办事项管理区域\n             *\n             * 个人待办事项的管理界面，支持添加、编辑、删除待办事项。\n             * 响应式布局：\n             * - 移动端(xs-md)：全宽显示\n             * - 桌面端(lg+)：占据右半部分\n             */}\n            <Col\n              xs={24}\n              sm={24}\n              md={24}\n              lg={12}\n              xl={12}\n              xxl={12}\n            >\n              <TodoManagement />\n            </Col>\n          </Row>\n        </ProCard>\n      </div>\n\n      {/*\n       * 全局浮动操作按钮\n       *\n       * 提供快速访问常用功能的浮动按钮，如：\n       * - 快速创建团队\n       * - 用户设置\n       * - 帮助信息\n       *\n       * 位置固定在页面右下角，不受页面滚动影响。\n       */}\n      <UserFloatButton />\n\n\n\n\n    </>\n  );\n};\n\nexport default PersonalCenterPage;\n", "\nimport \"F:/Project/teamAuth/frontend/src/pages/personal-center/DataOverview.module.css?modules\";\nexport default {\"errorAnimation\": `errorAnimation-NZNd358D`,\"vehicleCard\": `vehicleCard-0mxlIPL1`,\"fadeInUp\": `fadeInUp-bFqfnaEw`,\"statsCard\": `statsCard-0TbvigJF`,\"loadingCard\": `loadingCard-GDzrCBhM`,\"errorShake\": `errorShake-wpgXoB3R`,\"shimmer\": `shimmer-dA25g4ud`,\"fadeInDelay1\": `fadeInDelay1-V7zK7ccQ`,\"fadeInDelay2\": `fadeInDelay2-ua4hNaTO`,\"fadeInDelay3\": `fadeInDelay3-maMANaNI`,\"fadeInDelay4\": `fadeInDelay4-pZVLL5xX`,\"alertCard\": `alertCard-6usHqP81`,\"personnelCard\": `personnelCard-Dq6Syc9G`,\"warningCard\": `warningCard-xiWnZil3`,\"successAnimation\": `successAnimation-hpyb3zGP`,\"successPulse\": `successPulse-9Z25n3ZT`}\n", "import {\n  BarChartOutlined,\n  CarOutlined,\n  UsergroupAddOutlined,\n  ExclamationCircleOutlined,\n  AlertOutlined,\n} from '@ant-design/icons';\nimport {\n  Alert,\n  Card,\n  Col,\n  Row,\n  Spin,\n} from 'antd';\nimport { ProCard } from '@ant-design/pro-components';\nimport React, { useEffect, useState } from 'react';\nimport { UserService } from '@/services/user';\nimport type { UserPersonalStatsResponse } from '@/types/api';\nimport styles from './DataOverview.module.css';\n\n/**\n * 数据概览卡片组件\n *\n * 显示用户的个人统计数据，采用单行四列的水平布局。\n * 包括车辆、人员、预警、告警等指标的统计卡片。\n *\n * 主要功能：\n * 1. 显示车辆数量统计\n * 2. 显示人员数量统计\n * 3. 显示预警数量统计\n * 4. 显示告警数量统计\n *\n * 数据来源：\n * - 个人统计数据：通过UserService.getUserPersonalStats()获取\n *\n * 布局特点：\n * - 单行四列水平排列\n * - 每个统计项独立的卡片设计\n * - 响应式布局适配不同屏幕\n */\nconst DataOverview: React.FC = () => {\n  /**\n   * 个人统计数据状态管理\n   */\n  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({\n    vehicles: 0,\n    personnel: 0,\n    warnings: 0,\n    alerts: 0,\n  });\n\n  const [statsLoading, setStatsLoading] = useState(true);\n  const [statsError, setStatsError] = useState<string | null>(null);\n\n  // 获取统计数据\n  useEffect(() => {\n    const fetchStatsData = async () => {\n      try {\n        const stats = await UserService.getUserPersonalStats();\n        setPersonalStats(stats);\n        setStatsError(null);\n      } catch (error) {\n        console.error('获取统计数据失败:', error);\n        setStatsError('获取统计数据失败，请稍后重试');\n      } finally {\n        setStatsLoading(false);\n      }\n    };\n\n    fetchStatsData();\n  }, []);\n\n  return (\n    <ProCard\n      title={\n        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>\n          <BarChartOutlined style={{ fontSize: 16, color: '#1890ff' }} />\n          <span>数据概览</span>\n        </div>\n      }\n      style={{\n        marginBottom: 16,\n        borderRadius: 12,\n        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',\n      }}\n      headStyle={{\n        borderBottom: '1px solid #f0f0f0',\n        paddingBottom: 12,\n      }}\n      bodyStyle={{\n        padding: '20px',\n      }}\n      hoverable\n    >\n      {statsError ? (\n        <Alert\n          message=\"数据概览加载失败\"\n          description={statsError}\n          type=\"error\"\n          showIcon\n          style={{\n            borderRadius: 8,\n          }}\n        />\n      ) : (\n        <Spin spinning={statsLoading}>\n          {/* 单行四列布局 */}\n          <Row gutter={[16, 16]}>\n            {/* 车辆统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <Card\n                className={`${styles.statsCard} ${styles.vehicleCard} ${styles.fadeInDelay1}`}\n                styles={{\n                  body: {\n                    padding: '20px 16px',\n                    textAlign: 'center',\n                  },\n                }}\n                hoverable\n              >\n                <div style={{ marginBottom: 12 }}>\n                  <CarOutlined\n                    style={{\n                      fontSize: 24,\n                      color: '#1890ff',\n                      marginBottom: 8,\n                    }}\n                  />\n                </div>\n                <div\n                  style={{\n                    fontSize: 32,\n                    fontWeight: 700,\n                    color: '#1890ff',\n                    lineHeight: 1,\n                    marginBottom: 8,\n                  }}\n                >\n                  {personalStats.vehicles}\n                </div>\n                <div\n                  style={{\n                    fontSize: 14,\n                    color: '#1890ff',\n                    fontWeight: 600,\n                    opacity: 0.8,\n                  }}\n                >\n                  车辆\n                </div>\n              </Card>\n            </Col>\n\n            {/* 人员统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <Card\n                className={`${styles.statsCard} ${styles.personnelCard} ${styles.fadeInDelay2}`}\n                styles={{\n                  body: {\n                    padding: '20px 16px',\n                    textAlign: 'center',\n                  },\n                }}\n                hoverable\n              >\n                <div style={{ marginBottom: 12 }}>\n                  <UsergroupAddOutlined\n                    style={{\n                      fontSize: 24,\n                      color: '#52c41a',\n                      marginBottom: 8,\n                    }}\n                  />\n                </div>\n                <div\n                  style={{\n                    fontSize: 32,\n                    fontWeight: 700,\n                    color: '#52c41a',\n                    lineHeight: 1,\n                    marginBottom: 8,\n                  }}\n                >\n                  {personalStats.personnel}\n                </div>\n                <div\n                  style={{\n                    fontSize: 14,\n                    color: '#52c41a',\n                    fontWeight: 600,\n                    opacity: 0.8,\n                  }}\n                >\n                  人员\n                </div>\n              </Card>\n            </Col>\n\n            {/* 预警统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <Card\n                className={`${styles.statsCard} ${styles.warningCard} ${styles.fadeInDelay3}`}\n                styles={{\n                  body: {\n                    padding: '20px 16px',\n                    textAlign: 'center',\n                  },\n                }}\n                hoverable\n              >\n                <div style={{ marginBottom: 12 }}>\n                  <ExclamationCircleOutlined\n                    style={{\n                      fontSize: 24,\n                      color: '#faad14',\n                      marginBottom: 8,\n                    }}\n                  />\n                </div>\n                <div\n                  style={{\n                    fontSize: 32,\n                    fontWeight: 700,\n                    color: '#faad14',\n                    lineHeight: 1,\n                    marginBottom: 8,\n                  }}\n                >\n                  {personalStats.warnings}\n                </div>\n                <div\n                  style={{\n                    fontSize: 14,\n                    color: '#faad14',\n                    fontWeight: 600,\n                    opacity: 0.8,\n                  }}\n                >\n                  预警\n                </div>\n              </Card>\n            </Col>\n\n            {/* 告警统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <Card\n                className={`${styles.statsCard} ${styles.alertCard} ${styles.fadeInDelay4}`}\n                styles={{\n                  body: {\n                    padding: '20px 16px',\n                    textAlign: 'center',\n                  },\n                }}\n                hoverable\n              >\n                <div style={{ marginBottom: 12 }}>\n                  <AlertOutlined\n                    style={{\n                      fontSize: 24,\n                      color: '#ff4d4f',\n                      marginBottom: 8,\n                    }}\n                  />\n                </div>\n                <div\n                  style={{\n                    fontSize: 32,\n                    fontWeight: 700,\n                    color: '#ff4d4f',\n                    lineHeight: 1,\n                    marginBottom: 8,\n                  }}\n                >\n                  {personalStats.alerts}\n                </div>\n                <div\n                  style={{\n                    fontSize: 14,\n                    color: '#ff4d4f',\n                    fontWeight: 600,\n                    opacity: 0.8,\n                  }}\n                >\n                  告警\n                </div>\n              </Card>\n            </Col>\n          </Row>\n        </Spin>\n      )}\n    </ProCard>\n  );\n};\n\nexport default DataOverview;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCC+Lb;;;2BAAA;;;;;;;wCAlMyB;yCACM;kDACP;mFACN;yFACU;0FACH;4FACE;0FACF;0FACA;;;;;;;;;;YAEzB;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2BC,GACD,MAAM,qBAA+B;;gBACnC;;;;;;GAMC,GACD,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,IAAA,aAAQ,EAAC;gBAI3C;;;;;GAKC,GACD,IAAI,SACF,qBACE,2BAAC;oBACC,OAAO;wBACL,WAAW;wBACX,YAAY;wBACZ,SAAS;wBACT,gBAAgB;wBAChB,YAAY;oBACd;;sCAEA,2BAAC,UAAI;4BAAC,MAAK;;;;;;sCACX,2BAAC;4BAAI,OAAO;gCAAE,YAAY;4BAAG;sCAAG;;;;;;;;;;;;gBAKtC;;;;;;;;;GASC,GAED,qBACE;;sCAEE,2BAAC;4BACC,OAAO;gCACL,WAAW;gCACX,YAAY;gCACZ,SAAS;4BACX;sCAWA,cAAA,2BAAC,sBAAO;gCACN,OAAO;oCACL,OAAO;oCACP,WAAW;oCACX,cAAc;oCACd,WAAW;gCACb;gCACA,WAAW;oCACT,SAAS;gCACX;;kDAGA,2BAAC,qBAAY;;;;;kDAWb,2BAAC,SAAG;wCAAC,QAAQ;4CAAC;4CAAI;yCAAG;wCAAE,OAAO;4CAAE,QAAQ;wCAAE;;0DAYxC,2BAAC,SAAG;gDACF,IAAI;gDACJ,IAAI;gDACJ,IAAI;gDACJ,IAAI;gDACJ,IAAI;gDACJ,KAAK;;kEAGL,2BAAC,qBAAY;;;;;kEAGb,2BAAC,qBAAY;;;;;;;;;;;0DAWf,2BAAC,SAAG;gDACF,IAAI;gDACJ,IAAI;gDACJ,IAAI;gDACJ,IAAI;gDACJ,IAAI;gDACJ,KAAK;0DAEL,cAAA,2BAAC,uBAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAgBvB,2BAAC,oBAAe;;;;;;;YAOtB;eA1JM;;oBAQ8B,aAAQ;;;iBARtC;gBA4JN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCChMf;;;2BAAA;;;;gBAAA,WAAe;gBAAC,kBAAkB,CAAC,uBAAuB,CAAC;gBAAC,eAAe,CAAC,oBAAoB,CAAC;gBAAC,YAAY,CAAC,iBAAiB,CAAC;gBAAC,aAAa,CAAC,kBAAkB,CAAC;gBAAC,eAAe,CAAC,oBAAoB,CAAC;gBAAC,cAAc,CAAC,mBAAmB,CAAC;gBAAC,WAAW,CAAC,gBAAgB,CAAC;gBAAC,gBAAgB,CAAC,qBAAqB,CAAC;gBAAC,gBAAgB,CAAC,qBAAqB,CAAC;gBAAC,gBAAgB,CAAC,qBAAqB,CAAC;gBAAC,gBAAgB,CAAC,qBAAqB,CAAC;gBAAC,aAAa,CAAC,kBAAkB,CAAC;gBAAC,iBAAiB,CAAC,sBAAsB,CAAC;gBAAC,eAAe,CAAC,oBAAoB,CAAC;gBAAC,oBAAoB,CAAC,yBAAyB,CAAC;gBAAC,gBAAgB,CAAC,qBAAqB,CAAC;YAAA;;;;;;;wCCoSrnB;;;2BAAA;;;;;;;0CAhSO;yCAOA;kDACiB;oFACmB;yCACf;2GAET;;;;;;;;;;YAEnB;;;;;;;;;;;;;;;;;;;CAmBC,GACD,MAAM,eAAyB;;gBAC7B;;GAEC,GACD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAA4B;oBAC5E,UAAU;oBACV,WAAW;oBACX,UAAU;oBACV,QAAQ;gBACV;gBAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAC;gBACjD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAgB;gBAE5D,SAAS;gBACT,IAAA,gBAAS,EAAC;oBACR,MAAM,iBAAiB;wBACrB,IAAI;4BACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,oBAAoB;4BACpD,iBAAiB;4BACjB,cAAc;wBAChB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,aAAa;4BAC3B,cAAc;wBAChB,SAAU;4BACR,gBAAgB;wBAClB;oBACF;oBAEA;gBACF,GAAG,EAAE;gBAEL,qBACE,2BAAC,sBAAO;oBACN,qBACE,2BAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,YAAY;4BAAU,KAAK;wBAAE;;0CAC1D,2BAAC,uBAAgB;gCAAC,OAAO;oCAAE,UAAU;oCAAI,OAAO;gCAAU;;;;;;0CAC1D,2BAAC;0CAAK;;;;;;;;;;;;oBAGV,OAAO;wBACL,cAAc;wBACd,cAAc;wBACd,WAAW;oBACb;oBACA,WAAW;wBACT,cAAc;wBACd,eAAe;oBACjB;oBACA,WAAW;wBACT,SAAS;oBACX;oBACA,SAAS;8BAER,2BACC,2BAAC,WAAK;wBACJ,SAAQ;wBACR,aAAa;wBACb,MAAK;wBACL,QAAQ;wBACR,OAAO;4BACL,cAAc;wBAChB;;;;;6CAGF,2BAAC,UAAI;wBAAC,UAAU;kCAEd,cAAA,2BAAC,SAAG;4BAAC,QAAQ;gCAAC;gCAAI;6BAAG;;8CAEnB,2BAAC,SAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAG,IAAI;oCAAG,IAAI;oCAAG,IAAI;8CACpC,cAAA,2BAAC,UAAI;wCACH,WAAW,CAAC,EAAE,sCAAM,CAAC,SAAS,CAAC,CAAC,EAAE,sCAAM,CAAC,WAAW,CAAC,CAAC,EAAE,sCAAM,CAAC,YAAY,CAAC,CAAC;wCAC7E,QAAQ;4CACN,MAAM;gDACJ,SAAS;gDACT,WAAW;4CACb;wCACF;wCACA,SAAS;;0DAET,2BAAC;gDAAI,OAAO;oDAAE,cAAc;gDAAG;0DAC7B,cAAA,2BAAC,kBAAW;oDACV,OAAO;wDACL,UAAU;wDACV,OAAO;wDACP,cAAc;oDAChB;;;;;;;;;;;0DAGJ,2BAAC;gDACC,OAAO;oDACL,UAAU;oDACV,YAAY;oDACZ,OAAO;oDACP,YAAY;oDACZ,cAAc;gDAChB;0DAEC,cAAc,QAAQ;;;;;;0DAEzB,2BAAC;gDACC,OAAO;oDACL,UAAU;oDACV,OAAO;oDACP,YAAY;oDACZ,SAAS;gDACX;0DACD;;;;;;;;;;;;;;;;;8CAOL,2BAAC,SAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAG,IAAI;oCAAG,IAAI;oCAAG,IAAI;8CACpC,cAAA,2BAAC,UAAI;wCACH,WAAW,CAAC,EAAE,sCAAM,CAAC,SAAS,CAAC,CAAC,EAAE,sCAAM,CAAC,aAAa,CAAC,CAAC,EAAE,sCAAM,CAAC,YAAY,CAAC,CAAC;wCAC/E,QAAQ;4CACN,MAAM;gDACJ,SAAS;gDACT,WAAW;4CACb;wCACF;wCACA,SAAS;;0DAET,2BAAC;gDAAI,OAAO;oDAAE,cAAc;gDAAG;0DAC7B,cAAA,2BAAC,2BAAoB;oDACnB,OAAO;wDACL,UAAU;wDACV,OAAO;wDACP,cAAc;oDAChB;;;;;;;;;;;0DAGJ,2BAAC;gDACC,OAAO;oDACL,UAAU;oDACV,YAAY;oDACZ,OAAO;oDACP,YAAY;oDACZ,cAAc;gDAChB;0DAEC,cAAc,SAAS;;;;;;0DAE1B,2BAAC;gDACC,OAAO;oDACL,UAAU;oDACV,OAAO;oDACP,YAAY;oDACZ,SAAS;gDACX;0DACD;;;;;;;;;;;;;;;;;8CAOL,2BAAC,SAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAG,IAAI;oCAAG,IAAI;oCAAG,IAAI;8CACpC,cAAA,2BAAC,UAAI;wCACH,WAAW,CAAC,EAAE,sCAAM,CAAC,SAAS,CAAC,CAAC,EAAE,sCAAM,CAAC,WAAW,CAAC,CAAC,EAAE,sCAAM,CAAC,YAAY,CAAC,CAAC;wCAC7E,QAAQ;4CACN,MAAM;gDACJ,SAAS;gDACT,WAAW;4CACb;wCACF;wCACA,SAAS;;0DAET,2BAAC;gDAAI,OAAO;oDAAE,cAAc;gDAAG;0DAC7B,cAAA,2BAAC,gCAAyB;oDACxB,OAAO;wDACL,UAAU;wDACV,OAAO;wDACP,cAAc;oDAChB;;;;;;;;;;;0DAGJ,2BAAC;gDACC,OAAO;oDACL,UAAU;oDACV,YAAY;oDACZ,OAAO;oDACP,YAAY;oDACZ,cAAc;gDAChB;0DAEC,cAAc,QAAQ;;;;;;0DAEzB,2BAAC;gDACC,OAAO;oDACL,UAAU;oDACV,OAAO;oDACP,YAAY;oDACZ,SAAS;gDACX;0DACD;;;;;;;;;;;;;;;;;8CAOL,2BAAC,SAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAG,IAAI;oCAAG,IAAI;oCAAG,IAAI;8CACpC,cAAA,2BAAC,UAAI;wCACH,WAAW,CAAC,EAAE,sCAAM,CAAC,SAAS,CAAC,CAAC,EAAE,sCAAM,CAAC,SAAS,CAAC,CAAC,EAAE,sCAAM,CAAC,YAAY,CAAC,CAAC;wCAC3E,QAAQ;4CACN,MAAM;gDACJ,SAAS;gDACT,WAAW;4CACb;wCACF;wCACA,SAAS;;0DAET,2BAAC;gDAAI,OAAO;oDAAE,cAAc;gDAAG;0DAC7B,cAAA,2BAAC,oBAAa;oDACZ,OAAO;wDACL,UAAU;wDACV,OAAO;wDACP,cAAc;oDAChB;;;;;;;;;;;0DAGJ,2BAAC;gDACC,OAAO;oDACL,UAAU;oDACV,YAAY;oDACZ,OAAO;oDACP,YAAY;oDACZ,cAAc;gDAChB;0DAEC,cAAc,MAAM;;;;;;0DAEvB,2BAAC;gDACC,OAAO;oDACL,UAAU;oDACV,OAAO;oDACP,YAAY;oDACZ,SAAS;gDACX;0DACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUjB;eA5PM;iBAAA;gBA8PN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;IHnSD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,gCAA+B;YAAC;SAAqB;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AACrjB"}