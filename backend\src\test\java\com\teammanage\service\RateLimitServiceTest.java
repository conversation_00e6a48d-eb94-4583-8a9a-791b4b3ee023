package com.teammanage.service;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.teammanage.exception.RateLimitException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 速率限制服务测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class RateLimitServiceTest {

    @Mock
    private Cache<String, Object> cache;

    @InjectMocks
    private RateLimitService rateLimitService;

    @BeforeEach
    void setUp() {
        // 设置配置参数
        ReflectionTestUtils.setField(rateLimitService, "apiCallsPerMinute", 60);
        ReflectionTestUtils.setField(rateLimitService, "invitationsPerHour", 10);
        ReflectionTestUtils.setField(rateLimitService, "teamCreationPerDay", 5);
        ReflectionTestUtils.setField(rateLimitService, "loginAttemptsPerHour", 10);
    }

    @Test
    void testCheckApiCallLimit_Success() {
        // Given
        Long userId = 1L;
        String endpoint = "CREATE_TEAM";
        when(cache.getIfPresent(anyString())).thenReturn(null);

        // When & Then
        assertDoesNotThrow(() -> rateLimitService.checkApiCallLimit(userId, endpoint));
        verify(cache).put(anyString(), any());
    }

    @Test
    void testCheckApiCallLimit_ExceedsLimit() {
        // Given - 使用集成测试方式，不使用模拟对象
        Cache<String, Object> realCache = Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(1, TimeUnit.HOURS)
                .build();

        RateLimitService realService = new RateLimitService();
        ReflectionTestUtils.setField(realService, "cache", realCache);
        ReflectionTestUtils.setField(realService, "apiCallsPerMinute", 1);

        Long userId = 1L;
        String endpoint = "CREATE_TEAM";

        // When & Then
        // 第一次调用成功
        assertDoesNotThrow(() -> realService.checkApiCallLimit(userId, endpoint));

        // 第二次调用应该失败
        assertThrows(RateLimitException.class, () ->
            realService.checkApiCallLimit(userId, endpoint));
    }

    @Test
    void testCheckInvitationLimit_Success() {
        // Given
        Long userId = 1L;
        int invitationCount = 5;
        when(cache.getIfPresent(anyString())).thenReturn(null);

        // When & Then
        assertDoesNotThrow(() -> rateLimitService.checkInvitationLimit(userId, invitationCount));
        verify(cache).put(anyString(), any());
    }

    @Test
    void testCheckInvitationLimit_ExceedsLimit() {
        // Given - 使用集成测试方式
        Cache<String, Object> realCache = Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(1, TimeUnit.HOURS)
                .build();

        RateLimitService realService = new RateLimitService();
        ReflectionTestUtils.setField(realService, "cache", realCache);
        ReflectionTestUtils.setField(realService, "invitationsPerHour", 5);

        Long userId = 1L;

        // When & Then
        // 发送5个邀请成功
        assertDoesNotThrow(() -> realService.checkInvitationLimit(userId, 5));

        // 再发送1个邀请应该失败
        assertThrows(RateLimitException.class, () ->
            realService.checkInvitationLimit(userId, 1));
    }

    @Test
    void testCheckTeamCreationLimit_Success() {
        // Given
        Long userId = 1L;
        when(cache.getIfPresent(anyString())).thenReturn(null);

        // When & Then
        assertDoesNotThrow(() -> rateLimitService.checkTeamCreationLimit(userId));
        verify(cache).put(anyString(), any());
    }

    @Test
    void testCheckTeamCreationLimit_ExceedsLimit() {
        // Given - 使用集成测试方式
        Cache<String, Object> realCache = Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(1, TimeUnit.HOURS)
                .build();

        RateLimitService realService = new RateLimitService();
        ReflectionTestUtils.setField(realService, "cache", realCache);
        ReflectionTestUtils.setField(realService, "teamCreationPerDay", 2);

        Long userId = 1L;

        // When & Then
        // 前两次创建成功
        assertDoesNotThrow(() -> realService.checkTeamCreationLimit(userId));
        assertDoesNotThrow(() -> realService.checkTeamCreationLimit(userId));

        // 第三次创建应该失败
        assertThrows(RateLimitException.class, () ->
            realService.checkTeamCreationLimit(userId));
    }

    @Test
    void testCheckLoginAttemptLimit_Success() {
        // Given
        String identifier = "<EMAIL>";
        when(cache.getIfPresent(anyString())).thenReturn(null);

        // When & Then
        assertDoesNotThrow(() -> rateLimitService.checkLoginAttemptLimit(identifier));
        verify(cache).put(anyString(), any());
    }

    @Test
    void testCheckLoginAttemptLimit_ExceedsLimit() {
        // Given - 使用集成测试方式
        Cache<String, Object> realCache = Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(1, TimeUnit.HOURS)
                .build();

        RateLimitService realService = new RateLimitService();
        ReflectionTestUtils.setField(realService, "cache", realCache);
        ReflectionTestUtils.setField(realService, "loginAttemptsPerHour", 2);

        String identifier = "<EMAIL>";

        // When & Then
        // 前两次尝试成功
        assertDoesNotThrow(() -> realService.checkLoginAttemptLimit(identifier));
        assertDoesNotThrow(() -> realService.checkLoginAttemptLimit(identifier));

        // 第三次尝试应该失败
        assertThrows(RateLimitException.class, () ->
            realService.checkLoginAttemptLimit(identifier));
    }

    @Test
    void testResetRateLimit() {
        // Given
        Long userId = 1L;
        String limitType = "INVITATION"; // 使用INVITATION类型，只会调用一次invalidate

        // When
        rateLimitService.resetRateLimit(userId, limitType);

        // Then
        verify(cache).invalidate("invitation:" + userId);
    }

    @Test
    void testGetRateLimitStatus_WithExistingRecord() {
        // Given - 使用集成测试方式
        Cache<String, Object> realCache = Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(1, TimeUnit.HOURS)
                .build();

        RateLimitService realService = new RateLimitService();
        ReflectionTestUtils.setField(realService, "cache", realCache);
        ReflectionTestUtils.setField(realService, "apiCallsPerMinute", 60);

        Long userId = 1L;
        String limitType = "API_CALL";

        // 先进行一次API调用以创建记录
        assertDoesNotThrow(() -> realService.checkApiCallLimit(userId, "TEST"));

        // When
        var status = realService.getRateLimitStatus(userId, limitType);

        // Then
        assertNotNull(status);
        assertTrue(status.containsKey("maxCount"));
        assertTrue(status.containsKey("timeWindow"));
        assertEquals(60, status.get("maxCount"));
        assertEquals("1 minute", status.get("timeWindow"));
    }

    @Test
    void testGetRateLimitStatus_WithoutExistingRecord() {
        // Given
        Long userId = 1L;
        String limitType = "API_CALL";
        when(cache.getIfPresent(anyString())).thenReturn(null);

        // When
        var status = rateLimitService.getRateLimitStatus(userId, limitType);

        // Then
        assertNotNull(status);
        assertEquals(0, status.get("currentCount"));
        assertEquals("N/A", status.get("windowStart"));
        assertEquals("N/A", status.get("lastRequest"));
    }



    /**
     * 集成测试：使用真实的缓存
     */
    @Test
    void testIntegrationWithRealCache() {
        // Given
        Cache<String, Object> realCache = Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(1, TimeUnit.HOURS)
                .build();
        
        RateLimitService realService = new RateLimitService();
        ReflectionTestUtils.setField(realService, "cache", realCache);
        ReflectionTestUtils.setField(realService, "apiCallsPerMinute", 2); // 设置较小的限制便于测试
        
        Long userId = 1L;
        String endpoint = "TEST_ENDPOINT";

        // When & Then
        // 前两次调用应该成功
        assertDoesNotThrow(() -> realService.checkApiCallLimit(userId, endpoint));
        assertDoesNotThrow(() -> realService.checkApiCallLimit(userId, endpoint));
        
        // 第三次调用应该失败
        assertThrows(RateLimitException.class, () -> 
            realService.checkApiCallLimit(userId, endpoint));
    }

    /**
     * 测试重置速率限制功能
     */
    @Test
    void testResetAfterLimit() {
        // Given
        Cache<String, Object> realCache = Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(1, TimeUnit.HOURS)
                .build();

        RateLimitService realService = new RateLimitService();
        ReflectionTestUtils.setField(realService, "cache", realCache);
        ReflectionTestUtils.setField(realService, "apiCallsPerMinute", 1);

        Long userId = 1L;
        String endpoint = "TEST_ENDPOINT";

        // When
        // 第一次调用成功
        assertDoesNotThrow(() -> realService.checkApiCallLimit(userId, endpoint));

        // 立即第二次调用失败
        assertThrows(RateLimitException.class, () ->
            realService.checkApiCallLimit(userId, endpoint));

        // 重置速率限制
        realService.resetRateLimit(userId, "API_CALL");

        // Then
        // 重置后应该可以再次调用
        assertDoesNotThrow(() -> realService.checkApiCallLimit(userId, endpoint));
    }
}
