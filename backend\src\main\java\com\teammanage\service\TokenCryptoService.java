package com.teammanage.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;

/**
 * 令牌加密服务
 * 使用AES-256-GCM加密算法和HMAC-SHA256完整性校验
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class TokenCryptoService {

    private static final Logger log = LoggerFactory.getLogger(TokenCryptoService.class);

    // 加密算法配置
    private static final String AES_ALGORITHM = "AES";
    private static final String AES_TRANSFORMATION = "AES/GCM/NoPadding";
    private static final String HMAC_ALGORITHM = "HmacSHA256";
    private static final int GCM_IV_LENGTH = 12; // GCM推荐的IV长度
    private static final int GCM_TAG_LENGTH = 16; // GCM认证标签长度
    private static final int SALT_LENGTH = 16; // 盐值长度

    // 令牌结构分隔符
    private static final String SEPARATOR = "|";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");

    @Value("${app.security.token.encryption-key:}")
    private String encryptionKeyBase64;

    @Value("${app.security.token.hmac-key:}")
    private String hmacKeyBase64;

    private final SecureRandom secureRandom = new SecureRandom();

    /**
     * 令牌数据结构
     */
    public static class TokenData {
        private Long invitationId;
        private LocalDateTime expiresAt;
        private String salt;

        public TokenData(Long invitationId, LocalDateTime expiresAt, String salt) {
            this.invitationId = invitationId;
            this.expiresAt = expiresAt;
            this.salt = salt;
        }

        // Getters
        public Long getInvitationId() { return invitationId; }
        public LocalDateTime getExpiresAt() { return expiresAt; }
        public String getSalt() { return salt; }
    }

    /**
     * 生成安全的邀请令牌
     *
     * @param invitationId 邀请ID
     * @param expiresAt 过期时间
     * @return 加密的令牌字符串
     */
    public String generateToken(Long invitationId, LocalDateTime expiresAt) {
        try {
            // 生成随机盐值
            byte[] salt = new byte[SALT_LENGTH];
            secureRandom.nextBytes(salt);
            String saltBase64 = Base64.getEncoder().encodeToString(salt);

            // 构建明文数据
            String plaintext = invitationId + SEPARATOR + 
                             expiresAt.format(DATE_FORMATTER) + SEPARATOR + 
                             saltBase64;

            // 加密数据
            String encryptedData = encrypt(plaintext);

            // 生成HMAC签名
            String hmacSignature = generateHMAC(encryptedData);

            // 组合最终令牌：加密数据 + HMAC签名
            String finalToken = encryptedData + SEPARATOR + hmacSignature;

            // 使用URL安全的Base64编码
            return Base64.getUrlEncoder().withoutPadding().encodeToString(
                finalToken.getBytes(StandardCharsets.UTF_8)
            );

        } catch (Exception e) {
            log.error("生成令牌失败: {}", e.getMessage());
            throw new RuntimeException("令牌生成失败", e);
        }
    }

    /**
     * 解析和验证令牌
     *
     * @param token 令牌字符串
     * @return 解析后的令牌数据，如果无效则返回null
     */
    public TokenData parseToken(String token) {
        try {
            // URL安全Base64解码
            byte[] decodedBytes = Base64.getUrlDecoder().decode(token);
            String decodedToken = new String(decodedBytes, StandardCharsets.UTF_8);

            // 分离加密数据和HMAC签名
            String[] parts = decodedToken.split("\\" + SEPARATOR);
            if (parts.length != 2) {
                log.warn("令牌格式无效");
                return null;
            }

            String encryptedData = parts[0];
            String providedHmac = parts[1];

            // 验证HMAC签名
            String calculatedHmac = generateHMAC(encryptedData);
            if (!constantTimeEquals(providedHmac, calculatedHmac)) {
                log.warn("令牌HMAC验证失败");
                return null;
            }

            // 解密数据
            String plaintext = decrypt(encryptedData);
            if (plaintext == null) {
                return null;
            }

            // 解析明文数据
            String[] dataParts = plaintext.split("\\" + SEPARATOR);
            if (dataParts.length != 3) {
                log.warn("令牌数据格式无效");
                return null;
            }

            Long invitationId = Long.parseLong(dataParts[0]);
            LocalDateTime expiresAt = LocalDateTime.parse(dataParts[1], DATE_FORMATTER);
            String salt = dataParts[2];

            // 检查是否过期
            if (expiresAt.isBefore(LocalDateTime.now())) {
                log.warn("令牌已过期: invitationId={}", invitationId);
                return null;
            }

            return new TokenData(invitationId, expiresAt, salt);

        } catch (Exception e) {
            log.warn("解析令牌失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * AES-GCM加密
     */
    private String encrypt(String plaintext) throws Exception {
        SecretKey secretKey = getEncryptionKey();
        
        // 生成随机IV
        byte[] iv = new byte[GCM_IV_LENGTH];
        secureRandom.nextBytes(iv);

        Cipher cipher = Cipher.getInstance(AES_TRANSFORMATION);
        GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, gcmSpec);

        byte[] encryptedBytes = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));

        // 组合IV和加密数据
        byte[] result = new byte[iv.length + encryptedBytes.length];
        System.arraycopy(iv, 0, result, 0, iv.length);
        System.arraycopy(encryptedBytes, 0, result, iv.length, encryptedBytes.length);

        return Base64.getEncoder().encodeToString(result);
    }

    /**
     * AES-GCM解密
     */
    private String decrypt(String encryptedData) {
        try {
            SecretKey secretKey = getEncryptionKey();
            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedData);

            // 分离IV和加密数据
            byte[] iv = new byte[GCM_IV_LENGTH];
            byte[] ciphertext = new byte[encryptedBytes.length - GCM_IV_LENGTH];
            System.arraycopy(encryptedBytes, 0, iv, 0, GCM_IV_LENGTH);
            System.arraycopy(encryptedBytes, GCM_IV_LENGTH, ciphertext, 0, ciphertext.length);

            Cipher cipher = Cipher.getInstance(AES_TRANSFORMATION);
            GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, gcmSpec);

            byte[] decryptedBytes = cipher.doFinal(ciphertext);
            return new String(decryptedBytes, StandardCharsets.UTF_8);

        } catch (Exception e) {
            log.warn("解密失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 生成HMAC签名
     */
    private String generateHMAC(String data) throws Exception {
        SecretKey hmacKey = getHMACKey();
        Mac mac = Mac.getInstance(HMAC_ALGORITHM);
        mac.init(hmacKey);
        byte[] hmacBytes = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(hmacBytes);
    }

    /**
     * 获取加密密钥
     */
    private SecretKey getEncryptionKey() {
        if (encryptionKeyBase64 == null || encryptionKeyBase64.trim().isEmpty()) {
            throw new IllegalStateException("加密密钥未配置");
        }
        byte[] keyBytes = Base64.getDecoder().decode(encryptionKeyBase64);
        return new SecretKeySpec(keyBytes, AES_ALGORITHM);
    }

    /**
     * 获取HMAC密钥
     */
    private SecretKey getHMACKey() {
        if (hmacKeyBase64 == null || hmacKeyBase64.trim().isEmpty()) {
            throw new IllegalStateException("HMAC密钥未配置");
        }
        byte[] keyBytes = Base64.getDecoder().decode(hmacKeyBase64);
        return new SecretKeySpec(keyBytes, HMAC_ALGORITHM);
    }

    /**
     * 常量时间字符串比较，防止时序攻击
     */
    private boolean constantTimeEquals(String a, String b) {
        if (a.length() != b.length()) {
            return false;
        }
        int result = 0;
        for (int i = 0; i < a.length(); i++) {
            result |= a.charAt(i) ^ b.charAt(i);
        }
        return result == 0;
    }

    /**
     * 生成AES-256密钥（用于初始化配置）
     */
    public static String generateAESKey() throws NoSuchAlgorithmException {
        KeyGenerator keyGenerator = KeyGenerator.getInstance(AES_ALGORITHM);
        keyGenerator.init(256);
        SecretKey secretKey = keyGenerator.generateKey();
        return Base64.getEncoder().encodeToString(secretKey.getEncoded());
    }

    /**
     * 生成HMAC-SHA256密钥（用于初始化配置）
     */
    public static String generateHMACKey() throws NoSuchAlgorithmException {
        KeyGenerator keyGenerator = KeyGenerator.getInstance(HMAC_ALGORITHM);
        keyGenerator.init(256);
        SecretKey secretKey = keyGenerator.generateKey();
        return Base64.getEncoder().encodeToString(secretKey.getEncoded());
    }
}
