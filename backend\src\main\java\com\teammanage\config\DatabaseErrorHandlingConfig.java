package com.teammanage.config;

import com.teammanage.exception.DatabaseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.QueryTimeoutException;
import org.springframework.jdbc.CannotGetJdbcConnectionException;
import org.springframework.transaction.CannotCreateTransactionException;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 数据库错误处理配置
 * 
 * 提供数据库连接监控、错误处理和恢复机制
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class DatabaseErrorHandlingConfig {

    private static final Logger log = LoggerFactory.getLogger(DatabaseErrorHandlingConfig.class);

    /**
     * 数据库异常处理器
     * 
     * 将常见的数据库异常转换为业务异常
     */
    @Bean
    public DatabaseExceptionHandler databaseExceptionHandler() {
        return new DatabaseExceptionHandler();
    }

    /**
     * 数据库异常处理器实现
     */
    public static class DatabaseExceptionHandler {

        private static final Logger log = LoggerFactory.getLogger(DatabaseExceptionHandler.class);

        /**
         * 处理数据库异常
         * 
         * @param e 数据库异常
         * @param operation 操作类型
         * @return 转换后的业务异常
         */
        public DatabaseException handleException(Exception e, String operation) {
            log.error("数据库操作异常: operation={}", operation, e);

            if (e instanceof CannotGetJdbcConnectionException) {
                return DatabaseException.connectionFailed(e);
            }

            if (e instanceof CannotCreateTransactionException) {
                return DatabaseException.connectionFailed(e);
            }

            if (e instanceof QueryTimeoutException) {
                return DatabaseException.timeout(operation, e);
            }

            if (e instanceof DataIntegrityViolationException) {
                DataIntegrityViolationException dive = (DataIntegrityViolationException) e;
                String constraintName = extractConstraintName(dive);
                return DatabaseException.constraintViolation(constraintName, e);
            }

            if (e instanceof DataAccessException) {
                return new DatabaseException("数据库访问错误", e, operation, e.getMessage());
            }

            if (e instanceof SQLException) {
                SQLException sqlEx = (SQLException) e;
                return handleSqlException(sqlEx, operation);
            }

            // 其他未知数据库异常
            return new DatabaseException("数据库操作失败", e, operation, e.getMessage());
        }

        /**
         * 处理SQL异常
         * 
         * @param e SQL异常
         * @param operation 操作类型
         * @return 转换后的业务异常
         */
        private DatabaseException handleSqlException(SQLException e, String operation) {
            int errorCode = e.getErrorCode();
            String sqlState = e.getSQLState();

            log.error("SQL异常: errorCode={}, sqlState={}, operation={}", errorCode, sqlState, operation, e);

            // 连接相关错误
            if (sqlState != null && (sqlState.startsWith("08") || sqlState.startsWith("S1"))) {
                return DatabaseException.connectionFailed(e);
            }

            // 超时错误
            if (errorCode == 1205 || errorCode == 1213 || sqlState != null && sqlState.equals("40001")) {
                return DatabaseException.timeout(operation, e);
            }

            // 约束违反错误
            if (errorCode == 1062 || errorCode == 1452 || sqlState != null && sqlState.startsWith("23")) {
                String constraintName = extractConstraintNameFromSql(e);
                return DatabaseException.constraintViolation(constraintName, e);
            }

            // 其他SQL错误
            return new DatabaseException("SQL执行错误: " + e.getMessage(), e, operation, 
                    "ErrorCode: " + errorCode + ", SQLState: " + sqlState);
        }

        /**
         * 从数据完整性异常中提取约束名称
         * 
         * @param e 数据完整性异常
         * @return 约束名称
         */
        private String extractConstraintName(DataIntegrityViolationException e) {
            String message = e.getMessage();
            if (message != null) {
                // 尝试从错误消息中提取约束名称
                if (message.contains("Duplicate entry")) {
                    return "UNIQUE_CONSTRAINT";
                }
                if (message.contains("foreign key constraint")) {
                    return "FOREIGN_KEY_CONSTRAINT";
                }
                if (message.contains("cannot be null")) {
                    return "NOT_NULL_CONSTRAINT";
                }
            }
            return "UNKNOWN_CONSTRAINT";
        }

        /**
         * 从SQL异常中提取约束名称
         * 
         * @param e SQL异常
         * @return 约束名称
         */
        private String extractConstraintNameFromSql(SQLException e) {
            String message = e.getMessage();
            if (message != null) {
                // MySQL错误消息解析
                if (message.contains("Duplicate entry")) {
                    return "UNIQUE_CONSTRAINT";
                }
                if (message.contains("foreign key constraint")) {
                    return "FOREIGN_KEY_CONSTRAINT";
                }
                if (message.contains("cannot be null")) {
                    return "NOT_NULL_CONSTRAINT";
                }
            }
            return "SQL_CONSTRAINT_" + e.getErrorCode();
        }
    }

    /**
     * 数据库连接健康检查器
     */
    @Bean
    public DatabaseHealthChecker databaseHealthChecker(DataSource dataSource) {
        return new DatabaseHealthChecker(dataSource);
    }

    /**
     * 数据库健康检查器实现
     */
    public static class DatabaseHealthChecker {

        private static final Logger log = LoggerFactory.getLogger(DatabaseHealthChecker.class);
        private final DataSource dataSource;

        public DatabaseHealthChecker(DataSource dataSource) {
            this.dataSource = dataSource;
        }

        /**
         * 检查数据库连接健康状态
         * 
         * @return 是否健康
         */
        public boolean isHealthy() {
            try (Connection connection = dataSource.getConnection()) {
                return connection.isValid(5); // 5秒超时
            } catch (SQLException e) {
                log.error("数据库健康检查失败", e);
                return false;
            }
        }

        /**
         * 获取数据库连接池状态
         * 
         * @return 连接池状态信息
         */
        public String getConnectionPoolStatus() {
            try {
                // 这里可以根据实际使用的连接池（如HikariCP）获取详细状态
                return "Connection pool status: Active";
            } catch (Exception e) {
                log.error("获取连接池状态失败", e);
                return "Connection pool status: Unknown";
            }
        }
    }

    /**
     * 异步任务异常处理器
     */
    @Bean
    public AsyncUncaughtExceptionHandler asyncUncaughtExceptionHandler() {
        return (ex, method, params) -> {
            log.error("异步任务执行异常: method={}, params={}", method.getName(), params, ex);
            
            // 如果是数据库相关异常，进行特殊处理
            if (ex instanceof DataAccessException || ex instanceof SQLException) {
                log.error("异步任务中的数据库异常，可能需要重试或告警", ex);
            }
        };
    }
}
