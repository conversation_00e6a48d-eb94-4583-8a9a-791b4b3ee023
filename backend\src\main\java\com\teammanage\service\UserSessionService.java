package com.teammanage.service;

import java.time.LocalDateTime;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 用户会话服务
 * 使用Caffeine缓存存储会话信息
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class UserSessionService {

    private static final Logger log = LoggerFactory.getLogger(UserSessionService.class);

    @Autowired
    private CacheService cacheService;

    @Value("${app.session.max-concurrent-sessions:5}")
    private int maxConcurrentSessions;



    private static final String SESSION_PREFIX = "session:";
    private static final String USER_SESSIONS_PREFIX = "user_sessions:";

    /**
     * 创建会话
     *
     * @param sessionInfo 会话信息
     */
    public void createSession(com.teammanage.model.SessionInfo sessionInfo) {
        try {
            // 检查并清理超出限制的会话
            cleanupExcessiveSessions(sessionInfo.getAccountId());

            // 存储会话到缓存
            String sessionKey = SESSION_PREFIX + sessionInfo.getTokenHash();
            cacheService.set(sessionKey, sessionInfo);

            // 添加到用户会话集合
            String userSessionsKey = USER_SESSIONS_PREFIX + sessionInfo.getAccountId();
            cacheService.addToSet(userSessionsKey, sessionInfo.getTokenHash());

            log.info("创建用户会话: accountId={}, tokenHash={}",
                    sessionInfo.getAccountId(), sessionInfo.getTokenHash());
        } catch (Exception e) {
            log.error("创建会话失败", e);
            throw new RuntimeException("创建会话失败", e);
        }
    }

    /**
     * 更新会话
     *
     * @param sessionInfo 会话信息
     */
    public void updateSession(com.teammanage.model.SessionInfo sessionInfo) {
        try {
            String sessionKey = SESSION_PREFIX + sessionInfo.getTokenHash();
            cacheService.set(sessionKey, sessionInfo);
            log.debug("更新会话: tokenHash={}", sessionInfo.getTokenHash());
        } catch (Exception e) {
            log.error("更新会话失败", e);
        }
    }

    /**
     * 根据Token哈希查找会话
     *
     * @param tokenHash Token哈希
     * @return 会话信息
     */
    public com.teammanage.model.SessionInfo findByTokenHash(String tokenHash) {
        try {
            String sessionKey = SESSION_PREFIX + tokenHash;
            return cacheService.get(sessionKey, com.teammanage.model.SessionInfo.class);
        } catch (Exception e) {
            log.error("查找会话失败: tokenHash={}", tokenHash, e);
            return null;
        }
    }





    /**
     * 检查会话是否有效
     *
     * @param jti Token ID
     * @return 是否有效
     */
    public boolean isSessionValid(String jti) {
        try {
            // 在单令牌系统中，所有会话都通过tokenHash查找
            com.teammanage.model.SessionInfo session = findByTokenHash(jti);
            return session != null && Boolean.TRUE.equals(session.getIsActive());
        } catch (Exception e) {
            log.error("检查会话有效性失败: jti={}", jti, e);
            return false;
        }
    }



    /**
     * 更新会话活动时间
     *
     * @param jti Token ID
     */
    public void updateSessionActivity(String jti) {
        try {
            // 在单令牌系统中，直接通过JTI查找会话
            com.teammanage.model.SessionInfo session = findByTokenHash(jti);

            if (session != null) {
                session.setLastActivityTime(LocalDateTime.now());
                updateSession(session);
                log.debug("更新会话活动时间: jti={}", jti);
            }
        } catch (Exception e) {
            log.error("更新会话活动时间失败: jti={}", jti, e);
        }
    }

    /**
     * 使会话无效
     *
     * @param jti Token ID
     */
    public void invalidateSession(String jti) {
        try {
            // 在单令牌系统中，直接通过JTI查找会话
            com.teammanage.model.SessionInfo session = findByTokenHash(jti);

            if (session != null) {
                // 从缓存中删除会话
                String sessionKey = SESSION_PREFIX + session.getTokenHash();
                cacheService.delete(sessionKey);

                // 从用户会话集合中移除
                String userSessionsKey = USER_SESSIONS_PREFIX + session.getAccountId();
                cacheService.removeFromSet(userSessionsKey, session.getTokenHash());

                log.info("会话已失效: accountId={}, jti={}", session.getAccountId(), jti);
            }
        } catch (Exception e) {
            log.error("使会话无效失败: jti={}", jti, e);
        }
    }

    /**
     * 获取用户的活跃会话列表
     *
     * @param accountId 用户ID
     * @return 活跃会话列表
     */
    public java.util.List<com.teammanage.model.SessionInfo> getActiveSessionsByAccountId(Long accountId) {
        try {
            java.util.List<com.teammanage.model.SessionInfo> sessions = new java.util.ArrayList<>();
            String userSessionsKey = USER_SESSIONS_PREFIX + accountId;
            Set<Object> tokenHashes = cacheService.getSetMembers(userSessionsKey);

            if (tokenHashes != null) {
                for (Object tokenHashObj : tokenHashes) {
                    String tokenHash = tokenHashObj.toString();
                    com.teammanage.model.SessionInfo session = findByTokenHash(tokenHash);
                    if (session != null && Boolean.TRUE.equals(session.getIsActive())) {
                        sessions.add(session);
                    }
                }
            }

            // 按最后活动时间排序
            sessions.sort((s1, s2) -> s2.getLastActivityTime().compareTo(s1.getLastActivityTime()));
            return sessions;
        } catch (Exception e) {
            log.error("获取用户活跃会话失败: accountId={}", accountId, e);
            return new java.util.ArrayList<>();
        }
    }

    /**
     * 清理超出限制的会话
     *
     * @param accountId 用户ID
     */
    public void cleanupExcessiveSessions(Long accountId) {
        try {
            java.util.List<com.teammanage.model.SessionInfo> activeSessions = getActiveSessionsByAccountId(accountId);

            if (activeSessions.size() >= maxConcurrentSessions) {
                // 按最后活动时间排序，踢出最早的会话
                int sessionsToRemove = activeSessions.size() - maxConcurrentSessions + 1;
                for (int i = activeSessions.size() - sessionsToRemove; i < activeSessions.size(); i++) {
                    com.teammanage.model.SessionInfo oldSession = activeSessions.get(i);
                    invalidateSession(oldSession.getTokenHash());
                    log.info("踢出旧会话: accountId={}, tokenHash={}", accountId, oldSession.getTokenHash());
                }
            }
        } catch (Exception e) {
            log.error("清理超出限制的会话失败: accountId={}", accountId, e);
        }
    }

    /**
     * 定期清理过期会话
     * Caffeine会自动处理过期，这里主要是清理一些可能的残留数据
     */
    public void cleanupExpiredSessions() {
        try {
            // Caffeine会自动清理过期的key，这里可以做一些额外的清理工作
            log.debug("执行会话清理检查");
        } catch (Exception e) {
            log.error("清理过期会话失败", e);
        }
    }

    /**
     * 统计用户活跃会话数量
     *
     * @param accountId 用户ID
     * @return 会话数量
     */
    public int countActiveSessionsByAccountId(Long accountId) {
        try {
            String userSessionsKey = USER_SESSIONS_PREFIX + accountId;
            return (int) cacheService.getSetSize(userSessionsKey);
        } catch (Exception e) {
            log.error("统计用户活跃会话数量失败: accountId={}", accountId, e);
            return 0;
        }
    }

}
