import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import PersonalInfo from '../PersonalInfo';
import { UserService } from '@/services/user';

// Mock UserService
jest.mock('@/services/user', () => ({
  UserService: {
    getUserProfile: jest.fn(),
    getUserPersonalStats: jest.fn(),
  },
}));

// Mock CSS modules
jest.mock('../PersonalInfo.module.css', () => ({
  personalInfoCard: 'personalInfoCard',
  decorativeCircle1: 'decorativeCircle1',
  decorativeCircle2: 'decorativeCircle2',
  contentArea: 'contentArea',
  loadingContainer: 'loadingContainer',
  titleBar: 'titleBar',
  title: 'title',
  settingsButton: 'settingsButton',
  avatarContainer: 'avatarContainer',
  avatar: 'avatar',
  onlineIndicator: 'onlineIndicator',
  contactCard: 'contactCard',
  emailCard: 'emailCard',
  phoneCard: 'phoneCard',
  additionalInfo: 'additionalInfo',
  statsCard: 'statsCard',
  statsTitle: 'statsTitle',
  vehicleCard: 'vehicleCard',
  personnelCard: 'personnelCard',
  warningCard: 'warningCard',
  alertCard: 'alertCard',
  fadeInDelay1: 'fadeInDelay1',
  fadeInDelay2: 'fadeInDelay2',
  fadeInDelay3: 'fadeInDelay3',
  fadeInDelay4: 'fadeInDelay4',
}));

const mockUserProfile = {
  id: 1,
  name: '张三',
  email: '<EMAIL>',
  telephone: '13800138000',
  avatar: null,
  department: '技术部',
  position: '前端工程师',
  joinDate: '2023-01-01',
};

const mockPersonalStats = {
  vehicles: 5,
  personnel: 12,
  warnings: 3,
  alerts: 1,
};

describe('PersonalInfo Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (UserService.getUserProfile as jest.Mock).mockResolvedValue({
      data: mockUserProfile,
      success: true,
    });
    (UserService.getUserPersonalStats as jest.Mock).mockResolvedValue({
      data: mockPersonalStats,
      success: true,
    });
  });

  test('renders personal info card with modern design', async () => {
    render(<PersonalInfo />);
    
    // Check if the main card is rendered
    expect(screen.getByText('个人信息')).toBeInTheDocument();
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('张三')).toBeInTheDocument();
    });
    
    // Check if contact information is displayed
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('13800138000')).toBeInTheDocument();
  });

  test('displays statistics cards with correct data', async () => {
    render(<PersonalInfo />);
    
    await waitFor(() => {
      expect(screen.getByText('5')).toBeInTheDocument(); // vehicles
      expect(screen.getByText('12')).toBeInTheDocument(); // personnel
      expect(screen.getByText('3')).toBeInTheDocument(); // warnings
      expect(screen.getByText('1')).toBeInTheDocument(); // alerts
    });
    
    // Check if labels are displayed
    expect(screen.getByText('车辆')).toBeInTheDocument();
    expect(screen.getByText('人员')).toBeInTheDocument();
    expect(screen.getByText('预警')).toBeInTheDocument();
    expect(screen.getByText('告警')).toBeInTheDocument();
  });

  test('opens settings modal when settings button is clicked', async () => {
    render(<PersonalInfo />);
    
    await waitFor(() => {
      expect(screen.getByText('张三')).toBeInTheDocument();
    });
    
    // Find and click the settings button
    const settingsButton = screen.getByRole('button');
    fireEvent.click(settingsButton);
    
    // Note: The modal content would need to be tested separately
    // as it's a separate component
  });

  test('handles loading state correctly', () => {
    // Mock loading state
    (UserService.getUserProfile as jest.Mock).mockImplementation(
      () => new Promise(() => {}) // Never resolves
    );
    
    render(<PersonalInfo />);
    
    // Check if loading spinner is displayed
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  test('handles error state correctly', async () => {
    // Mock error response
    (UserService.getUserProfile as jest.Mock).mockRejectedValue(
      new Error('Failed to fetch user profile')
    );
    
    render(<PersonalInfo />);
    
    await waitFor(() => {
      expect(screen.getByText(/获取用户信息失败/)).toBeInTheDocument();
    });
  });

  test('displays avatar with first letter of name', async () => {
    render(<PersonalInfo />);
    
    await waitFor(() => {
      expect(screen.getByText('张')).toBeInTheDocument();
    });
  });

  test('applies correct CSS classes for modern design', async () => {
    const { container } = render(<PersonalInfo />);
    
    await waitFor(() => {
      expect(screen.getByText('张三')).toBeInTheDocument();
    });
    
    // Check if modern design classes are applied
    expect(container.querySelector('.personalInfoCard')).toBeInTheDocument();
    expect(container.querySelector('.contentArea')).toBeInTheDocument();
    expect(container.querySelector('.titleBar')).toBeInTheDocument();
  });

  test('statistics cards have fade-in animation classes', async () => {
    const { container } = render(<PersonalInfo />);
    
    await waitFor(() => {
      expect(screen.getByText('5')).toBeInTheDocument();
    });
    
    // Check if animation classes are applied
    expect(container.querySelector('.fadeInDelay1')).toBeInTheDocument();
    expect(container.querySelector('.fadeInDelay2')).toBeInTheDocument();
    expect(container.querySelector('.fadeInDelay3')).toBeInTheDocument();
    expect(container.querySelector('.fadeInDelay4')).toBeInTheDocument();
  });
});
