package com.teammanage.service;

import com.github.benmanes.caffeine.cache.Cache;
import com.teammanage.exception.RateLimitException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * 速率限制服务
 * 
 * 提供API调用频率控制、防止恶意请求和系统过载
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class RateLimitService {

    private static final Logger log = LoggerFactory.getLogger(RateLimitService.class);

    @Autowired
    @Qualifier("dataCache")
    private Cache<String, Object> cache;

    // 配置参数
    @Value("${app.rate-limit.api-calls-per-minute:60}")
    private int apiCallsPerMinute;

    @Value("${app.rate-limit.invitations-per-hour:10}")
    private int invitationsPerHour;

    @Value("${app.rate-limit.team-creation-per-day:5}")
    private int teamCreationPerDay;

    @Value("${app.rate-limit.login-attempts-per-hour:10}")
    private int loginAttemptsPerHour;

    /**
     * 速率限制记录
     */
    private static class RateLimitRecord {
        private int count;
        private LocalDateTime windowStart;
        private LocalDateTime lastRequest;

        public RateLimitRecord() {
            this.count = 0;
            this.windowStart = LocalDateTime.now();
            this.lastRequest = LocalDateTime.now();
        }

        public int getCount() { return count; }
        public void setCount(int count) { this.count = count; }
        public LocalDateTime getWindowStart() { return windowStart; }
        public void setWindowStart(LocalDateTime windowStart) { this.windowStart = windowStart; }
        public LocalDateTime getLastRequest() { return lastRequest; }
        public void setLastRequest(LocalDateTime lastRequest) { this.lastRequest = lastRequest; }
    }

    /**
     * 检查API调用频率限制
     * 
     * @param userId 用户ID
     * @param endpoint 端点名称
     * @throws RateLimitException 如果超过频率限制
     */
    public void checkApiCallLimit(Long userId, String endpoint) {
        String key = "api_call:" + userId + ":" + endpoint;
        checkRateLimit(key, apiCallsPerMinute, 1, ChronoUnit.MINUTES, "API_CALL");
    }

    /**
     * 检查邀请发送频率限制
     * 
     * @param userId 用户ID
     * @param invitationCount 邀请数量
     * @throws RateLimitException 如果超过频率限制
     */
    public void checkInvitationLimit(Long userId, int invitationCount) {
        String key = "invitation:" + userId;
        checkRateLimit(key, invitationsPerHour, invitationCount, ChronoUnit.HOURS, "INVITATION");
    }

    /**
     * 检查团队创建频率限制
     * 
     * @param userId 用户ID
     * @throws RateLimitException 如果超过频率限制
     */
    public void checkTeamCreationLimit(Long userId) {
        String key = "team_creation:" + userId;
        checkRateLimit(key, teamCreationPerDay, 1, ChronoUnit.DAYS, "TEAM_CREATION");
    }

    /**
     * 检查登录尝试频率限制
     * 
     * @param identifier 标识符（用户名、邮箱或IP）
     * @throws RateLimitException 如果超过频率限制
     */
    public void checkLoginAttemptLimit(String identifier) {
        String key = "login_attempt:" + identifier;
        checkRateLimit(key, loginAttemptsPerHour, 1, ChronoUnit.HOURS, "LOGIN_ATTEMPT");
    }

    /**
     * 通用速率限制检查
     * 
     * @param key 缓存键
     * @param maxCount 最大计数
     * @param increment 增量
     * @param timeUnit 时间单位
     * @param limitType 限制类型
     * @throws RateLimitException 如果超过频率限制
     */
    private void checkRateLimit(String key, int maxCount, int increment, 
                               ChronoUnit timeUnit, String limitType) {
        LocalDateTime now = LocalDateTime.now();
        
        // 获取或创建速率限制记录
        RateLimitRecord record = (RateLimitRecord) cache.getIfPresent(key);
        if (record == null) {
            record = new RateLimitRecord();
        }

        // 检查时间窗口是否需要重置
        long timeDiff = timeUnit.between(record.getWindowStart(), now);
        if (timeDiff >= 1) {
            // 重置时间窗口
            record.setCount(0);
            record.setWindowStart(now);
        }

        // 检查是否超过限制
        if (record.getCount() + increment > maxCount) {
            long retryAfter = getRetryAfterSeconds(record.getWindowStart(), timeUnit);
            
            log.warn("速率限制触发: key={}, limitType={}, currentCount={}, maxCount={}, retryAfter={}",
                    key, limitType, record.getCount(), maxCount, retryAfter);
            
            throw createRateLimitException(limitType, retryAfter, record.getCount(), maxCount);
        }

        // 更新记录
        record.setCount(record.getCount() + increment);
        record.setLastRequest(now);
        
        // 保存到缓存
        cache.put(key, record);
        
        log.debug("速率限制检查通过: key={}, limitType={}, currentCount={}, maxCount={}",
                key, limitType, record.getCount(), maxCount);
    }

    /**
     * 计算重试等待时间
     * 
     * @param windowStart 时间窗口开始时间
     * @param timeUnit 时间单位
     * @return 重试等待时间（秒）
     */
    private long getRetryAfterSeconds(LocalDateTime windowStart, ChronoUnit timeUnit) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime nextWindow = windowStart.plus(1, timeUnit);
        return ChronoUnit.SECONDS.between(now, nextWindow);
    }

    /**
     * 创建速率限制异常
     * 
     * @param limitType 限制类型
     * @param retryAfter 重试等待时间
     * @param currentCount 当前计数
     * @param maxCount 最大计数
     * @return 速率限制异常
     */
    private RateLimitException createRateLimitException(String limitType, long retryAfter, 
                                                       int currentCount, int maxCount) {
        switch (limitType) {
            case "API_CALL":
                return RateLimitException.apiCallLimit(retryAfter, currentCount, maxCount);
            case "INVITATION":
                return RateLimitException.invitationLimit(retryAfter);
            case "TEAM_CREATION":
                return RateLimitException.teamCreationLimit(retryAfter);
            case "LOGIN_ATTEMPT":
                return RateLimitException.loginAttemptLimit(retryAfter, currentCount, maxCount);
            default:
                return new RateLimitException(
                    String.format("请求过于频繁，请%d秒后重试", retryAfter),
                    limitType,
                    retryAfter,
                    currentCount,
                    maxCount
                );
        }
    }

    /**
     * 重置用户的速率限制记录
     *
     * @param userId 用户ID
     * @param limitType 限制类型
     */
    public void resetRateLimit(Long userId, String limitType) {
        // 根据限制类型构建正确的缓存键
        String key;
        switch (limitType.toUpperCase()) {
            case "API_CALL":
                // API调用限制需要指定端点，这里重置所有相关的键
                // 由于Caffeine不支持模式匹配，我们需要重置常见的端点
                cache.invalidate("api_call:" + userId + ":CREATE_TEAM");
                cache.invalidate("api_call:" + userId + ":SEND_INVITATION");
                cache.invalidate("api_call:" + userId + ":TEST_ENDPOINT");
                break;
            case "INVITATION":
                key = "invitation:" + userId;
                cache.invalidate(key);
                break;
            case "TEAM_CREATION":
                key = "team_creation:" + userId;
                cache.invalidate(key);
                break;
            case "LOGIN_ATTEMPT":
                key = "login_attempt:" + userId;
                cache.invalidate(key);
                break;
            default:
                key = limitType.toLowerCase() + ":" + userId;
                cache.invalidate(key);
                break;
        }
        log.info("重置速率限制: userId={}, limitType={}", userId, limitType);
    }

    /**
     * 获取用户当前的速率限制状态
     *
     * @param userId 用户ID
     * @param limitType 限制类型
     * @return 速率限制状态信息
     */
    public Map<String, Object> getRateLimitStatus(Long userId, String limitType) {
        String key = limitType.toLowerCase() + ":" + userId;
        RateLimitRecord record = (RateLimitRecord) cache.getIfPresent(key);

        Map<String, Object> status = new ConcurrentHashMap<>();
        if (record != null) {
            status.put("currentCount", record.getCount());
            status.put("windowStart", record.getWindowStart());
            status.put("lastRequest", record.getLastRequest());
        } else {
            status.put("currentCount", 0);
            // 使用字符串而不是null来避免ConcurrentHashMap的NullPointerException
            status.put("windowStart", "N/A");
            status.put("lastRequest", "N/A");
        }

        // 添加限制配置信息
        switch (limitType.toUpperCase()) {
            case "API_CALL":
                status.put("maxCount", apiCallsPerMinute);
                status.put("timeWindow", "1 minute");
                break;
            case "INVITATION":
                status.put("maxCount", invitationsPerHour);
                status.put("timeWindow", "1 hour");
                break;
            case "TEAM_CREATION":
                status.put("maxCount", teamCreationPerDay);
                status.put("timeWindow", "1 day");
                break;
            case "LOGIN_ATTEMPT":
                status.put("maxCount", loginAttemptsPerHour);
                status.put("timeWindow", "1 hour");
                break;
            default:
                status.put("maxCount", "Unknown");
                status.put("timeWindow", "Unknown");
                break;
        }

        return status;
    }
}
