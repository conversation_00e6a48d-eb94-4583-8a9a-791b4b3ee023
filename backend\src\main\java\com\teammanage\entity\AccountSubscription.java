package com.teammanage.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.util.Objects;

/**
 * 用户订阅记录实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

@TableName("account_subscription")
public class AccountSubscription extends BaseEntity {

    /**
     * 用户ID
     */
    private Long accountId;

    /**
     * 套餐ID
     */
    private Long subscriptionPlanId;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 订阅状态
     */
    private SubscriptionStatus status;

    /**
     * 订阅状态枚举
     */
    public enum SubscriptionStatus {
        ACTIVE,   // 激活
        EXPIRED,  // 过期
        CANCELED  // 取消
    }

    // Getter and Setter methods
    public Long getAccountId() { return accountId; }
    public void setAccountId(Long accountId) { this.accountId = accountId; }

    public Long getSubscriptionPlanId() { return subscriptionPlanId; }
    public void setSubscriptionPlanId(Long subscriptionPlanId) { this.subscriptionPlanId = subscriptionPlanId; }

    public LocalDate getStartDate() { return startDate; }
    public void setStartDate(LocalDate startDate) { this.startDate = startDate; }

    public LocalDate getEndDate() { return endDate; }
    public void setEndDate(LocalDate endDate) { this.endDate = endDate; }

    public SubscriptionStatus getStatus() { return status; }
    public void setStatus(SubscriptionStatus status) { this.status = status; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        AccountSubscription that = (AccountSubscription) o;
        return Objects.equals(accountId, that.accountId) &&
               Objects.equals(subscriptionPlanId, that.subscriptionPlanId) &&
               Objects.equals(startDate, that.startDate) &&
               Objects.equals(endDate, that.endDate) &&
               status == that.status;
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), accountId, subscriptionPlanId, startDate, endDate, status);
    }

}
