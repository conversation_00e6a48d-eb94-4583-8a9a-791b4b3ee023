# Token管理流程文档

## 概述

JWT Token是团队管理应用身份验证系统的核心，负责用户身份验证和团队上下文管理。本文档详细描述了Token的生命周期管理、存储策略和安全机制。

## 1. Token设计架构

### 1.1 单Token设计原则

**设计理念**: 使用一个JWT Token同时承载用户身份和团队上下文信息

**优势**:
- 简化Token管理逻辑
- 减少客户端存储复杂度
- 提高API调用效率
- 降低状态同步复杂度

**Token类型**:
- **用户Token**: 只包含用户信息，用于团队选择前
- **团队Token**: 包含用户和团队信息，用于团队操作

### 1.2 Token结构设计

```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "sub": "123",           // 用户ID
    "teamId": "456",        // 团队ID (可选)
    "role": "TEAM_CREATOR", // 用户在团队中的角色 (可选)
    "iat": 1627834567,      // 签发时间
    "exp": 1627838167,      // 过期时间
    "jti": "uuid-string"    // Token唯一标识
  },
  "signature": "..."
}
```

## 2. Token生命周期管理

### 2.1 Token生成流程

```mermaid
sequenceDiagram
    participant C as 客户端
    participant S as 服务端
    participant J as JWT工具
    participant D as 数据库

    C->>S: 认证请求
    S->>D: 验证用户信息
    D->>S: 返回用户数据
    S->>J: 生成Token请求
    J->>J: 创建JWT Payload
    J->>J: 签名Token
    J->>S: 返回签名Token
    S->>C: 返回Token
```

**生成步骤**:
1. **用户认证**: 验证用户身份（验证码、密码等）
2. **信息收集**: 收集用户和团队信息
3. **Payload构建**: 构建JWT Payload对象
4. **Token签名**: 使用密钥对Token进行签名
5. **Token返回**: 将Token返回给客户端

**关键代码位置**:
- 后端: `JwtTokenUtil.generateUserToken()`
- 后端: `JwtTokenUtil.generateTeamToken()`

### 2.2 Token存储管理

```mermaid
graph TD
    A[Token接收] --> B[TokenManager.setToken]
    B --> C[localStorage存储]
    C --> D[内存缓存]
    D --> E[请求拦截器注入]
    E --> F[API调用]
    F --> G{Token有效?}
    G -->|有效| H[正常响应]
    G -->|无效| I[401错误]
    I --> J[自动刷新Token]
    J --> K[重试请求]
```

**存储策略**:
- **主存储**: localStorage持久化存储
- **缓存**: 内存中缓存Token避免重复读取
- **自动清理**: 登出时自动清除所有Token数据
- **安全性**: 仅在HTTPS环境下使用localStorage

**关键代码位置**:
- 前端: `frontend/src/utils/request.ts` - `TokenManager`类

### 2.3 Token刷新机制

```mermaid
sequenceDiagram
    participant F as 前端
    participant I as 拦截器
    participant B as 后端

    F->>B: API请求 (Token即将过期)
    B->>F: 401 Unauthorized
    I->>I: 检测401错误
    I->>B: POST /auth/refresh
    B->>B: 验证当前Token
    B->>B: 生成新Token
    B->>I: 返回新Token
    I->>I: 更新localStorage
    I->>B: 重试原始请求
    B->>F: 返回原始数据
```

**刷新策略**:
- **被动刷新**: 收到401错误时自动刷新
- **预刷新**: Token即将过期时主动刷新
- **重试机制**: 刷新成功后自动重试原始请求
- **失败处理**: 刷新失败时跳转到登录页

**关键代码位置**:
- 前端: `frontend/src/utils/request.ts` - 响应拦截器
- 前端: `frontend/src/services/auth.ts` - `refreshToken()`

## 3. Token解析和使用

### 3.1 Token解析工具

```typescript
// 位置: frontend/src/utils/tokenUtils.ts

/**
 * JWT Payload解析
 */
export const parseJwtPayload = (token: string): any => {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) return null;
    
    // Base64URL解码
    const payload = parts[1];
    const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'));
    return JSON.parse(decoded);
  } catch {
    return null;
  }
};

/**
 * 提取团队ID
 */
export const getTeamIdFromCurrentToken = (): number | null => {
  const token = TokenManager.getToken();
  if (!token) return null;
  
  const payload = parseJwtPayload(token);
  return payload?.teamId ? parseInt(payload.teamId, 10) : null;
};
```

### 3.2 Token信息提取

| 函数名 | 功能 | 返回值 |
|--------|------|--------|
| `parseJwtPayload()` | 解析JWT Payload | Payload对象或null |
| `getTeamIdFromCurrentToken()` | 获取团队ID | number或null |
| `getUserIdFromCurrentToken()` | 获取用户ID | number或null |
| `hasTeamInCurrentToken()` | 检查是否包含团队信息 | boolean |
| `getIsCreatorFromCurrentToken()` | 检查是否为创建者 | boolean |
| `getCurrentTokenInfo()` | 获取完整Token信息 | TokenInfo对象 |

## 4. Token安全机制

### 4.1 安全特性

**传输安全**:
- 所有Token传输使用HTTPS加密
- 请求头中使用Authorization Bearer格式
- 避免在URL参数中传递Token

**存储安全**:
- 使用localStorage而非sessionStorage（支持多标签页）
- Token过期后自动清理
- 登出时立即清除所有Token数据

**验证安全**:
- 每个API请求都验证Token有效性
- Token签名验证防止篡改
- 服务端维护Token黑名单

### 4.2 Token过期处理

```mermaid
flowchart TD
    A[API请求] --> B{Token存在?}
    B -->|否| C[跳转登录页]
    B -->|是| D[发送请求]
    D --> E{响应状态}
    E -->|200| F[正常处理]
    E -->|401| G[Token过期]
    G --> H[尝试刷新Token]
    H --> I{刷新成功?}
    I -->|是| J[重试原始请求]
    I -->|否| K[跳转登录页]
    J --> F
```

**过期处理策略**:
1. **自动检测**: 响应拦截器检测401错误
2. **自动刷新**: 尝试使用refresh token获取新Token
3. **重试请求**: 刷新成功后重试原始请求
4. **失败处理**: 刷新失败时清除状态并跳转登录

## 5. Token最佳实践

### 5.1 开发最佳实践

**Token获取**:
```typescript
// ✅ 推荐：使用TokenManager
const token = TokenManager.getToken();

// ❌ 不推荐：直接访问localStorage
const token = localStorage.getItem('token');
```

**Token信息提取**:
```typescript
// ✅ 推荐：使用工具函数
const teamId = getTeamIdFromCurrentToken();
const userId = getUserIdFromCurrentToken();

// ❌ 不推荐：手动解析
const payload = JSON.parse(atob(token.split('.')[1]));
```

**权限检查**:
```typescript
// ✅ 推荐：使用权限系统
const { canManageTeam } = useAccess();

// ❌ 不推荐：直接检查Token
const isCreator = getIsCreatorFromCurrentToken();
```

### 5.2 错误处理最佳实践

**API调用错误处理**:
```typescript
try {
  const response = await AuthService.selectTeam({ teamId });
  // 处理成功响应
} catch (error) {
  // 响应拦截器已处理大部分错误
  // 只处理特定的业务错误
  if (error.code === 'NETWORK_ERROR') {
    message.error('网络连接失败，请检查网络');
  }
}
```

**Token状态检查**:
```typescript
// 在组件中检查Token状态
useEffect(() => {
  const token = TokenManager.getToken();
  if (!token) {
    // 处理无Token状态
    history.push('/user/login');
  }
}, []);
```

## 6. 监控和调试

### 6.1 Token状态监控

```typescript
// 开发环境下的Token状态监控
if (process.env.NODE_ENV === 'development') {
  // 监控Token变化
  const originalSetToken = TokenManager.setToken;
  TokenManager.setToken = (token: string) => {
    console.log('Token更新:', getCurrentTokenInfo());
    return originalSetToken(token);
  };
}
```

### 6.2 调试工具

**Token信息查看**:
```javascript
// 在浏览器控制台中执行
console.log('当前Token信息:', getCurrentTokenInfo());
console.log('团队ID:', getTeamIdFromCurrentToken());
console.log('用户ID:', getUserIdFromCurrentToken());
```

**状态检查**:
```javascript
// 检查全局状态
console.log('全局状态:', window.g_app._store.getState());
```

---

*最后更新时间: 2025-07-31*
