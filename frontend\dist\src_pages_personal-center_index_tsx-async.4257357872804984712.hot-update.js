globalThis.makoModuleHotUpdate('src/pages/personal-center/index.tsx', {
    modules: {
        "src/pages/personal-center/PersonalInfo.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _user = __mako_require__("src/services/user.ts");
            var _UnifiedSettingsModal = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/UnifiedSettingsModal.tsx"));
            var _PersonalInfomodulecssasmodule = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/PersonalInfo.module.css?asmodule"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text } = _antd.Typography;
            /**
 * 个人信息组件
 *
 * 显示用户的基本个人信息，采用简洁的卡片设计。
 * 用户名支持悬浮显示详细信息（电话、邮箱、注册时间等）。
 *
 * 主要功能：
 * 1. 显示用户头像（使用姓名首字母）
 * 2. 显示用户姓名（支持气泡卡片显示详细信息）
 * 3. 显示最后登录时间和登录团队
 * 4. 提供设置入口
 *
 * 数据来源：
 * - 用户详细信息：通过UserService.getUserProfileDetail()获取
 */ const PersonalInfo = ()=>{
                var _userInfo_name;
                _s();
                /**
   * 用户详细信息状态管理
   */ const [userInfo, setUserInfo] = (0, _react.useState)({
                    name: '',
                    position: '',
                    email: '',
                    telephone: '',
                    registerDate: '',
                    lastLoginTime: '',
                    lastLoginTeam: '',
                    teamCount: 0,
                    avatar: ''
                });
                const [userInfoLoading, setUserInfoLoading] = (0, _react.useState)(true);
                const [userInfoError, setUserInfoError] = (0, _react.useState)(null);
                // Modal状态管理
                const [settingsModalVisible, setSettingsModalVisible] = (0, _react.useState)(false);
                // 获取用户数据
                (0, _react.useEffect)(()=>{
                    const fetchUserData = async ()=>{
                        try {
                            const userDetail = await _user.UserService.getUserProfileDetail();
                            setUserInfo(userDetail);
                            setUserInfoError(null);
                        } catch (error) {
                            console.error('获取用户详细信息失败:', error);
                            setUserInfoError('获取用户详细信息失败，请稍后重试');
                        } finally{
                            setUserInfoLoading(false);
                        }
                    };
                    fetchUserData();
                }, []);
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                    title: "个人信息",
                    extra: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "text",
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {}, void 0, false, {
                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                            lineNumber: 88,
                            columnNumber: 17
                        }, void 0),
                        onClick: ()=>setSettingsModalVisible(true)
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                        lineNumber: 86,
                        columnNumber: 9
                    }, void 0),
                    children: [
                        userInfoError ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                            message: "个人信息加载失败",
                            description: userInfoError,
                            type: "error",
                            showIcon: true,
                            style: {
                                borderRadius: 12,
                                border: 'none'
                            }
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                            lineNumber: 94,
                            columnNumber: 11
                        }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                            spinning: userInfoLoading || statsLoading,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Row, {
                                gutter: [
                                    24,
                                    16
                                ],
                                align: "top",
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Col, {
                                        xs: 24,
                                        sm: 24,
                                        md: 14,
                                        lg: 14,
                                        xl: 14,
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    display: 'flex',
                                                    alignItems: 'flex-start',
                                                    gap: 20,
                                                    marginBottom: 12
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        className: _PersonalInfomodulecssasmodule.default.avatarContainer,
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                                                size: 80,
                                                                className: _PersonalInfomodulecssasmodule.default.avatar,
                                                                icon: !userInfo.name && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 123,
                                                                    columnNumber: 47
                                                                }, void 0),
                                                                children: (_userInfo_name = userInfo.name) === null || _userInfo_name === void 0 ? void 0 : _userInfo_name.charAt(0).toUpperCase()
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 120,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                className: _PersonalInfomodulecssasmodule.default.onlineIndicator
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 128,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                        lineNumber: 119,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            flex: 1,
                                                            minWidth: 0
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Title, {
                                                                level: 3,
                                                                style: {
                                                                    margin: '0 0 8px 0',
                                                                    fontSize: 24,
                                                                    fontWeight: 600,
                                                                    color: '#262626',
                                                                    lineHeight: 1.2
                                                                },
                                                                children: userInfo.name || '加载中...'
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 133,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                direction: "vertical",
                                                                size: 8,
                                                                children: [
                                                                    userInfo.email && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                        className: `${_PersonalInfomodulecssasmodule.default.contactCard} ${_PersonalInfomodulecssasmodule.default.emailCard}`,
                                                                        children: [
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(MailOutlined, {
                                                                                style: {
                                                                                    fontSize: 16,
                                                                                    color: '#1890ff'
                                                                                }
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                lineNumber: 150,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                                                style: {
                                                                                    color: '#595959',
                                                                                    fontSize: 14,
                                                                                    fontWeight: 500
                                                                                },
                                                                                children: userInfo.email
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                lineNumber: 156,
                                                                                columnNumber: 27
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 149,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    userInfo.telephone && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                        className: `${_PersonalInfomodulecssasmodule.default.contactCard} ${_PersonalInfomodulecssasmodule.default.phoneCard}`,
                                                                        children: [
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(PhoneOutlined, {
                                                                                style: {
                                                                                    fontSize: 16,
                                                                                    color: '#52c41a'
                                                                                }
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                lineNumber: 169,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                                                style: {
                                                                                    color: '#595959',
                                                                                    fontSize: 14,
                                                                                    fontWeight: 500
                                                                                },
                                                                                children: userInfo.telephone
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                lineNumber: 175,
                                                                                columnNumber: 27
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 168,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 147,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                        lineNumber: 132,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                lineNumber: 110,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                className: _PersonalInfomodulecssasmodule.default.additionalInfo,
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                    direction: "vertical",
                                                    size: 6,
                                                    style: {
                                                        width: '100%'
                                                    },
                                                    children: [
                                                        userInfo.registerDate && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                display: 'flex',
                                                                alignItems: 'center',
                                                                gap: 8
                                                            },
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                                style: {
                                                                    fontSize: 13,
                                                                    color: '#8c8c8c',
                                                                    fontWeight: 500
                                                                },
                                                                children: [
                                                                    "📅 注册于 ",
                                                                    userInfo.registerDate
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 195,
                                                                columnNumber: 25
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 194,
                                                            columnNumber: 23
                                                        }, this),
                                                        userInfo.lastLoginTime && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                display: 'flex',
                                                                alignItems: 'center',
                                                                gap: 8
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {
                                                                    style: {
                                                                        fontSize: 13,
                                                                        color: '#1890ff'
                                                                    }
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 208,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                                    style: {
                                                                        fontSize: 13,
                                                                        color: '#8c8c8c',
                                                                        fontWeight: 500
                                                                    },
                                                                    children: [
                                                                        "最后登录：",
                                                                        userInfo.lastLoginTime
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 214,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 207,
                                                            columnNumber: 23
                                                        }, this),
                                                        userInfo.lastLoginTeam && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                display: 'flex',
                                                                alignItems: 'center',
                                                                gap: 8
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                                                    style: {
                                                                        fontSize: 13,
                                                                        color: '#52c41a'
                                                                    }
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 227,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                                    style: {
                                                                        fontSize: 13,
                                                                        color: '#8c8c8c',
                                                                        fontWeight: 500
                                                                    },
                                                                    children: [
                                                                        "团队：",
                                                                        userInfo.lastLoginTeam
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 233,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 226,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                    lineNumber: 192,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                lineNumber: 191,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                        lineNumber: 109,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Col, {
                                        xs: 24,
                                        sm: 24,
                                        md: 10,
                                        lg: 10,
                                        xl: 10,
                                        children: statsError ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                                            message: "数据概览加载失败",
                                            description: statsError,
                                            type: "error",
                                            showIcon: true,
                                            style: {
                                                borderRadius: 12,
                                                border: 'none'
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                            lineNumber: 251,
                                            columnNumber: 19
                                        }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Title, {
                                                    level: 5,
                                                    className: _PersonalInfomodulecssasmodule.default.statsTitle,
                                                    children: "数据概览"
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                    lineNumber: 263,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Row, {
                                                    gutter: [
                                                        8,
                                                        8
                                                    ],
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Col, {
                                                            xs: 12,
                                                            sm: 12,
                                                            md: 12,
                                                            lg: 12,
                                                            xl: 12,
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Card, {
                                                                size: "small",
                                                                className: `${_PersonalInfomodulecssasmodule.default.statsCard} ${_PersonalInfomodulecssasmodule.default.vehicleCard} ${_PersonalInfomodulecssasmodule.default.fadeInDelay1}`,
                                                                styles: {
                                                                    body: {
                                                                        padding: '16px 12px',
                                                                        textAlign: 'center'
                                                                    }
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                        style: {
                                                                            marginBottom: 8
                                                                        },
                                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(CarOutlined, {
                                                                            style: {
                                                                                fontSize: 20,
                                                                                color: '#1890ff',
                                                                                marginBottom: 4
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                            lineNumber: 280,
                                                                            columnNumber: 29
                                                                        }, this)
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 279,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                        style: {
                                                                            fontSize: 24,
                                                                            fontWeight: 700,
                                                                            color: '#1890ff',
                                                                            lineHeight: 1,
                                                                            marginBottom: 4
                                                                        },
                                                                        children: personalStats.vehicles
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 288,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                        style: {
                                                                            fontSize: 12,
                                                                            color: '#1890ff',
                                                                            fontWeight: 600,
                                                                            opacity: 0.8
                                                                        },
                                                                        children: "车辆"
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 299,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 269,
                                                                columnNumber: 25
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 268,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Col, {
                                                            xs: 12,
                                                            sm: 12,
                                                            md: 12,
                                                            lg: 12,
                                                            xl: 12,
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Card, {
                                                                size: "small",
                                                                className: `${_PersonalInfomodulecssasmodule.default.statsCard} ${_PersonalInfomodulecssasmodule.default.personnelCard} ${_PersonalInfomodulecssasmodule.default.fadeInDelay2}`,
                                                                styles: {
                                                                    body: {
                                                                        padding: '16px 12px',
                                                                        textAlign: 'center'
                                                                    }
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                        style: {
                                                                            marginBottom: 8
                                                                        },
                                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(UsergroupAddOutlined, {
                                                                            style: {
                                                                                fontSize: 20,
                                                                                color: '#52c41a',
                                                                                marginBottom: 4
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                            lineNumber: 325,
                                                                            columnNumber: 29
                                                                        }, this)
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 324,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                        style: {
                                                                            fontSize: 24,
                                                                            fontWeight: 700,
                                                                            color: '#52c41a',
                                                                            lineHeight: 1,
                                                                            marginBottom: 4
                                                                        },
                                                                        children: personalStats.personnel
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 333,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                        style: {
                                                                            fontSize: 12,
                                                                            color: '#52c41a',
                                                                            fontWeight: 600,
                                                                            opacity: 0.8
                                                                        },
                                                                        children: "人员"
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 344,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 314,
                                                                columnNumber: 25
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 313,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Col, {
                                                            xs: 12,
                                                            sm: 12,
                                                            md: 12,
                                                            lg: 12,
                                                            xl: 12,
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Card, {
                                                                size: "small",
                                                                className: `${_PersonalInfomodulecssasmodule.default.statsCard} ${_PersonalInfomodulecssasmodule.default.warningCard} ${_PersonalInfomodulecssasmodule.default.fadeInDelay3}`,
                                                                styles: {
                                                                    body: {
                                                                        padding: '16px 12px',
                                                                        textAlign: 'center'
                                                                    }
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                        style: {
                                                                            marginBottom: 8
                                                                        },
                                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(ExclamationCircleOutlined, {
                                                                            style: {
                                                                                fontSize: 20,
                                                                                color: '#faad14',
                                                                                marginBottom: 4
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                            lineNumber: 370,
                                                                            columnNumber: 29
                                                                        }, this)
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 369,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                        style: {
                                                                            fontSize: 24,
                                                                            fontWeight: 700,
                                                                            color: '#faad14',
                                                                            lineHeight: 1,
                                                                            marginBottom: 4
                                                                        },
                                                                        children: personalStats.warnings
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 378,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                        style: {
                                                                            fontSize: 12,
                                                                            color: '#faad14',
                                                                            fontWeight: 600,
                                                                            opacity: 0.8
                                                                        },
                                                                        children: "预警"
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 389,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 359,
                                                                columnNumber: 25
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 358,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Col, {
                                                            xs: 12,
                                                            sm: 12,
                                                            md: 12,
                                                            lg: 12,
                                                            xl: 12,
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Card, {
                                                                size: "small",
                                                                className: `${_PersonalInfomodulecssasmodule.default.statsCard} ${_PersonalInfomodulecssasmodule.default.alertCard} ${_PersonalInfomodulecssasmodule.default.fadeInDelay4}`,
                                                                styles: {
                                                                    body: {
                                                                        padding: '16px 12px',
                                                                        textAlign: 'center'
                                                                    }
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                        style: {
                                                                            marginBottom: 8
                                                                        },
                                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(AlertOutlined, {
                                                                            style: {
                                                                                fontSize: 20,
                                                                                color: '#ff4d4f',
                                                                                marginBottom: 4
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                            lineNumber: 415,
                                                                            columnNumber: 29
                                                                        }, this)
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 414,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                        style: {
                                                                            fontSize: 24,
                                                                            fontWeight: 700,
                                                                            color: '#ff4d4f',
                                                                            lineHeight: 1,
                                                                            marginBottom: 4
                                                                        },
                                                                        children: personalStats.alerts
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 423,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                        style: {
                                                                            fontSize: 12,
                                                                            color: '#ff4d4f',
                                                                            fontWeight: 600,
                                                                            opacity: 0.8
                                                                        },
                                                                        children: "告警"
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 434,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 404,
                                                                columnNumber: 25
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 403,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                    lineNumber: 266,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                            lineNumber: 262,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                        lineNumber: 249,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                lineNumber: 107,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                            lineNumber: 105,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_UnifiedSettingsModal.default, {
                            visible: settingsModalVisible,
                            onCancel: ()=>setSettingsModalVisible(false),
                            userInfo: userInfo,
                            onSuccess: ()=>{
                                // 可以在这里刷新用户信息或团队列表
                                console.log('设置操作成功');
                            }
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                            lineNumber: 455,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                    lineNumber: 82,
                    columnNumber: 5
                }, this);
            };
            _s(PersonalInfo, "dXhNNnAJBAZ3COqFbPW6jEaOYJI=");
            _c = PersonalInfo;
            var _default = PersonalInfo;
            var _c;
            $RefreshReg$(_c, "PersonalInfo");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '10086463622502801591';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/settings/index.tsx": [
            "p__settings__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_personal-center_index_tsx-async.4257357872804984712.hot-update.js.map