package com.teammanage.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.Cache;
import com.teammanage.service.CacheService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * Caffeine缓存服务实现
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class CaffeineCacheService implements CacheService {

    private static final Logger log = LoggerFactory.getLogger(CaffeineCacheService.class);

    @Autowired
    @Qualifier("sessionCache")
    private Cache<String, Object> sessionCache;

    @Autowired
    @Qualifier("userSessionsCache")
    private Cache<String, Object> userSessionsCache;

    @Autowired
    @Qualifier("dataCache")
    private Cache<String, Object> dataCache;

    @Autowired
    private ObjectMapper objectMapper;

    // 用于存储集合数据的内部映射
    private final ConcurrentMap<String, Set<Object>> setStorage = new ConcurrentHashMap<>();

    @Override
    public void set(String key, Object value) {
        try {
            getAppropriateCache(key).put(key, value);
            log.debug("缓存存储成功: key={}", key);
        } catch (Exception e) {
            log.error("缓存存储失败: key={}", key, e);
        }
    }

    @Override
    public void set(String key, Object value, Duration duration) {
        // Caffeine 不支持单独设置过期时间，使用默认配置
        // 如果需要不同的过期时间，需要使用不同的缓存实例
        set(key, value);
    }

    @Override
    public Object get(String key) {
        try {
            Object value = getAppropriateCache(key).getIfPresent(key);
            log.debug("缓存获取: key={}, found={}", key, value != null);
            return value;
        } catch (Exception e) {
            log.error("缓存获取失败: key={}", key, e);
            return null;
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> T get(String key, Class<T> clazz) {
        try {
            Object value = get(key);
            if (value == null) {
                return null;
            }
            
            if (clazz.isInstance(value)) {
                return (T) value;
            }
            
            // 尝试使用 ObjectMapper 进行类型转换
            return objectMapper.convertValue(value, clazz);
        } catch (Exception e) {
            log.error("缓存类型转换失败: key={}, targetType={}", key, clazz.getSimpleName(), e);
            return null;
        }
    }

    @Override
    public void delete(String key) {
        try {
            getAppropriateCache(key).invalidate(key);
            // 同时清理集合存储
            setStorage.remove(key);
            log.debug("缓存删除成功: key={}", key);
        } catch (Exception e) {
            log.error("缓存删除失败: key={}", key, e);
        }
    }

    @Override
    public boolean exists(String key) {
        try {
            return getAppropriateCache(key).getIfPresent(key) != null;
        } catch (Exception e) {
            log.error("缓存存在性检查失败: key={}", key, e);
            return false;
        }
    }

    @Override
    public void addToSet(String key, Object value) {
        try {
            setStorage.computeIfAbsent(key, k -> ConcurrentHashMap.newKeySet()).add(value);
            // 同时在缓存中存储集合的引用
            set(key, setStorage.get(key));
            log.debug("集合添加元素成功: key={}, value={}", key, value);
        } catch (Exception e) {
            log.error("集合添加元素失败: key={}, value={}", key, value, e);
        }
    }

    @Override
    public void removeFromSet(String key, Object value) {
        try {
            Set<Object> set = setStorage.get(key);
            if (set != null) {
                set.remove(value);
                if (set.isEmpty()) {
                    setStorage.remove(key);
                    delete(key);
                } else {
                    set(key, set);
                }
            }
            log.debug("集合移除元素成功: key={}, value={}", key, value);
        } catch (Exception e) {
            log.error("集合移除元素失败: key={}, value={}", key, value, e);
        }
    }

    @Override
    public Set<Object> getSetMembers(String key) {
        try {
            Set<Object> set = setStorage.get(key);
            return set != null ? new HashSet<>(set) : new HashSet<>();
        } catch (Exception e) {
            log.error("获取集合成员失败: key={}", key, e);
            return new HashSet<>();
        }
    }

    @Override
    public long getSetSize(String key) {
        try {
            Set<Object> set = setStorage.get(key);
            return set != null ? set.size() : 0;
        } catch (Exception e) {
            log.error("获取集合大小失败: key={}", key, e);
            return 0;
        }
    }

    @Override
    public Set<String> keys(String pattern) {
        try {
            Set<String> allKeys = new HashSet<>();
            
            // 从所有缓存实例中获取键
            allKeys.addAll(sessionCache.asMap().keySet());
            allKeys.addAll(userSessionsCache.asMap().keySet());
            allKeys.addAll(dataCache.asMap().keySet());
            allKeys.addAll(setStorage.keySet());
            
            // 简单的模式匹配（支持 * 通配符）
            if (pattern.equals("*")) {
                return allKeys;
            }
            
            String regex = pattern.replace("*", ".*");
            return allKeys.stream()
                    .filter(key -> key.matches(regex))
                    .collect(Collectors.toSet());
        } catch (Exception e) {
            log.error("模式匹配键失败: pattern={}", pattern, e);
            return new HashSet<>();
        }
    }

    @Override
    public void clear() {
        try {
            sessionCache.invalidateAll();
            userSessionsCache.invalidateAll();
            dataCache.invalidateAll();
            setStorage.clear();
            log.info("所有缓存已清空");
        } catch (Exception e) {
            log.error("清空缓存失败", e);
        }
    }

    @Override
    public String getStats() {
        try {
            StringBuilder stats = new StringBuilder();
            stats.append("Session Cache: ").append(sessionCache.stats()).append("\n");
            stats.append("User Sessions Cache: ").append(userSessionsCache.stats()).append("\n");
            stats.append("Data Cache: ").append(dataCache.stats()).append("\n");
            stats.append("Set Storage Size: ").append(setStorage.size());
            return stats.toString();
        } catch (Exception e) {
            log.error("获取缓存统计失败", e);
            return "统计信息获取失败";
        }
    }

    /**
     * 根据键的前缀选择合适的缓存实例
     */
    private Cache<String, Object> getAppropriateCache(String key) {
        if (key.startsWith("session:")) {
            return sessionCache;
        } else if (key.startsWith("user_sessions:")) {
            return userSessionsCache;
        } else {
            return dataCache;
        }
    }
}
