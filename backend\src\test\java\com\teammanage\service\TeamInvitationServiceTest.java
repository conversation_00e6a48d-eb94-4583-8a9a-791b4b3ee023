package com.teammanage.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 团队邀请服务测试类
 */
@SpringBootTest
@ActiveProfiles("test")
public class TeamInvitationServiceTest {

    @Test
    public void testInvitationTokenGeneration() throws Exception {
        TeamInvitationService service = new TeamInvitationService();
        
        // 使用反射测试私有方法
        Method generateTokenMethod = TeamInvitationService.class.getDeclaredMethod("generateInvitationToken", Long.class);
        generateTokenMethod.setAccessible(true);
        
        Method parseTokenMethod = TeamInvitationService.class.getDeclaredMethod("parseInvitationToken", String.class);
        parseTokenMethod.setAccessible(true);
        
        // 测试邀请ID
        Long testInvitationId = 12345L;
        
        // 生成token
        String token = (String) generateTokenMethod.invoke(service, testInvitationId);
        
        // 验证token不为空且不包含特殊字符
        assertNotNull(token);
        assertFalse(token.isEmpty());
        assertFalse(token.contains("+"));  // URL安全的Base64不应包含+
        assertFalse(token.contains("/"));  // URL安全的Base64不应包含/
        assertFalse(token.contains("="));  // 无填充的Base64不应包含=
        
        System.out.println("Generated token: " + token);
        
        // 解析token
        Long parsedId = (Long) parseTokenMethod.invoke(service, token);
        
        // 验证解析结果
        assertNotNull(parsedId);
        assertEquals(testInvitationId, parsedId);
        
        System.out.println("Original ID: " + testInvitationId);
        System.out.println("Parsed ID: " + parsedId);
    }
    
    @Test
    public void testInvalidTokenParsing() throws Exception {
        TeamInvitationService service = new TeamInvitationService();
        
        Method parseTokenMethod = TeamInvitationService.class.getDeclaredMethod("parseInvitationToken", String.class);
        parseTokenMethod.setAccessible(true);
        
        // 测试无效token
        Long result1 = (Long) parseTokenMethod.invoke(service, "invalid-token");
        assertNull(result1);
        
        Long result2 = (Long) parseTokenMethod.invoke(service, "");
        assertNull(result2);
        
        Long result3 = (Long) parseTokenMethod.invoke(service, (String) null);
        assertNull(result3);
        
        System.out.println("Invalid token parsing tests passed");
    }
}
