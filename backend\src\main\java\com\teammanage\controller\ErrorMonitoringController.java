package com.teammanage.controller;

import com.teammanage.common.ApiResponse;
import com.teammanage.service.ErrorMonitoringService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 错误监控控制器
 * 
 * 提供错误统计和监控信息的API接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/error-monitoring")
@Tag(name = "错误监控", description = "错误统计和监控相关接口")
public class ErrorMonitoringController {

    @Autowired
    private ErrorMonitoringService errorMonitoringService;

    /**
     * 获取错误统计信息
     * 
     * @return 错误统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取错误统计信息", description = "获取系统错误统计和健康状态信息")
    public ApiResponse<Map<String, Object>> getErrorStatistics() {
        try {
            Map<String, Object> statistics = errorMonitoringService.getErrorStatistics();
            return ApiResponse.success(statistics);
        } catch (Exception e) {
            return ApiResponse.error("获取错误统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取最近的错误记录
     * 
     * @param limit 限制数量，默认50
     * @return 最近的错误记录
     */
    @GetMapping("/recent-errors")
    @Operation(summary = "获取最近的错误记录", description = "获取最近发生的错误记录详情")
    public ApiResponse<Map<String, ErrorMonitoringService.ErrorRecord>> getRecentErrors(
            @RequestParam(defaultValue = "50") int limit) {
        try {
            Map<String, ErrorMonitoringService.ErrorRecord> recentErrors = 
                    errorMonitoringService.getRecentErrors(limit);
            return ApiResponse.success(recentErrors);
        } catch (Exception e) {
            return ApiResponse.error("获取最近错误记录失败: " + e.getMessage());
        }
    }

    /**
     * 清除错误统计
     * 
     * @return 操作结果
     */
    @PostMapping("/clear-statistics")
    @Operation(summary = "清除错误统计", description = "清除所有错误统计数据")
    public ApiResponse<String> clearErrorStatistics() {
        try {
            errorMonitoringService.clearErrorStatistics();
            return ApiResponse.success("错误统计已清除");
        } catch (Exception e) {
            return ApiResponse.error("清除错误统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统健康状态
     * 
     * @return 系统健康状态
     */
    @GetMapping("/health-status")
    @Operation(summary = "获取系统健康状态", description = "获取基于错误统计的系统健康状态评估")
    public ApiResponse<Map<String, Object>> getHealthStatus() {
        try {
            Map<String, Object> statistics = errorMonitoringService.getErrorStatistics();
            
            Map<String, Object> healthInfo = Map.of(
                "healthStatus", statistics.get("healthStatus"),
                "totalErrors", statistics.get("totalErrors"),
                "timestamp", System.currentTimeMillis()
            );
            
            return ApiResponse.success(healthInfo);
        } catch (Exception e) {
            return ApiResponse.error("获取健康状态失败: " + e.getMessage());
        }
    }
}
