package com.teammanage.mapper;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teammanage.entity.TeamMember;

/**
 * 团队成员Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface TeamMemberMapper extends BaseMapper<TeamMember> {

    /**
     * 根据用户ID查询团队成员信息
     * 
     * @param accountId 用户ID
     * @return 团队成员列表
     */
    @Select("SELECT * FROM team_member WHERE account_id = #{accountId} AND is_deleted = 0")
    List<TeamMember> findByAccountId(@Param("accountId") Long accountId);

    /**
     * 根据团队ID查询团队成员信息
     * 
     * @param teamId 团队ID
     * @return 团队成员列表
     */
    @Select("SELECT * FROM team_member WHERE team_id = #{teamId} AND is_active = 1 AND is_deleted = 0")
    List<TeamMember> findByTeamId(@Param("teamId") Long teamId);

    /**
     * 根据团队ID和用户ID查询团队成员信息
     * 
     * @param teamId 团队ID
     * @param accountId 用户ID
     * @return 团队成员信息
     */
    @Select("SELECT * FROM team_member WHERE team_id = #{teamId} AND account_id = #{accountId} AND is_deleted = 0")
    TeamMember findByTeamIdAndAccountId(@Param("teamId") Long teamId, @Param("accountId") Long accountId);

    /**
     * 更新最后访问时间
     * 
     * @param teamId 团队ID
     * @param accountId 用户ID
     * @param lastAccessTime 最后访问时间
     */
    @Update("UPDATE team_member SET last_access_time = #{lastAccessTime} WHERE team_id = #{teamId} AND account_id = #{accountId}")
    void updateLastAccessTime(@Param("teamId") Long teamId, @Param("accountId") Long accountId, @Param("lastAccessTime") LocalDateTime lastAccessTime);

    /**
     * 统计团队成员数量
     * 
     * @param teamId 团队ID
     * @return 成员数量
     */
    @Select("SELECT COUNT(1) FROM team_member WHERE team_id = #{teamId} AND is_active = 1 AND is_deleted = 0")
    int countByTeamId(@Param("teamId") Long teamId);

}
