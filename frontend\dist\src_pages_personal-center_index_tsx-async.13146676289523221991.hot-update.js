globalThis.makoModuleHotUpdate('src/pages/personal-center/index.tsx', {
    modules: {
        "src/pages/personal-center/PersonalInfo.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _user = __mako_require__("src/services/user.ts");
            var _UnifiedSettingsModal = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/UnifiedSettingsModal.tsx"));
            var _UserInfoPopover = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/UserInfoPopover.tsx"));
            var _PersonalInfomodulecssasmodule = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/PersonalInfo.module.css?asmodule"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text } = _antd.Typography;
            /**
 * 个人信息组件
 *
 * 显示用户的基本个人信息，采用简洁的卡片设计。
 * 用户名支持悬浮显示详细信息（电话、邮箱、注册时间等）。
 *
 * 主要功能：
 * 1. 显示用户头像（使用姓名首字母）
 * 2. 显示用户姓名（支持气泡卡片显示详细信息）
 * 3. 显示最后登录时间和登录团队
 * 4. 提供设置入口
 *
 * 数据来源：
 * - 用户详细信息：通过UserService.getUserProfileDetail()获取
 */ const PersonalInfo = ()=>{
                _s();
                /**
   * 用户详细信息状态管理
   */ const [userInfo, setUserInfo] = (0, _react.useState)({
                    name: '',
                    position: '',
                    email: '',
                    telephone: '',
                    registerDate: '',
                    lastLoginTime: '',
                    lastLoginTeam: '',
                    teamCount: 0,
                    avatar: ''
                });
                const [userInfoLoading, setUserInfoLoading] = (0, _react.useState)(true);
                const [userInfoError, setUserInfoError] = (0, _react.useState)(null);
                // Modal状态管理
                const [settingsModalVisible, setSettingsModalVisible] = (0, _react.useState)(false);
                // 获取用户数据
                (0, _react.useEffect)(()=>{
                    const fetchUserData = async ()=>{
                        try {
                            const userDetail = await _user.UserService.getUserProfileDetail();
                            setUserInfo(userDetail);
                            setUserInfoError(null);
                        } catch (error) {
                            console.error('获取用户详细信息失败:', error);
                            setUserInfoError('获取用户详细信息失败，请稍后重试');
                        } finally{
                            setUserInfoLoading(false);
                        }
                    };
                    fetchUserData();
                }, []);
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                    title: "个人信息",
                    extra: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "text",
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {}, void 0, false, {
                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                            lineNumber: 85,
                            columnNumber: 17
                        }, void 0),
                        onClick: ()=>setSettingsModalVisible(true)
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                        lineNumber: 83,
                        columnNumber: 9
                    }, void 0),
                    style: {
                        marginBottom: 16,
                        borderRadius: 8,
                        border: '1px solid #d9d9d9'
                    },
                    children: [
                        userInfoError ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                            message: "个人信息加载失败",
                            description: userInfoError,
                            type: "error",
                            showIcon: true,
                            style: {
                                borderRadius: 12,
                                border: 'none'
                            }
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                            lineNumber: 96,
                            columnNumber: 9
                        }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                            spinning: userInfoLoading,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                className: _PersonalInfomodulecssasmodule.default.personalInfoContent,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        className: _PersonalInfomodulecssasmodule.default.userBasicInfo,
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_UserInfoPopover.default, {
                                                userInfo: userInfo,
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Title, {
                                                    level: 3,
                                                    className: _PersonalInfomodulecssasmodule.default.userName,
                                                    children: userInfo.name || '加载中...'
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                    lineNumber: 113,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                lineNumber: 112,
                                                columnNumber: 15
                                            }, this),
                                            userInfo.position && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                type: "secondary",
                                                style: {
                                                    fontSize: 14,
                                                    marginBottom: 12,
                                                    display: 'block'
                                                },
                                                children: userInfo.position
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                lineNumber: 123,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                        lineNumber: 111,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        className: _PersonalInfomodulecssasmodule.default.loginInfoSection,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            direction: "vertical",
                                            size: 8,
                                            style: {
                                                width: '100%'
                                            },
                                            children: [
                                                userInfo.lastLoginTime && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    className: _PersonalInfomodulecssasmodule.default.loginInfoItem,
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {
                                                            style: {
                                                                fontSize: 14,
                                                                color: '#1890ff'
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 141,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                            style: {
                                                                fontSize: 13,
                                                                color: '#8c8c8c',
                                                                fontWeight: 500
                                                            },
                                                            children: [
                                                                "最后登录：",
                                                                userInfo.lastLoginTime
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 147,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                    lineNumber: 140,
                                                    columnNumber: 19
                                                }, this),
                                                userInfo.lastLoginTeam && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    className: _PersonalInfomodulecssasmodule.default.loginInfoItem,
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                                            style: {
                                                                fontSize: 14,
                                                                color: '#52c41a'
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 160,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                            style: {
                                                                fontSize: 13,
                                                                color: '#8c8c8c',
                                                                fontWeight: 500
                                                            },
                                                            children: [
                                                                "团队：",
                                                                userInfo.lastLoginTeam
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 166,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                    lineNumber: 159,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                            lineNumber: 138,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                        lineNumber: 137,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                lineNumber: 109,
                                columnNumber: 11
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                            lineNumber: 107,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_UnifiedSettingsModal.default, {
                            visible: settingsModalVisible,
                            onCancel: ()=>setSettingsModalVisible(false),
                            userInfo: userInfo,
                            onSuccess: ()=>{
                                // 可以在这里刷新用户信息
                                console.log('设置操作成功');
                            }
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                            lineNumber: 184,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                    lineNumber: 80,
                    columnNumber: 5
                }, this);
            };
            _s(PersonalInfo, "dXhNNnAJBAZ3COqFbPW6jEaOYJI=");
            _c = PersonalInfo;
            var _default = PersonalInfo;
            var _c;
            $RefreshReg$(_c, "PersonalInfo");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '4476642497419208368';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/settings/index.tsx": [
            "p__settings__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_personal-center_index_tsx-async.13146676289523221991.hot-update.js.map