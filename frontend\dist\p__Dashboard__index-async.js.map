{"version": 3, "sources": ["src/pages/Dashboard/index.tsx"], "sourcesContent": ["/**\n * 仪表板页面\n */\n\nimport { PageContainer } from '@ant-design/pro-components';\nimport React from 'react';\n\nconst Dashboard: React.FC = () => {\n  return (\n    <PageContainer title=\"仪表板\">\n      <div style={{\n        height: '400px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        color: '#999',\n        fontSize: '16px'\n      }}>\n        {/* 空白页面 */}\n      </div>\n    </PageContainer>\n  );\n};\n\nexport default Dashboard;\n"], "names": [], "mappings": ";;;AAAA;;CAEC;;;;4BAsBD;;;eAAA;;;;;;;sCApB8B;uEACZ;;;;;;;;;AAElB,MAAM,YAAsB;IAC1B,qBACE,2BAAC,4BAAa;QAAC,OAAM;kBACnB,cAAA,2BAAC;YAAI,OAAO;gBACV,QAAQ;gBACR,SAAS;gBACT,YAAY;gBACZ,gBAAgB;gBAChB,OAAO;gBACP,UAAU;YACZ;;;;;;;;;;;AAKN;KAfM;IAiBN,WAAe"}