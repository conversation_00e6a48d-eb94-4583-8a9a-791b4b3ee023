package com.teammanage.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TokenCryptoService 测试类
 */
public class TokenCryptoServiceTest {

    private TokenCryptoService tokenCryptoService;

    @BeforeEach
    void setUp() {
        tokenCryptoService = new TokenCryptoService();
        
        // 设置测试密钥
        ReflectionTestUtils.setField(tokenCryptoService, "encryptionKeyBase64", 
            "mi9diBMqX4757+rSoa3UVt5rfRzhqmRrnINlkvdFbPg=");
        ReflectionTestUtils.setField(tokenCryptoService, "hmacKeyBase64", 
            "6gy5Gbd8Orp0sD7+z6oxi3RdpyJz7jEQrNH0d1CTbUw=");
    }

    @Test
    void testGenerateAndParseToken() {
        // 测试数据
        Long invitationId = 12345L;
        LocalDateTime expiresAt = LocalDateTime.now().plusHours(24);

        // 生成令牌
        String token = tokenCryptoService.generateToken(invitationId, expiresAt);
        assertNotNull(token);
        assertFalse(token.isEmpty());
        
        // 验证令牌不包含明文信息
        assertFalse(token.contains("12345"));
        assertFalse(token.contains("inv_"));
        
        System.out.println("生成的安全令牌: " + token);

        // 解析令牌
        TokenCryptoService.TokenData tokenData = tokenCryptoService.parseToken(token);
        assertNotNull(tokenData);
        assertEquals(invitationId, tokenData.getInvitationId());
        
        // 验证过期时间（允许1秒误差）
        assertTrue(Math.abs(java.time.Duration.between(expiresAt, tokenData.getExpiresAt()).getSeconds()) <= 1);
        
        // 验证盐值存在
        assertNotNull(tokenData.getSalt());
        assertFalse(tokenData.getSalt().isEmpty());
    }

    @Test
    void testTokenUniqueness() {
        // 相同的邀请ID应该生成不同的令牌（因为有随机盐值）
        Long invitationId = 12345L;
        LocalDateTime expiresAt = LocalDateTime.now().plusHours(24);

        String token1 = tokenCryptoService.generateToken(invitationId, expiresAt);
        String token2 = tokenCryptoService.generateToken(invitationId, expiresAt);

        assertNotEquals(token1, token2);
        System.out.println("令牌1: " + token1);
        System.out.println("令牌2: " + token2);

        // 但两个令牌都应该能正确解析出相同的邀请ID
        TokenCryptoService.TokenData data1 = tokenCryptoService.parseToken(token1);
        TokenCryptoService.TokenData data2 = tokenCryptoService.parseToken(token2);

        assertEquals(invitationId, data1.getInvitationId());
        assertEquals(invitationId, data2.getInvitationId());
    }

    @Test
    void testExpiredToken() {
        // 生成已过期的令牌
        Long invitationId = 12345L;
        LocalDateTime expiresAt = LocalDateTime.now().minusHours(1); // 1小时前过期

        String token = tokenCryptoService.generateToken(invitationId, expiresAt);
        
        // 解析过期令牌应该返回null
        TokenCryptoService.TokenData tokenData = tokenCryptoService.parseToken(token);
        assertNull(tokenData);
    }

    @Test
    void testInvalidToken() {
        // 测试无效令牌
        assertNull(tokenCryptoService.parseToken(null));
        assertNull(tokenCryptoService.parseToken(""));
        assertNull(tokenCryptoService.parseToken("invalid-token"));
        assertNull(tokenCryptoService.parseToken("aW52YWxpZC10b2tlbg==")); // base64编码的"invalid-token"
    }

    @Test
    void testTamperedToken() {
        // 生成有效令牌
        Long invitationId = 12345L;
        LocalDateTime expiresAt = LocalDateTime.now().plusHours(24);
        String originalToken = tokenCryptoService.generateToken(invitationId, expiresAt);

        // 篡改令牌（修改最后一个字符）
        String tamperedToken = originalToken.substring(0, originalToken.length() - 1) + "X";

        // 篡改的令牌应该无法解析
        TokenCryptoService.TokenData tokenData = tokenCryptoService.parseToken(tamperedToken);
        assertNull(tokenData);
    }

    @Test
    void testTokenSecurity() {
        // 验证令牌的安全特性
        Long invitationId = 12345L;
        LocalDateTime expiresAt = LocalDateTime.now().plusHours(24);
        String token = tokenCryptoService.generateToken(invitationId, expiresAt);

        // 令牌应该是不透明的，无法从中推断出内部结构
        assertFalse(token.contains("12345"));
        assertFalse(token.contains("inv"));
        assertFalse(token.contains("_"));
        
        // 令牌长度应该合理（不会太短或太长）
        assertTrue(token.length() > 50);
        assertTrue(token.length() < 500);
        
        System.out.println("令牌长度: " + token.length());
        System.out.println("安全令牌示例: " + token);
    }
}
