<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} -- %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 主应用日志文件 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/team-manage.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/team-manage.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} -- %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 错误监控专用日志文件 -->
    <appender name="ERROR_MONITOR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/error-monitor.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/error-monitor.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>90</maxHistory>
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} -- %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
    </appender>

    <!-- 数据库错误专用日志文件 -->
    <appender name="DATABASE_ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/database-errors.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/database-errors.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>60</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} -- %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 网络错误专用日志文件 -->
    <appender name="NETWORK_ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/network-errors.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/network-errors.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>60</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} -- %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 速率限制日志文件 -->
    <appender name="RATE_LIMIT_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/rate-limit.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/rate-limit.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>500MB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} -- %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 错误监控服务专用Logger -->
    <logger name="ERROR_MONITOR" level="INFO" additivity="false">
        <appender-ref ref="ERROR_MONITOR_FILE" />
        <appender-ref ref="CONSOLE" />
    </logger>

    <!-- 数据库相关Logger -->
    <logger name="com.teammanage.exception.DatabaseException" level="ERROR" additivity="false">
        <appender-ref ref="DATABASE_ERROR_FILE" />
        <appender-ref ref="CONSOLE" />
    </logger>

    <!-- 网络相关Logger -->
    <logger name="com.teammanage.exception.NetworkException" level="ERROR" additivity="false">
        <appender-ref ref="NETWORK_ERROR_FILE" />
        <appender-ref ref="CONSOLE" />
    </logger>

    <!-- 速率限制Logger -->
    <logger name="com.teammanage.service.RateLimitService" level="WARN" additivity="false">
        <appender-ref ref="RATE_LIMIT_FILE" />
        <appender-ref ref="CONSOLE" />
    </logger>

    <!-- MyBatis日志 -->
    <logger name="com.teammanage.mapper" level="DEBUG" />

    <!-- 根日志级别 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </root>
</configuration>
