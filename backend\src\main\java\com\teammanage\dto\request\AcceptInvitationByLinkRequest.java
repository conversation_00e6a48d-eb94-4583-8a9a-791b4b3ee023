package com.teammanage.dto.request;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Size;

/**
 * 通过邀请链接接受邀请请求DTO
 * 用于新用户注册并接受邀请的场景
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class AcceptInvitationByLinkRequest {

    /**
     * 用户姓名（新用户注册时需要）
     */
    @Size(max = 100, message = "姓名长度不能超过100字符")
    private String name;

    /**
     * 用户邮箱（新用户注册时需要）
     */
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 用户密码（新用户注册时需要）
     */
    @Size(min = 8, max = 100, message = "密码长度必须在8-100字符之间")
    private String password;

    /**
     * 响应消息（可选）
     */
    @Size(max = 500, message = "响应消息长度不能超过500字符")
    private String message;

    // Getter and Setter methods
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }

    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }
}
