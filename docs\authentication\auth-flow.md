# 身份验证流程文档

## 概述

本文档详细描述了团队管理应用的身份验证流程，包括用户登录、团队选择、Token管理等核心流程。

## 1. 用户登录流程

### 1.1 验证码发送流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant E as 邮件服务

    U->>F: 输入邮箱地址
    F->>F: 邮箱格式验证
    F->>B: POST /auth/send-code
    B->>B: 生成6位验证码
    B->>E: 发送验证码邮件
    B->>F: 返回发送成功状态
    F->>U: 显示验证码发送成功
```

**详细步骤**:
1. 用户在登录页面输入邮箱地址
2. 前端进行邮箱格式验证
3. 调用`AuthService.sendVerificationCode()`发送请求
4. 后端生成6位随机数字验证码
5. 后端调用邮件服务发送验证码
6. 前端显示发送成功消息和倒计时

**关键代码位置**:
- 前端: `frontend/src/services/auth.ts` - `sendVerificationCode()`
- 后端: `AuthController.sendVerificationCode()`

### 1.2 用户登录流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant DB as 数据库

    U->>F: 输入邮箱和验证码
    F->>B: POST /auth/login
    B->>B: 验证验证码
    B->>DB: 查询用户信息
    alt 用户不存在
        B->>DB: 创建新用户
    end
    B->>B: 生成用户Token
    B->>F: 返回Token和用户信息
    F->>F: 存储Token到localStorage
    F->>F: 更新全局状态
    F->>U: 跳转到团队选择页面
```

**详细步骤**:
1. 用户输入邮箱和收到的验证码
2. 前端调用`AuthService.login()`提交登录请求
3. 后端验证验证码的有效性和正确性
4. 后端查询用户是否存在，不存在则自动创建
5. 后端生成包含用户信息的JWT Token
6. 前端接收Token并存储到localStorage
7. 前端更新全局用户状态
8. 根据用户团队情况跳转到相应页面

**关键代码位置**:
- 前端: `frontend/src/services/auth.ts` - `login()`
- 前端: `frontend/src/utils/request.ts` - `TokenManager`
- 后端: `AuthService.login()`

## 2. 团队选择流程

### 2.1 团队选择流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant DB as 数据库

    U->>F: 选择团队
    F->>B: POST /auth/select-team
    B->>B: 验证用户Token
    B->>DB: 验证团队成员关系
    B->>B: 生成团队Token
    B->>F: 返回团队Token和信息
    F->>F: 更新Token存储
    F->>F: 更新全局状态
    F->>U: 跳转到团队仪表盘
```

**详细步骤**:
1. 用户在团队列表中选择要进入的团队
2. 前端调用`AuthService.selectTeam()`发送团队选择请求
3. 后端验证当前用户Token的有效性
4. 后端验证用户是否为该团队的有效成员
5. 后端生成包含团队信息的新JWT Token
6. 前端更新localStorage中的Token
7. 前端更新全局状态中的团队信息
8. 跳转到团队仪表盘页面

**关键代码位置**:
- 前端: `frontend/src/pages/personal-center/TeamListCard.tsx` - `handleTeamSwitch()`
- 前端: `frontend/src/services/auth.ts` - `selectTeam()`
- 后端: `AuthService.selectTeam()`

### 2.2 团队切换流程

团队切换与团队选择使用相同的API和流程，区别在于：
- **团队选择**: 从无团队状态选择一个团队
- **团队切换**: 从一个团队切换到另一个团队

## 3. Token生命周期管理

### 3.1 Token刷新流程

```mermaid
sequenceDiagram
    participant F as 前端
    participant B as 后端
    participant I as 拦截器

    F->>B: API请求 (Token即将过期)
    B->>F: 401 Unauthorized
    I->>I: 检测到401错误
    I->>B: POST /auth/refresh
    B->>B: 验证刷新Token
    B->>I: 返回新Token
    I->>I: 更新localStorage
    I->>B: 重试原始请求
    B->>F: 返回原始请求结果
```

**详细步骤**:
1. 前端发送API请求，Token即将过期
2. 后端返回401 Unauthorized错误
3. 响应拦截器检测到401错误
4. 自动调用Token刷新接口
5. 后端验证并生成新Token
6. 拦截器更新本地Token存储
7. 自动重试原始请求
8. 返回原始请求的结果

**关键代码位置**:
- 前端: `frontend/src/utils/request.ts` - 响应拦截器
- 前端: `frontend/src/services/auth.ts` - `refreshToken()`
- 后端: `AuthService.refreshToken()`

### 3.2 Token清理流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端

    U->>F: 点击登出
    F->>B: POST /auth/logout
    B->>B: 标记Token为无效
    B->>F: 返回登出成功
    F->>F: 清除localStorage
    F->>F: 清除全局状态
    F->>U: 跳转到登录页面
```

**详细步骤**:
1. 用户点击登出按钮
2. 前端调用`AuthService.logout()`
3. 后端将当前Token标记为无效
4. 前端清除localStorage中的Token
5. 前端清除全局状态中的用户和团队信息
6. 跳转到登录页面

**关键代码位置**:
- 前端: `frontend/src/services/auth.ts` - `logout()`
- 前端: `frontend/src/utils/request.ts` - `TokenManager.clearToken()`
- 后端: `AuthService.logout()`

## 4. 状态同步机制

### 4.1 全局状态管理

```mermaid
graph TD
    A[Token更新] --> B[触发状态刷新]
    B --> C[获取用户信息]
    B --> D[获取团队信息]
    C --> E[更新全局状态]
    D --> E
    E --> F[通知组件更新]
    F --> G[UI重新渲染]
```

**状态同步策略**:
1. **Token驱动**: Token变化触发状态更新
2. **异步更新**: 状态更新不阻塞用户操作
3. **错误处理**: 状态更新失败不影响核心功能
4. **实时同步**: 状态变化实时反映到UI

### 4.2 组件状态管理

- **全局状态**: 用户信息、团队信息通过UmiJS initialState管理
- **本地状态**: 组件内部状态（加载状态、错误状态等）
- **Token状态**: 通过TokenManager统一管理
- **权限状态**: 通过access函数动态计算

## 5. 错误处理机制

### 5.1 认证错误处理

| 错误类型 | 处理方式 | 用户体验 |
|---------|---------|---------|
| 验证码错误 | 显示错误消息，允许重试 | 友好的错误提示 |
| Token过期 | 自动刷新Token | 无感知的自动处理 |
| 权限不足 | 跳转到403页面 | 清晰的权限说明 |
| 网络错误 | 显示网络错误提示 | 引导用户检查网络 |
| 服务器错误 | 显示通用错误消息 | 建议用户稍后重试 |

### 5.2 错误恢复机制

- **自动重试**: 网络错误自动重试
- **状态恢复**: 错误后恢复到安全状态
- **用户引导**: 提供明确的操作指导
- **日志记录**: 详细记录错误信息用于调试

## 6. 性能优化

### 6.1 Token管理优化
- **本地缓存**: Token存储在localStorage，减少网络请求
- **预刷新**: Token即将过期时提前刷新
- **批量更新**: 多个API调用共享Token更新

### 6.2 状态管理优化
- **懒加载**: 按需加载用户和团队信息
- **缓存策略**: 合理缓存用户状态
- **异步更新**: 状态更新不阻塞UI操作

---

*最后更新时间: 2025-07-31*
