package com.teammanage.task;

import com.teammanage.service.TeamInvitationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 邀请清理定时任务
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class InvitationCleanupTask {

    private static final Logger log = LoggerFactory.getLogger(InvitationCleanupTask.class);

    @Autowired
    private TeamInvitationService teamInvitationService;

    /**
     * 每小时执行一次，更新过期邀请状态
     */
    @Scheduled(fixedRate = 3600000) // 1小时 = 3600000毫秒
    public void updateExpiredInvitations() {
        try {
            int count = teamInvitationService.updateExpiredInvitations();
            if (count > 0) {
                log.info("定时任务：更新过期邀请状态完成，更新数量: {}", count);
            }
        } catch (Exception e) {
            log.error("定时任务：更新过期邀请状态失败", e);
        }
    }
}
