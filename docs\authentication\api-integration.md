# API集成文档

## 概述

本文档详细描述了团队管理应用前后端API的集成方式，包括认证API、请求/响应处理、错误处理等核心集成机制。

## 1. API架构设计

### 1.1 请求架构

```mermaid
graph TD
    A[前端组件] --> B[Service层]
    B --> C[Request工具]
    C --> D[请求拦截器]
    D --> E[Token注入]
    E --> F[HTTP请求]
    F --> G[后端API]
    G --> H[响应数据]
    H --> I[响应拦截器]
    I --> J[错误处理]
    J --> K[状态更新]
    K --> L[返回组件]
```

### 1.2 核心组件

**前端请求层次**:
- **组件层**: React组件调用Service
- **Service层**: 业务逻辑封装和API调用
- **Request层**: HTTP请求配置和拦截器
- **Utils层**: Token管理和解析工具

**后端响应层次**:
- **Controller层**: 处理HTTP请求和响应
- **Service层**: 业务逻辑实现
- **Repository层**: 数据访问和持久化
- **Utils层**: JWT工具和安全验证

## 2. 认证API集成

### 2.1 发送验证码API

**前端调用**:
```typescript
// 位置: frontend/src/services/auth.ts
static async sendVerificationCode(data: SendCodeRequest): Promise<SendCodeResponse> {
  const response = await apiRequest.post<SendCodeResponse>(
    '/auth/send-code',
    data,
  );
  return response.data;
}
```

**请求格式**:
```typescript
interface SendCodeRequest {
  email: string;
}
```

**响应格式**:
```typescript
interface SendCodeResponse {
  message: string;
  success: boolean;
}
```

**后端端点**: `POST /auth/send-code`

### 2.2 用户登录API

**前端调用**:
```typescript
static async login(data: LoginRequest): Promise<LoginResponse> {
  const response = await apiRequest.post<LoginResponse>('/auth/login', data);
  
  // 自动存储Token
  if (response.data.token) {
    TokenManager.setToken(response.data.token);
  }
  
  return response.data;
}
```

**请求格式**:
```typescript
interface LoginRequest {
  email: string;
  code: string;
}
```

**响应格式**:
```typescript
interface LoginResponse {
  token: string;
  user: UserProfileResponse;
  team?: TeamDetailResponse;
  teamSelectionSuccess?: boolean;
}
```

**后端端点**: `POST /auth/login`

### 2.3 团队选择API

**前端调用**:
```typescript
static async selectTeam(data: { teamId: number }): Promise<LoginResponse> {
  const response = await apiRequest.post<LoginResponse>(
    '/auth/select-team',
    data,
  );
  
  // 自动更新Token
  if (response.data.token) {
    TokenManager.setToken(response.data.token);
  }
  
  return response.data;
}
```

**请求格式**:
```typescript
interface SelectTeamRequest {
  teamId: number;
}
```

**响应格式**: 与登录API相同的`LoginResponse`

**后端端点**: `POST /auth/select-team`

## 3. 请求/响应拦截器

### 3.1 请求拦截器

```typescript
// 位置: frontend/src/utils/request.ts
apiRequest.interceptors.request.use(
  (config) => {
    // 自动注入Token
    const token = TokenManager.getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // 设置请求头
    config.headers['Content-Type'] = 'application/json';
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);
```

**功能**:
- **Token注入**: 自动在请求头中添加Authorization
- **请求头设置**: 统一设置Content-Type等头部
- **请求日志**: 开发环境下记录请求信息
- **请求预处理**: 对请求数据进行预处理

### 3.2 响应拦截器

```typescript
apiRequest.interceptors.response.use(
  (response) => {
    // 检查是否有新Token
    const newToken = response.headers['new-token'];
    if (newToken) {
      TokenManager.setToken(newToken);
    }
    
    return response;
  },
  async (error) => {
    const { response, config } = error;
    
    // 处理401错误 - Token过期
    if (response?.status === 401) {
      try {
        // 尝试刷新Token
        await AuthService.refreshToken();
        // 重试原始请求
        return apiRequest(config);
      } catch (refreshError) {
        // 刷新失败，跳转登录页
        TokenManager.clearToken();
        window.location.href = '/user/login';
        return Promise.reject(refreshError);
      }
    }
    
    // 其他错误处理
    handleApiError(error);
    return Promise.reject(error);
  }
);
```

**功能**:
- **Token更新**: 自动处理响应中的新Token
- **401处理**: 自动刷新过期Token并重试请求
- **错误处理**: 统一处理各种API错误
- **消息提示**: 自动显示错误消息给用户

## 4. 错误处理机制

### 4.1 错误类型和处理

| 错误类型 | HTTP状态码 | 处理方式 | 用户体验 |
|---------|-----------|---------|---------|
| 认证失败 | 401 | 自动刷新Token或跳转登录 | 无感知或友好提示 |
| 权限不足 | 403 | 显示权限错误页面 | 明确的权限说明 |
| 资源不存在 | 404 | 显示404页面 | 友好的404页面 |
| 参数错误 | 400 | 显示具体错误信息 | 明确的错误提示 |
| 服务器错误 | 500 | 显示通用错误消息 | 建议稍后重试 |
| 网络错误 | - | 显示网络错误提示 | 检查网络连接 |

### 4.2 错误处理实现

```typescript
// 位置: frontend/src/utils/request.ts
const handleApiError = (error: any) => {
  const { response } = error;
  
  if (!response) {
    // 网络错误
    message.error('网络连接失败，请检查网络设置');
    return;
  }
  
  const { status, data } = response;
  
  switch (status) {
    case 400:
      message.error(data.message || '请求参数错误');
      break;
    case 401:
      // 由拦截器处理
      break;
    case 403:
      message.error('权限不足，无法执行此操作');
      break;
    case 404:
      message.error('请求的资源不存在');
      break;
    case 500:
      message.error('服务器内部错误，请稍后重试');
      break;
    default:
      message.error(data.message || '操作失败，请重试');
  }
};
```

## 5. 状态管理集成

### 5.1 全局状态更新

```typescript
// 位置: frontend/src/app.tsx
export async function getInitialState(): Promise<{
  currentUser?: UserProfileResponse;
  currentTeam?: TeamDetailResponse;
  fetchUserInfo?: () => Promise<UserProfileResponse | undefined>;
  fetchTeamInfo?: () => Promise<TeamDetailResponse | undefined>;
}> {
  // 获取用户信息的函数
  const fetchUserInfo = async () => {
    try {
      const userInfo = await UserService.getCurrentUser();
      return userInfo;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return undefined;
    }
  };

  // 获取团队信息的函数
  const fetchTeamInfo = async () => {
    try {
      const teamInfo = await TeamService.getCurrentTeam();
      return teamInfo;
    } catch (error) {
      console.error('获取团队信息失败:', error);
      return undefined;
    }
  };

  // 初始化时获取状态
  const [currentUser, currentTeam] = await Promise.all([
    fetchUserInfo(),
    fetchTeamInfo(),
  ]);

  return {
    currentUser,
    currentTeam,
    fetchUserInfo,
    fetchTeamInfo,
  };
}
```

### 5.2 状态同步策略

**同步时机**:
- 用户登录成功后
- 团队切换成功后
- Token刷新成功后
- 页面刷新时

**同步方式**:
- **主动同步**: API调用成功后主动更新状态
- **被动同步**: 响应拦截器检测到状态变化时更新
- **定时同步**: 定期检查和更新状态（可选）
- **事件同步**: 基于特定事件触发状态同步

## 6. API调用示例

### 6.1 组件中的API调用

```typescript
// 在React组件中使用认证API
const LoginPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  
  const handleLogin = async (values: LoginRequest) => {
    setLoading(true);
    try {
      // 调用登录API
      const response = await AuthService.login(values);
      
      // 更新全局状态
      if (setInitialState) {
        setInitialState({
          currentUser: response.user,
          currentTeam: response.team,
        });
      }
      
      // 跳转页面
      if (response.team) {
        history.push('/dashboard');
      } else {
        history.push('/personal-center');
      }
    } catch (error) {
      // 错误已由拦截器处理
      console.error('登录失败:', error);
    } finally {
      setLoading(false);
    }
  };
};
```

### 6.2 Service层的API封装

```typescript
// Service层的标准API封装模式
export class AuthService {
  private static readonly BASE_URL = '/auth';
  
  static async login(data: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await apiRequest.post<LoginResponse>(
        `${this.BASE_URL}/login`,
        data
      );
      
      // 自动处理Token存储
      if (response.data.token) {
        TokenManager.setToken(response.data.token);
      }
      
      return response.data;
    } catch (error) {
      // 让拦截器处理错误
      throw error;
    }
  }
}
```

## 7. 集成测试

### 7.1 API集成测试

```typescript
// 位置: frontend/src/services/__tests__/auth.test.ts
describe('AuthService API集成测试', () => {
  beforeEach(() => {
    // 清理Token状态
    TokenManager.clearToken();
  });

  it('应该成功发送验证码', async () => {
    const mockResponse = { message: '验证码发送成功', success: true };
    (apiRequest.post as jest.Mock).mockResolvedValue({ data: mockResponse });

    const result = await AuthService.sendVerificationCode({ email: '<EMAIL>' });
    
    expect(result).toEqual(mockResponse);
    expect(apiRequest.post).toHaveBeenCalledWith('/auth/send-code', { email: '<EMAIL>' });
  });

  it('应该成功登录并存储Token', async () => {
    const mockResponse = {
      token: 'mock-jwt-token',
      user: { id: 1, email: '<EMAIL>' }
    };
    (apiRequest.post as jest.Mock).mockResolvedValue({ data: mockResponse });

    const result = await AuthService.login({ email: '<EMAIL>', code: '123456' });
    
    expect(result).toEqual(mockResponse);
    expect(TokenManager.getToken()).toBe('mock-jwt-token');
  });
});
```

### 7.2 拦截器测试

```typescript
describe('请求拦截器测试', () => {
  it('应该自动注入Token', () => {
    TokenManager.setToken('test-token');
    
    const config = { headers: {} };
    const interceptedConfig = requestInterceptor(config);
    
    expect(interceptedConfig.headers.Authorization).toBe('Bearer test-token');
  });
});

describe('响应拦截器测试', () => {
  it('应该处理401错误并刷新Token', async () => {
    const mockError = {
      response: { status: 401 },
      config: { url: '/api/test' }
    };
    
    (AuthService.refreshToken as jest.Mock).mockResolvedValue('new-token');
    
    await expect(responseInterceptor(mockError)).resolves.toBeDefined();
    expect(AuthService.refreshToken).toHaveBeenCalled();
  });
});
```

---

*最后更新时间: 2025-07-31*
