package com.teammanage.config;

import java.time.Duration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;

/**
 * Caffeine缓存配置类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@EnableCaching
public class CaffeineConfig {

    @Value("${app.session.max-concurrent-sessions:5}")
    private int maxConcurrentSessions;

    @Value("${jwt.token-expiration:604800}")
    private long tokenExpiration; // 7天

    /**
     * 会话缓存 - 用于存储用户会话信息
     * 使用Token的过期时间作为缓存过期时间
     */
    @Bean("sessionCache")
    public Cache<String, Object> sessionCache() {
        return Caffeine.newBuilder()
                .maximumSize(maxConcurrentSessions * 1000) // 基于最大并发会话数设置容量
                .expireAfterWrite(Duration.ofSeconds(tokenExpiration)) // 写入后过期时间
                .expireAfterAccess(Duration.ofHours(2)) // 访问后过期时间
                .recordStats() // 启用统计
                .build();
    }

    /**
     * 用户会话集合缓存 - 用于存储用户的会话集合
     * 使用Account Token的过期时间作为缓存过期时间
     */
    @Bean("userSessionsCache")
    public Cache<String, Object> userSessionsCache() {
        return Caffeine.newBuilder()
                .maximumSize(maxConcurrentSessions * 200) // 基于最大并发会话数设置容量
                .expireAfterWrite(Duration.ofSeconds(tokenExpiration)) // 写入后过期时间
                .expireAfterAccess(Duration.ofHours(2)) // 访问后过期时间
                .recordStats() // 启用统计
                .build();
    }

    /**
     * 通用数据缓存 - 用于缓存业务数据
     */
    @Bean("dataCache")
    public Cache<String, Object> dataCache() {
        return Caffeine.newBuilder()
                .maximumSize(5000) // 最大缓存条目数
                .expireAfterWrite(Duration.ofMinutes(30)) // 30分钟后过期
                .expireAfterAccess(Duration.ofMinutes(10)) // 10分钟无访问后过期
                .recordStats() // 启用统计
                .build();
    }

    /**
     * 团队数据缓存 - 用于缓存团队相关的短期数据
     * 使用Team Token的过期时间作为缓存过期时间
     */
    @Bean("teamCache")
    public Cache<String, Object> teamCache() {
        return Caffeine.newBuilder()
                .maximumSize(1000) // 最大缓存条目数
                .expireAfterWrite(Duration.ofSeconds(tokenExpiration)) // 使用Token过期时间
                .expireAfterAccess(Duration.ofMinutes(30)) // 30分钟无访问后过期
                .recordStats() // 启用统计
                .build();
    }


}
