package com.teammanage.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;


import java.time.LocalDateTime;

/**
 * 用户资料响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class UserProfileResponse {

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 用户名
     */
    private String name;

    /**
     * 默认订阅套餐ID
     */
    private Long defaultSubscriptionPlanId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    // Getter and Setter methods
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public Long getDefaultSubscriptionPlanId() { return defaultSubscriptionPlanId; }
    public void setDefaultSubscriptionPlanId(Long defaultSubscriptionPlanId) { this.defaultSubscriptionPlanId = defaultSubscriptionPlanId; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

}
