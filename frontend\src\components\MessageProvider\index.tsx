import React, { useEffect } from 'react';
import { App } from 'antd';
import { setMessageApi } from '@/utils/request';

interface MessageProviderProps {
  children: React.ReactNode;
}

/**
 * 消息提供者组件
 * 用于初始化全局消息API，解决静态方法无法使用动态主题的问题
 */
const MessageProvider: React.FC<MessageProviderProps> = ({ children }) => {
  const { message } = App.useApp();

  useEffect(() => {
    // 设置全局消息API
    setMessageApi(message);
  }, [message]);

  return <>{children}</>;
};

export default MessageProvider;
