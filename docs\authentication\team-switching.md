# 团队切换流程文档

## 概述

团队切换是团队管理应用的核心功能之一，允许用户在多个团队之间无缝切换。本文档详细描述了团队切换的完整流程、状态管理和错误处理机制。

## 1. 团队切换场景

### 1.1 使用场景
- **多团队用户**: 用户同时属于多个团队
- **角色切换**: 用户在不同团队中可能有不同角色
- **工作上下文切换**: 根据工作需要切换到不同团队
- **权限隔离**: 确保用户只能访问当前团队的数据

### 1.2 切换触发点
- 个人中心的团队列表
- 顶部导航的团队选择器
- 团队管理页面的团队切换
- URL直接访问时的自动团队验证

## 2. 团队切换流程

### 2.1 完整切换流程

```mermaid
flowchart TD
    A[用户点击团队] --> B{检查登录状态}
    B -->|未登录| C[跳转登录页]
    B -->|已登录| D{是否当前团队}
    D -->|是| E[直接进入仪表盘]
    D -->|否| F[调用切换API]
    F --> G{验证团队权限}
    G -->|无权限| H[显示权限错误]
    G -->|有权限| I[生成新Token]
    I --> J[返回团队信息]
    J --> K[更新本地Token]
    K --> L[记录选择历史]
    L --> M[更新全局状态]
    M --> N[跳转仪表盘]
    
    H --> O[结束]
    C --> O
    E --> O
    N --> O
```

### 2.2 前端处理流程

#### 步骤1: 用户操作检查
```typescript
// 位置: frontend/src/pages/personal-center/TeamListCard.tsx
const handleTeamSwitch = async (teamId: number, teamName: string) => {
  // 1. 检查用户登录状态
  if (!initialState?.currentUser) {
    message.error('请先登录');
    return;
  }
  
  // 2. 设置切换状态（防止重复点击）
  setSwitchingTeamId(teamId);
  
  // 3. 检查是否为当前团队
  if (teamId === actualCurrentTeamId) {
    message.success(`进入团队：${teamName}`);
    history.push('/dashboard');
    return;
  }
```

#### 步骤2: API调用和响应处理
```typescript
  try {
    // 4. 调用团队选择API
    const response = await AuthService.selectTeam({ teamId });
    
    // 5. 验证切换结果
    if (response.teamSelectionSuccess && response.team?.id === teamId) {
      message.success(`已切换到团队：${teamName}`);
      
      // 6. 记录用户选择历史
      if (currentUserId) {
        recordTeamSelection(currentUserId, teamId);
      }
      
      // 7. 异步更新全局状态
      updateGlobalState(teamId);
      
      // 8. 跳转到团队仪表盘
      history.push('/dashboard');
    }
  } catch (error) {
    // 错误处理
  } finally {
    setSwitchingTeamId(null);
  }
```

### 2.3 后端处理流程

#### 步骤1: 权限验证
```java
// 位置: backend/src/main/java/com/teammanage/service/AuthService.java
public LoginResponse selectTeam(Long teamId, String currentToken) {
    // 1. 验证当前Token
    if (!jwtTokenUtil.validateToken(currentToken)) {
        throw new BusinessException("Token无效或已过期");
    }
    
    // 2. 获取用户ID
    Long userId = jwtTokenUtil.getUserIdFromToken(currentToken);
    
    // 3. 验证团队成员关系
    TeamMember teamMember = teamMemberMapper.findByTeamIdAndAccountId(teamId, userId);
    if (teamMember == null) {
        throw TeamAccessDeniedException.notTeamMember(teamId, userId);
    }
    
    // 4. 验证成员状态
    if (!teamMember.canAccessTeam()) {
        throw new TeamAccessDeniedException(teamMember.getAccessDeniedMessage(), teamId, userId);
    }
```

#### 步骤2: Token生成和返回
```java
    // 5. 获取团队详细信息
    Team team = teamMapper.selectById(teamId);
    TeamDetailResponse teamDetail = teamService.buildTeamDetailResponse(team, teamMember);
    
    // 6. 生成新的团队Token
    String newToken = jwtTokenUtil.generateTeamToken(userId, teamId, teamMember.getRole());
    
    // 7. 构建响应
    return LoginResponse.builder()
        .token(newToken)
        .team(teamDetail)
        .teamSelectionSuccess(true)
        .build();
}
```

## 3. 状态管理

### 3.1 Token状态管理

```mermaid
stateDiagram-v2
    [*] --> UserToken: 用户登录
    UserToken --> TeamToken: 选择团队
    TeamToken --> NewTeamToken: 切换团队
    TeamToken --> UserToken: 清除团队
    NewTeamToken --> TeamToken: 切换完成
    TeamToken --> [*]: 用户登出
    UserToken --> [*]: 用户登出
```

**Token状态说明**:
- **UserToken**: 只包含用户信息的Token
- **TeamToken**: 包含用户和团队信息的Token
- **NewTeamToken**: 切换过程中的临时Token状态

### 3.2 全局状态同步

```typescript
// 异步更新全局状态
const updateGlobalState = async (teamId: number) => {
  if (initialState?.fetchTeamInfo && initialState?.fetchUserInfo && setInitialState) {
    try {
      // 并行获取最新信息
      const [currentUser, currentTeam] = await Promise.all([
        initialState.fetchUserInfo(),
        initialState.fetchTeamInfo(),
      ]);
      
      // 验证团队信息正确性
      if (currentTeam && currentTeam.id === teamId) {
        setInitialState({
          ...initialState,
          currentUser,
          currentTeam,
        });
      }
    } catch (error) {
      console.error('更新全局状态失败:', error);
    }
  }
};
```

## 4. 用户体验优化

### 4.1 加载状态管理
- **切换状态指示**: 显示正在切换的团队
- **防重复点击**: 切换过程中禁用其他团队的点击
- **视觉反馈**: 当前团队的高亮显示
- **进度提示**: 切换过程的友好提示

### 4.2 错误处理和恢复
- **网络错误**: 提示检查网络连接
- **权限错误**: 显示具体的权限问题
- **状态恢复**: 错误后恢复到安全状态
- **重试机制**: 支持用户手动重试

### 4.3 性能优化
- **状态缓存**: 缓存团队列表减少重复请求
- **异步更新**: 状态更新不阻塞页面跳转
- **预加载**: 预加载常用团队的信息
- **智能刷新**: 只在必要时刷新团队信息

## 5. 安全考虑

### 5.1 权限验证
- **双重验证**: 前端和后端都进行权限检查
- **实时验证**: 每次切换都验证最新权限
- **角色隔离**: 不同角色的权限严格隔离
- **审计日志**: 记录所有团队切换操作

### 5.2 数据隔离
- **团队数据隔离**: 确保用户只能访问当前团队数据
- **Token隔离**: 不同团队使用不同的Token
- **会话隔离**: 团队切换后清除前一个团队的会话数据
- **缓存隔离**: 不同团队的缓存数据分离

## 6. 故障排除

### 6.1 常见问题

**问题1: 团队切换后数据未更新**
- **原因**: 全局状态更新失败
- **解决**: 检查网络连接，手动刷新页面
- **预防**: 改进状态更新的错误处理

**问题2: 切换后权限异常**
- **原因**: Token信息与实际权限不同步
- **解决**: 重新登录或联系管理员
- **预防**: 加强Token验证机制

**问题3: 切换过程中页面卡死**
- **原因**: API请求超时或网络问题
- **解决**: 刷新页面重试
- **预防**: 添加请求超时处理

### 6.2 调试方法

1. **检查Token内容**:
   ```javascript
   import { getCurrentTokenInfo } from '@/utils/tokenUtils';
   console.log('当前Token信息:', getCurrentTokenInfo());
   ```

2. **检查全局状态**:
   ```javascript
   console.log('全局状态:', initialState);
   ```

3. **检查网络请求**:
   - 打开浏览器开发者工具
   - 查看Network标签页
   - 检查API请求和响应

---

*最后更新时间: 2025-07-31*
