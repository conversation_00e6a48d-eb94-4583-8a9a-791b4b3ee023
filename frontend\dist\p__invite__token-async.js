((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] || []).push([
        ['p__invite__token'],
{ "src/pages/invite/[token].tsx": function (module, exports, __mako_require__){
/**
 * 邀请链接处理页面
 * 路由: /invite/:token
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _antdstyle = __mako_require__("node_modules/antd-style/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _components = __mako_require__("src/components/index.ts");
var _services = __mako_require__("src/services/index.ts");
var _request = __mako_require__("src/utils/request.ts");
var _defaultSettings = /*#__PURE__*/ _interop_require_default._(__mako_require__("config/defaultSettings.ts"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text, Paragraph } = _antd.Typography;
const useStyles = (0, _antdstyle.createStyles)(({ token })=>{
    return {
        container: {
            display: 'flex',
            flexDirection: 'column',
            height: '100vh',
            overflow: 'auto',
            backgroundImage: "url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')",
            backgroundSize: '100% 100%'
        },
        content: {
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '32px 16px'
        },
        header: {
            marginBottom: 40,
            textAlign: 'center'
        },
        logo: {
            marginBottom: 16
        },
        title: {
            marginBottom: 0
        },
        inviteCard: {
            width: '100%',
            maxWidth: 500,
            boxShadow: token.boxShadowTertiary
        },
        footer: {
            marginTop: 40,
            textAlign: 'center'
        }
    };
});
const InvitePage = ()=>{
    _s();
    const { token } = (0, _max.useParams)();
    const [loading, setLoading] = (0, _react.useState)(true);
    const [processing, setProcessing] = (0, _react.useState)(false);
    const [invitationInfo, setInvitationInfo] = (0, _react.useState)(null);
    const [result, setResult] = (0, _react.useState)(null);
    const { styles } = useStyles();
    // 获取邀请信息
    const fetchInvitationInfo = async ()=>{
        if (!token) {
            setResult({
                type: 'error',
                title: '邀请链接无效',
                message: '邀请链接格式错误或已过期'
            });
            setLoading(false);
            return;
        }
        try {
            const response = await _services.InvitationService.getInvitationInfo(token);
            if (response.success) setInvitationInfo(response);
            else setResult({
                type: 'error',
                title: '邀请链接无效',
                message: response.errorMessage || '无法获取邀请信息'
            });
        } catch (error) {
            setResult({
                type: 'error',
                title: '获取邀请信息失败',
                message: '网络错误，请稍后重试'
            });
        } finally{
            setLoading(false);
        }
    };
    // 处理邀请接受
    const handleAcceptInvitation = async ()=>{
        if (!token) return;
        setProcessing(true);
        try {
            const response = await _services.InvitationService.acceptInvitationByLink(token, {});
            if (response.success) {
                // 如果是新用户且返回了访问令牌，自动登录
                if (response.isNewUser && response.accessToken) try {
                    _request.TokenManager.setToken(response.accessToken);
                    setResult({
                        type: 'success',
                        title: '欢迎加入团队！',
                        message: `您的账号已自动创建并登录（无需密码）。${response.nextAction || '正在跳转到个人中心...'}`,
                        teamName: response.teamName,
                        isNewUser: response.isNewUser,
                        autoLogin: true
                    });
                    // 延迟跳转到个人中心
                    setTimeout(()=>{
                        _max.history.push('/personal-center');
                    }, 3000);
                } catch (error) {
                    setResult({
                        type: 'success',
                        title: '账号创建成功！',
                        message: '您的账号已成功创建（无需密码），请使用邮箱验证码登录后查看团队信息。',
                        teamName: response.teamName,
                        isNewUser: response.isNewUser,
                        autoLogin: false
                    });
                }
                else if (response.isNewUser) // 新用户但没有自动登录令牌
                setResult({
                    type: 'success',
                    title: '账号创建成功！',
                    message: '您的账号已成功创建（无需密码），请使用邮箱验证码登录后查看团队信息。',
                    teamName: response.teamName,
                    isNewUser: response.isNewUser,
                    autoLogin: false
                });
                else // 现有用户
                setResult({
                    type: 'success',
                    title: '加入成功！',
                    message: response.nextAction || '您已成功加入团队，请登录后查看团队信息。',
                    teamName: response.teamName,
                    isNewUser: response.isNewUser,
                    autoLogin: false
                });
            } else setResult({
                type: 'error',
                title: '加入失败',
                message: response.errorMessage || '处理邀请时发生错误'
            });
        } catch (error) {
            setResult({
                type: 'error',
                title: '加入失败',
                message: '网络错误，请稍后重试'
            });
        } finally{
            setProcessing(false);
        }
    };
    // 处理取消操作
    const handleCancel = ()=>{
        _max.history.push('/');
    };
    // 邀请确认界面
    const InvitationConfirm = ()=>{
        if (!invitationInfo) return null;
        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
            style: {
                textAlign: 'center',
                padding: '40px 20px'
            },
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                    style: {
                        fontSize: 64,
                        color: '#1890ff',
                        marginBottom: 24
                    }
                }, void 0, false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 201,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                    level: 3,
                    children: "团队邀请"
                }, void 0, false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 202,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        marginBottom: 32
                    },
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        direction: "vertical",
                        size: "middle",
                        style: {
                            width: '100%'
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: "您被邀请加入团队："
                                    }, void 0, false, {
                                        fileName: "src/pages/invite/[token].tsx",
                                        lineNumber: 207,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                        fileName: "src/pages/invite/[token].tsx",
                                        lineNumber: 208,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                        level: 4,
                                        style: {
                                            margin: '8px 0',
                                            color: '#1890ff'
                                        },
                                        children: invitationInfo.teamName
                                    }, void 0, false, {
                                        fileName: "src/pages/invite/[token].tsx",
                                        lineNumber: 209,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/invite/[token].tsx",
                                lineNumber: 206,
                                columnNumber: 13
                            }, this),
                            invitationInfo.inviterName && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: "邀请人："
                                    }, void 0, false, {
                                        fileName: "src/pages/invite/[token].tsx",
                                        lineNumber: 216,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: invitationInfo.inviterName
                                    }, void 0, false, {
                                        fileName: "src/pages/invite/[token].tsx",
                                        lineNumber: 217,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/invite/[token].tsx",
                                lineNumber: 215,
                                columnNumber: 15
                            }, this),
                            invitationInfo.message && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: "邀请消息："
                                    }, void 0, false, {
                                        fileName: "src/pages/invite/[token].tsx",
                                        lineNumber: 223,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                        fileName: "src/pages/invite/[token].tsx",
                                        lineNumber: 224,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        italic: true,
                                        children: [
                                            '"',
                                            invitationInfo.message,
                                            '"'
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/invite/[token].tsx",
                                        lineNumber: 225,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/invite/[token].tsx",
                                lineNumber: 222,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/invite/[token].tsx",
                        lineNumber: 205,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 204,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {}, void 0, false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 231,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        marginTop: 24
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                            level: 4,
                            style: {
                                marginBottom: 24
                            },
                            children: "您确定要加入此团队吗？"
                        }, void 0, false, {
                            fileName: "src/pages/invite/[token].tsx",
                            lineNumber: 234,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            size: "large",
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "primary",
                                    size: "large",
                                    loading: processing,
                                    onClick: handleAcceptInvitation,
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckCircleOutlined, {}, void 0, false, {
                                        fileName: "src/pages/invite/[token].tsx",
                                        lineNumber: 244,
                                        columnNumber: 21
                                    }, void 0),
                                    disabled: invitationInfo.isExpired,
                                    children: processing ? '正在处理...' : '确认加入'
                                }, void 0, false, {
                                    fileName: "src/pages/invite/[token].tsx",
                                    lineNumber: 239,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    size: "large",
                                    onClick: handleCancel,
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CloseCircleOutlined, {}, void 0, false, {
                                        fileName: "src/pages/invite/[token].tsx",
                                        lineNumber: 252,
                                        columnNumber: 21
                                    }, void 0),
                                    disabled: processing,
                                    children: "取消"
                                }, void 0, false, {
                                    fileName: "src/pages/invite/[token].tsx",
                                    lineNumber: 249,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/invite/[token].tsx",
                            lineNumber: 238,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 233,
                    columnNumber: 9
                }, this),
                invitationInfo.isExpired && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                    message: "邀请已过期",
                    description: "此邀请链接已过期，请联系团队管理员重新发送邀请。",
                    type: "warning",
                    showIcon: true,
                    style: {
                        marginTop: 24
                    }
                }, void 0, false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 261,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/invite/[token].tsx",
            lineNumber: 200,
            columnNumber: 7
        }, this);
    };
    // 结果展示
    const ResultDisplay = ()=>{
        if (!result) return null;
        // 根据结果类型和自动登录状态显示不同的按钮
        const getExtraButtons = ()=>{
            if (result.type === 'success') {
                if (result.autoLogin) // 自动登录成功，显示跳转到个人中心的按钮
                return [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "primary",
                        onClick: ()=>_max.history.push('/personal-center'),
                        children: "前往个人中心"
                    }, "personal-center", false, {
                        fileName: "src/pages/invite/[token].tsx",
                        lineNumber: 283,
                        columnNumber: 13
                    }, this)
                ];
                else if (result.isNewUser) // 新用户但未自动登录，引导去登录
                return [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "primary",
                        onClick: ()=>_max.history.push('/user/login'),
                        children: "前往登录"
                    }, "login", false, {
                        fileName: "src/pages/invite/[token].tsx",
                        lineNumber: 290,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        onClick: ()=>_max.history.push('/'),
                        children: "返回首页"
                    }, "home", false, {
                        fileName: "src/pages/invite/[token].tsx",
                        lineNumber: 293,
                        columnNumber: 13
                    }, this)
                ];
                else // 现有用户，引导去登录
                return [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "primary",
                        onClick: ()=>_max.history.push('/user/login'),
                        children: "前往登录"
                    }, "login", false, {
                        fileName: "src/pages/invite/[token].tsx",
                        lineNumber: 300,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        onClick: ()=>_max.history.push('/'),
                        children: "返回首页"
                    }, "home", false, {
                        fileName: "src/pages/invite/[token].tsx",
                        lineNumber: 303,
                        columnNumber: 13
                    }, this)
                ];
            } else // 错误情况，显示重试和返回首页
            return [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                    type: "primary",
                    onClick: ()=>window.location.reload(),
                    children: "重试"
                }, "retry", false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 311,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                    onClick: ()=>_max.history.push('/'),
                    children: "返回首页"
                }, "home", false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 314,
                    columnNumber: 11
                }, this)
            ];
        };
        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Result, {
            status: result.type,
            title: result.title,
            subTitle: result.message,
            extra: getExtraButtons()
        }, void 0, false, {
            fileName: "src/pages/invite/[token].tsx",
            lineNumber: 322,
            columnNumber: 7
        }, this);
    };
    // 页面加载时获取邀请信息
    (0, _react.useEffect)(()=>{
        fetchInvitationInfo();
    }, [
        token
    ]);
    // 加载中状态
    if (loading) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        className: styles.container,
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.Helmet, {
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("title", {
                    children: [
                        "团队邀请",
                        _defaultSettings.default.title && ` - ${_defaultSettings.default.title}`
                    ]
                }, void 0, true, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 341,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/invite/[token].tsx",
                lineNumber: 340,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                className: styles.content,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                    className: styles.inviteCard,
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            textAlign: 'center',
                            padding: '60px 20px'
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                                size: "large"
                            }, void 0, false, {
                                fileName: "src/pages/invite/[token].tsx",
                                lineNumber: 349,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    marginTop: 16
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    children: "正在加载邀请信息..."
                                }, void 0, false, {
                                    fileName: "src/pages/invite/[token].tsx",
                                    lineNumber: 351,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/invite/[token].tsx",
                                lineNumber: 350,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/invite/[token].tsx",
                        lineNumber: 348,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 347,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/invite/[token].tsx",
                lineNumber: 346,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.Footer, {}, void 0, false, {
                fileName: "src/pages/invite/[token].tsx",
                lineNumber: 356,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/invite/[token].tsx",
        lineNumber: 339,
        columnNumber: 7
    }, this);
    // 显示结果页面
    if (result) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        className: styles.container,
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.Helmet, {
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("title", {
                    children: [
                        "团队邀请",
                        _defaultSettings.default.title && ` - ${_defaultSettings.default.title}`
                    ]
                }, void 0, true, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 366,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/invite/[token].tsx",
                lineNumber: 365,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                className: styles.content,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                    className: styles.inviteCard,
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(ResultDisplay, {}, void 0, false, {
                        fileName: "src/pages/invite/[token].tsx",
                        lineNumber: 373,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 372,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/invite/[token].tsx",
                lineNumber: 371,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.Footer, {}, void 0, false, {
                fileName: "src/pages/invite/[token].tsx",
                lineNumber: 376,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/invite/[token].tsx",
        lineNumber: 364,
        columnNumber: 7
    }, this);
    // 显示邀请确认页面
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        className: styles.container,
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.Helmet, {
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("title", {
                    children: [
                        "团队邀请",
                        _defaultSettings.default.title && ` - ${_defaultSettings.default.title}`
                    ]
                }, void 0, true, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 385,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/invite/[token].tsx",
                lineNumber: 384,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                className: styles.content,
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        className: styles.header,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            direction: "vertical",
                            align: "center",
                            size: "large",
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: styles.logo,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("img", {
                                        src: "/logo.svg",
                                        alt: "TeamAuth",
                                        height: 48
                                    }, void 0, false, {
                                        fileName: "src/pages/invite/[token].tsx",
                                        lineNumber: 394,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/invite/[token].tsx",
                                    lineNumber: 393,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: styles.title,
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                            level: 2,
                                            children: "团队邀请"
                                        }, void 0, false, {
                                            fileName: "src/pages/invite/[token].tsx",
                                            lineNumber: 397,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            type: "secondary",
                                            children: "加入团队，开始协作"
                                        }, void 0, false, {
                                            fileName: "src/pages/invite/[token].tsx",
                                            lineNumber: 398,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/invite/[token].tsx",
                                    lineNumber: 396,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/invite/[token].tsx",
                            lineNumber: 392,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/invite/[token].tsx",
                        lineNumber: 391,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                        className: styles.inviteCard,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(InvitationConfirm, {}, void 0, false, {
                            fileName: "src/pages/invite/[token].tsx",
                            lineNumber: 404,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/invite/[token].tsx",
                        lineNumber: 403,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        className: styles.footer,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            type: "secondary",
                            children: "© 2025 TeamAuth. All rights reserved."
                        }, void 0, false, {
                            fileName: "src/pages/invite/[token].tsx",
                            lineNumber: 408,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/invite/[token].tsx",
                        lineNumber: 407,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/invite/[token].tsx",
                lineNumber: 390,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.Footer, {}, void 0, false, {
                fileName: "src/pages/invite/[token].tsx",
                lineNumber: 411,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/invite/[token].tsx",
        lineNumber: 383,
        columnNumber: 5
    }, this);
};
_s(InvitePage, "BKdBJ5I1qD3mEg2QSXn93lLBB0A=", false, function() {
    return [
        _max.useParams,
        useStyles
    ];
});
_c = InvitePage;
var _default = InvitePage;
var _c;
$RefreshReg$(_c, "InvitePage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__invite__token-async.js.map