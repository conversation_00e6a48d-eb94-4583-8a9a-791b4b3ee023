package com.teammanage.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.teammanage.common.ApiResponse;
import com.teammanage.dto.request.CreateTodoRequest;
import com.teammanage.dto.request.UpdateTodoRequest;
import com.teammanage.dto.response.TodoResponse;
import com.teammanage.service.TodoService;
import com.teammanage.util.SecurityUtil;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

/**
 * TODO控制器
 */
@RestController
@RequestMapping("/todos")
@Tag(name = "TODO管理", description = "TODO相关接口")
@SecurityRequirement(name = "bearerAuth")
public class TodoController {

    @Autowired
    private TodoService todoService;

    /**
     * 获取用户的TODO列表
     */
    @GetMapping
    @Operation(summary = "获取TODO列表", description = "获取当前用户的所有TODO")
    public ApiResponse<List<TodoResponse>> getUserTodos() {
        Long userId = SecurityUtil.getCurrentUserId();
        List<TodoResponse> todos = todoService.getUserTodos(userId);
        return ApiResponse.success(todos);
    }

    /**
     * 创建TODO
     */
    @PostMapping
    @Operation(summary = "创建TODO", description = "创建新的TODO")
    public ApiResponse<TodoResponse> createTodo(@Valid @RequestBody CreateTodoRequest request) {
        Long userId = SecurityUtil.getCurrentUserId();
        TodoResponse todo = todoService.createTodo(request, userId);
        return ApiResponse.success("TODO创建成功", todo);
    }

    /**
     * 更新TODO
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新TODO", description = "更新指定的TODO")
    public ApiResponse<TodoResponse> updateTodo(
            @PathVariable Long id,
            @Valid @RequestBody UpdateTodoRequest request) {
        Long userId = SecurityUtil.getCurrentUserId();
        TodoResponse todo = todoService.updateTodo(id, request, userId);
        return ApiResponse.success("TODO更新成功", todo);
    }

    /**
     * 删除TODO
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除TODO", description = "删除指定的TODO")
    public ApiResponse<Void> deleteTodo(@PathVariable Long id) {
        Long userId = SecurityUtil.getCurrentUserId();
        todoService.deleteTodo(id, userId);
        return ApiResponse.success("TODO删除成功", null);
    }

    /**
     * 获取TODO统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取TODO统计", description = "获取当前用户的TODO统计信息")
    public ApiResponse<Map<String, Object>> getTodoStats() {
        Long userId = SecurityUtil.getCurrentUserId();
        Map<String, Object> stats = todoService.getTodoStats(userId);
        return ApiResponse.success(stats);
    }
}
