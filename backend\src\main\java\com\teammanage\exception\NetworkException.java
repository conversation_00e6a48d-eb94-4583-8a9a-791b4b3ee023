package com.teammanage.exception;

/**
 * 网络相关异常
 * 
 * 用于处理网络连接、超时、服务不可用等相关错误
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class NetworkException extends RuntimeException {

    private final Integer code;
    private final String service;
    private final String operation;
    private final Long timeout;

    /**
     * 构造函数
     * 
     * @param message 错误消息
     */
    public NetworkException(String message) {
        super(message);
        this.code = 503;
        this.service = null;
        this.operation = null;
        this.timeout = null;
    }

    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param cause 原因
     */
    public NetworkException(String message, Throwable cause) {
        super(message, cause);
        this.code = 503;
        this.service = null;
        this.operation = null;
        this.timeout = null;
    }

    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param service 服务名称
     * @param operation 操作类型
     * @param timeout 超时时间
     */
    public NetworkException(String message, String service, String operation, Long timeout) {
        super(message);
        this.code = 503;
        this.service = service;
        this.operation = operation;
        this.timeout = timeout;
    }

    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param cause 原因
     * @param service 服务名称
     * @param operation 操作类型
     * @param timeout 超时时间
     */
    public NetworkException(String message, Throwable cause, String service, String operation, Long timeout) {
        super(message, cause);
        this.code = 503;
        this.service = service;
        this.operation = operation;
        this.timeout = timeout;
    }

    // Getter 方法
    public Integer getCode() {
        return code;
    }

    public String getService() {
        return service;
    }

    public String getOperation() {
        return operation;
    }

    public Long getTimeout() {
        return timeout;
    }

    // 静态工厂方法，用于创建常见的网络异常

    /**
     * 创建请求超时异常
     * 
     * @param service 服务名称
     * @param timeout 超时时间（毫秒）
     * @param cause 原因
     * @return 异常实例
     */
    public static NetworkException requestTimeout(String service, Long timeout, Throwable cause) {
        return new NetworkException(
            "请求超时，请稍后重试",
            cause,
            service,
            "REQUEST",
            timeout
        );
    }

    /**
     * 创建连接超时异常
     * 
     * @param service 服务名称
     * @param timeout 超时时间（毫秒）
     * @param cause 原因
     * @return 异常实例
     */
    public static NetworkException connectionTimeout(String service, Long timeout, Throwable cause) {
        return new NetworkException(
            "连接超时，请检查网络连接",
            cause,
            service,
            "CONNECTION",
            timeout
        );
    }

    /**
     * 创建服务不可用异常
     * 
     * @param service 服务名称
     * @param cause 原因
     * @return 异常实例
     */
    public static NetworkException serviceUnavailable(String service, Throwable cause) {
        return new NetworkException(
            "服务暂时不可用，请稍后重试",
            cause,
            service,
            "SERVICE_UNAVAILABLE",
            null
        );
    }

    /**
     * 创建邮件服务异常
     * 
     * @param operation 操作类型
     * @param cause 原因
     * @return 异常实例
     */
    public static NetworkException emailServiceError(String operation, Throwable cause) {
        return new NetworkException(
            "邮件发送失败，请稍后重试",
            cause,
            "EMAIL_SERVICE",
            operation,
            null
        );
    }

    /**
     * 创建网络连接中断异常
     * 
     * @param service 服务名称
     * @param cause 原因
     * @return 异常实例
     */
    public static NetworkException connectionInterrupted(String service, Throwable cause) {
        return new NetworkException(
            "网络连接中断，请检查网络状态",
            cause,
            service,
            "CONNECTION_INTERRUPTED",
            null
        );
    }

    @Override
    public String toString() {
        return "NetworkException{" +
                "code=" + code +
                ", service='" + service + '\'' +
                ", operation='" + operation + '\'' +
                ", timeout=" + timeout +
                ", message='" + getMessage() + '\'' +
                '}';
    }
}
