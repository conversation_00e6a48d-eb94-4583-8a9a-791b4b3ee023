package com.teammanage.service;

import com.teammanage.dto.request.CreateTodoRequest;
import com.teammanage.dto.request.UpdateTodoRequest;
import com.teammanage.dto.response.TodoResponse;

import java.util.List;
import java.util.Map;

/**
 * TODO服务接口
 */
public interface TodoService {

    /**
     * 获取用户的TODO列表
     */
    List<TodoResponse> getUserTodos(Long userId);

    /**
     * 创建TODO
     */
    TodoResponse createTodo(CreateTodoRequest request, Long userId);

    /**
     * 更新TODO
     */
    TodoResponse updateTodo(Long todoId, UpdateTodoRequest request, Long userId);

    /**
     * 删除TODO
     */
    void deleteTodo(Long todoId, Long userId);

    /**
     * 获取TODO统计信息
     */
    Map<String, Object> getTodoStats(Long userId);
}
