package com.teammanage.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;


/**
 * 更新团队请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

public class UpdateTeamRequest {

    /**
     * 团队名称
     */
    @NotBlank(message = "团队名称不能为空")
    @Size(max = 100, message = "团队名称长度不能超过100字符")
    private String name;

    /**
     * 团队描述
     */
    @Size(max = 500, message = "团队描述长度不能超过500字符")
    private String description;

    // 手动添加getter/setter方法
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

}
