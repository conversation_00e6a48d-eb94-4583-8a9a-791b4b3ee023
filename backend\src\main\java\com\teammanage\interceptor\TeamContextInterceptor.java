package com.teammanage.interceptor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import com.teammanage.context.TeamContextHolder;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 团队上下文拦截器
 * 在请求处理前设置团队上下文，请求处理后清理上下文
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class TeamContextInterceptor implements HandlerInterceptor {

    private static final Logger log = LoggerFactory.getLogger(TeamContextInterceptor.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 在简化的系统中，不再需要设置团队上下文
        // 团队相关的权限检查将通过其他方式实现
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 清理团队上下文
        TeamContextHolder.clear();
    }

}
