{"version": 3, "sources": ["src_pages_personal-center_index_tsx-async.10086463622502801591.hot-update.js", "src/pages/personal-center/PersonalInfo.tsx", "F:\\Project\\teamAuth\\frontend\\src\\pages\\personal-center\\UserInfoPopover.module.css?asmodule", "src/pages/personal-center/UserInfoPopover.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/personal-center/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='13784631362800528906';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/settings/index.tsx\":[\"p__settings__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import {\n  ClockCircleOutlined,\n  SettingOutlined,\n  TeamOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport {\n  <PERSON>ert,\n  Avatar,\n  Button,\n  Space,\n  Spin,\n  Typography,\n} from 'antd';\nimport { ProCard } from '@ant-design/pro-components';\n\nimport React, { useEffect, useState } from 'react';\nimport { UserService } from '@/services/user';\nimport type { UserProfileDetailResponse } from '@/types/api';\nimport UnifiedSettingsModal from './UnifiedSettingsModal';\nimport UserInfoPopover from './UserInfoPopover';\nimport styles from './PersonalInfo.module.css';\n\nconst { Title, Text } = Typography;\n\n/**\n * 个人信息组件\n *\n * 显示用户的基本个人信息，采用简洁的卡片设计。\n * 用户名支持悬浮显示详细信息（电话、邮箱、注册时间等）。\n *\n * 主要功能：\n * 1. 显示用户头像（使用姓名首字母）\n * 2. 显示用户姓名（支持气泡卡片显示详细信息）\n * 3. 显示最后登录时间和登录团队\n * 4. 提供设置入口\n *\n * 数据来源：\n * - 用户详细信息：通过UserService.getUserProfileDetail()获取\n */\nconst PersonalInfo: React.FC = () => {\n  /**\n   * 用户详细信息状态管理\n   */\n  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({\n    name: '',\n    position: '',\n    email: '',\n    telephone: '',\n    registerDate: '',\n    lastLoginTime: '',\n    lastLoginTeam: '',\n    teamCount: 0,\n    avatar: '',\n  });\n\n  const [userInfoLoading, setUserInfoLoading] = useState(true);\n  const [userInfoError, setUserInfoError] = useState<string | null>(null);\n\n  // Modal状态管理\n  const [settingsModalVisible, setSettingsModalVisible] = useState(false);\n\n  // 获取用户数据\n  useEffect(() => {\n    const fetchUserData = async () => {\n      try {\n        const userDetail = await UserService.getUserProfileDetail();\n        setUserInfo(userDetail);\n        setUserInfoError(null);\n      } catch (error) {\n        console.error('获取用户详细信息失败:', error);\n        setUserInfoError('获取用户详细信息失败，请稍后重试');\n      } finally {\n        setUserInfoLoading(false);\n      }\n    };\n\n    fetchUserData();\n  }, []);\n\n  return (\n    <ProCard\n      title=\"个人信息\"\n      extra={\n        <Button\n          type=\"text\"\n          icon={<SettingOutlined />}\n          onClick={() => setSettingsModalVisible(true)}\n        />\n      }\n      style={{\n        marginBottom: 16,\n        borderRadius: 12,\n        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',\n      }}\n    >\n      {userInfoError ? (\n        <Alert\n          message=\"个人信息加载失败\"\n          description={userInfoError}\n          type=\"error\"\n          showIcon\n          style={{\n            borderRadius: 12,\n            border: 'none',\n          }}\n        />\n      ) : (\n        <Spin spinning={userInfoLoading}>\n          {/* 主要内容区域 */}\n          <div className={styles.personalInfoContent}>\n            <div className={styles.userInfoSection}>\n              {/* 头像区域 */}\n              <div className={styles.avatarContainer}>\n                <Avatar\n                  size={80}\n                  className={styles.avatar}\n                  icon={!userInfo.name && <UserOutlined />}\n                >\n                  {userInfo.name?.charAt(0).toUpperCase()}\n                </Avatar>\n                {/* 在线状态指示器 */}\n                <div className={styles.onlineIndicator} />\n              </div>\n\n              {/* 基本信息 */}\n              <div className={styles.userBasicInfo}>\n                <UserInfoPopover userInfo={userInfo}>\n                  <Typography.Title\n                    level={3}\n                    className={styles.userName}\n                  >\n                    {userInfo.name || '加载中...'}\n                  </Typography.Title>\n                </UserInfoPopover>\n\n                {/* 职位信息（如果有） */}\n                {userInfo.position && (\n                  <Text\n                    type=\"secondary\"\n                    style={{\n                      fontSize: 14,\n                      marginBottom: 12,\n                      display: 'block',\n                    }}\n                  >\n                    {userInfo.position}\n                  </Text>\n                )}\n              </div>\n            </div>\n\n            {/* 登录信息区域 */}\n            <div className={styles.loginInfoSection}>\n              <Space direction=\"vertical\" size={8} style={{ width: '100%' }}>\n                {userInfo.lastLoginTime && (\n                  <div className={styles.loginInfoItem}>\n                    <ClockCircleOutlined\n                      style={{\n                        fontSize: 14,\n                        color: '#1890ff',\n                      }}\n                    />\n                    <Typography.Text\n                      style={{\n                        fontSize: 13,\n                        color: '#8c8c8c',\n                        fontWeight: 500,\n                      }}\n                    >\n                      最后登录：{userInfo.lastLoginTime}\n                    </Typography.Text>\n                  </div>\n                )}\n                {userInfo.lastLoginTeam && (\n                  <div className={styles.loginInfoItem}>\n                    <TeamOutlined\n                      style={{\n                        fontSize: 14,\n                        color: '#52c41a',\n                      }}\n                    />\n                    <Typography.Text\n                      style={{\n                        fontSize: 13,\n                        color: '#8c8c8c',\n                        fontWeight: 500,\n                      }}\n                    >\n                      团队：{userInfo.lastLoginTeam}\n                    </Typography.Text>\n                  </div>\n                )}\n              </Space>\n            </div>\n          </div>\n        </Spin>\n      )}\n\n      {/* 统一设置Modal */}\n      <UnifiedSettingsModal\n        visible={settingsModalVisible}\n        onCancel={() => setSettingsModalVisible(false)}\n        userInfo={userInfo}\n        onSuccess={() => {\n          // 可以在这里刷新用户信息\n          console.log('设置操作成功');\n        }}\n      />\n    </ProCard>\n  );\n};\n\nexport default PersonalInfo;\n", "\nimport \"F:/Project/teamAuth/frontend/src/pages/personal-center/UserInfoPopover.module.css?modules\";\nexport default {\"trigger\": `trigger-vd13A828`,\"popoverTitle\": `popoverTitle-gZ0PK1Dm`,\"iconWrapper\": `iconWrapper-HCDP4HDG`,\"icon\": `icon-z1rS9q3h`,\"label\": `label-fY2ApLsi`,\"value\": `value-ClCU1LzT`,\"popoverOverlay\": `popoverOverlay-wOd48E7k`,\"popoverContent\": `popoverContent-EhVEGF9V`,\"fadeIn\": `fadeIn-Bxe6iemy`,\"infoContent\": `infoContent-AvzO53H-`,\"infoItem\": `infoItem-TRvgqB7k`}\n", "import {\n  MailOutlined,\n  PhoneOutlined,\n  CalendarOutlined,\n  ClockCircleOutlined,\n  TeamOutlined,\n} from '@ant-design/icons';\nimport {\n  Popover,\n  Space,\n  Typography,\n  Divider,\n} from 'antd';\nimport React from 'react';\nimport type { UserProfileDetailResponse } from '@/types/api';\nimport styles from './UserInfoPopover.module.css';\n\nconst { Text } = Typography;\n\ninterface UserInfoPopoverProps {\n  userInfo: UserProfileDetailResponse;\n  children: React.ReactNode;\n}\n\n/**\n * 用户信息气泡卡片组件\n *\n * 在用户名上显示详细的用户信息，包括电话、邮箱、注册时间等。\n * 采用Popover组件实现悬浮显示效果。\n *\n * 主要功能：\n * 1. 显示用户邮箱\n * 2. 显示用户电话\n * 3. 显示注册时间\n * 4. 显示最后登录时间\n * 5. 显示最后登录团队\n *\n * 使用方式：\n * <UserInfoPopover userInfo={userInfo}>\n *   <span>用户名</span>\n * </UserInfoPopover>\n */\nconst UserInfoPopover: React.FC<UserInfoPopoverProps> = ({\n  userInfo,\n  children,\n}) => {\n  const popoverContent = (\n    <div className={styles.popoverContent}>\n      <Space direction=\"vertical\" size={12} style={{ width: '100%' }}>\n        {/* 联系信息 */}\n        {userInfo.email && (\n          <div className={styles.infoItem}>\n            <div className={styles.iconWrapper}>\n              <MailOutlined className={styles.icon} style={{ color: '#1890ff' }} />\n            </div>\n            <div className={styles.infoContent}>\n              <Text type=\"secondary\" className={styles.label}>\n                邮箱\n              </Text>\n              <Text className={styles.value} copyable>\n                {userInfo.email}\n              </Text>\n            </div>\n          </div>\n        )}\n\n        {userInfo.telephone && (\n          <div className={styles.infoItem}>\n            <div className={styles.iconWrapper}>\n              <PhoneOutlined className={styles.icon} style={{ color: '#52c41a' }} />\n            </div>\n            <div className={styles.infoContent}>\n              <Text type=\"secondary\" className={styles.label}>\n                电话\n              </Text>\n              <Text className={styles.value} copyable>\n                {userInfo.telephone}\n              </Text>\n            </div>\n          </div>\n        )}\n\n        {(userInfo.email || userInfo.telephone) && userInfo.registerDate && (\n          <Divider style={{ margin: '8px 0' }} />\n        )}\n\n        {/* 时间信息 */}\n        {userInfo.registerDate && (\n          <div className={styles.infoItem}>\n            <div className={styles.iconWrapper}>\n              <CalendarOutlined className={styles.icon} style={{ color: '#722ed1' }} />\n            </div>\n            <div className={styles.infoContent}>\n              <Text type=\"secondary\" className={styles.label}>\n                注册时间\n              </Text>\n              <Text className={styles.value}>\n                {userInfo.registerDate}\n              </Text>\n            </div>\n          </div>\n        )}\n\n        {userInfo.lastLoginTime && (\n          <div className={styles.infoItem}>\n            <div className={styles.iconWrapper}>\n              <ClockCircleOutlined className={styles.icon} style={{ color: '#fa8c16' }} />\n            </div>\n            <div className={styles.infoContent}>\n              <Text type=\"secondary\" className={styles.label}>\n                最后登录\n              </Text>\n              <Text className={styles.value}>\n                {userInfo.lastLoginTime}\n              </Text>\n            </div>\n          </div>\n        )}\n\n        {userInfo.lastLoginTeam && (\n          <div className={styles.infoItem}>\n            <div className={styles.iconWrapper}>\n              <TeamOutlined className={styles.icon} style={{ color: '#13c2c2' }} />\n            </div>\n            <div className={styles.infoContent}>\n              <Text type=\"secondary\" className={styles.label}>\n                登录团队\n              </Text>\n              <Text className={styles.value}>\n                {userInfo.lastLoginTeam}\n              </Text>\n            </div>\n          </div>\n        )}\n      </Space>\n    </div>\n  );\n\n  return (\n    <Popover\n      content={popoverContent}\n      title={\n        <div className={styles.popoverTitle}>\n          <Text strong>用户详细信息</Text>\n        </div>\n      }\n      trigger=\"hover\"\n      placement=\"bottomLeft\"\n      overlayClassName={styles.popoverOverlay}\n      overlayStyle={{\n        maxWidth: 320,\n      }}\n    >\n      <span className={styles.trigger}>\n        {children}\n      </span>\n    </Popover>\n  );\n};\n\nexport default UserInfoPopover;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCCkNb;;;2BAAA;;;;;;;0CAhNO;yCAQA;kDACiB;oFAEmB;yCACf;kGAEK;6FACL;2GACT;;;;;;;;;;YAEnB,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;YAElC;;;;;;;;;;;;;;CAcC,GACD,MAAM,eAAyB;oBA+EZ;;gBA9EjB;;GAEC,GACD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAA4B;oBAClE,MAAM;oBACN,UAAU;oBACV,OAAO;oBACP,WAAW;oBACX,cAAc;oBACd,eAAe;oBACf,eAAe;oBACf,WAAW;oBACX,QAAQ;gBACV;gBAEA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAC;gBACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;gBAElE,YAAY;gBACZ,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,IAAA,eAAQ,EAAC;gBAEjE,SAAS;gBACT,IAAA,gBAAS,EAAC;oBACR,MAAM,gBAAgB;wBACpB,IAAI;4BACF,MAAM,aAAa,MAAM,iBAAW,CAAC,oBAAoB;4BACzD,YAAY;4BACZ,iBAAiB;wBACnB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,eAAe;4BAC7B,iBAAiB;wBACnB,SAAU;4BACR,mBAAmB;wBACrB;oBACF;oBAEA;gBACF,GAAG,EAAE;gBAEL,qBACE,2BAAC,sBAAO;oBACN,OAAM;oBACN,qBACE,2BAAC,YAAM;wBACL,MAAK;wBACL,oBAAM,2BAAC,sBAAe;;;;;wBACtB,SAAS,IAAM,wBAAwB;;;;;;oBAG3C,OAAO;wBACL,cAAc;wBACd,cAAc;wBACd,WAAW;oBACb;;wBAEC,8BACC,2BAAC,WAAK;4BACJ,SAAQ;4BACR,aAAa;4BACb,MAAK;4BACL,QAAQ;4BACR,OAAO;gCACL,cAAc;gCACd,QAAQ;4BACV;;;;;iDAGF,2BAAC,UAAI;4BAAC,UAAU;sCAEd,cAAA,2BAAC;gCAAI,WAAW,sCAAM,CAAC,mBAAmB;;kDACxC,2BAAC;wCAAI,WAAW,sCAAM,CAAC,eAAe;;0DAEpC,2BAAC;gDAAI,WAAW,sCAAM,CAAC,eAAe;;kEACpC,2BAAC,YAAM;wDACL,MAAM;wDACN,WAAW,sCAAM,CAAC,MAAM;wDACxB,MAAM,CAAC,SAAS,IAAI,kBAAI,2BAAC,mBAAY;;;;;mEAEpC,iBAAA,SAAS,IAAI,cAAb,qCAAA,eAAe,MAAM,CAAC,GAAG,WAAW;;;;;;kEAGvC,2BAAC;wDAAI,WAAW,sCAAM,CAAC,eAAe;;;;;;;;;;;;0DAIxC,2BAAC;gDAAI,WAAW,sCAAM,CAAC,aAAa;;kEAClC,2BAAC,wBAAe;wDAAC,UAAU;kEACzB,cAAA,2BAAC,gBAAU,CAAC,KAAK;4DACf,OAAO;4DACP,WAAW,sCAAM,CAAC,QAAQ;sEAEzB,SAAS,IAAI,IAAI;;;;;;;;;;;oDAKrB,SAAS,QAAQ,kBAChB,2BAAC;wDACC,MAAK;wDACL,OAAO;4DACL,UAAU;4DACV,cAAc;4DACd,SAAS;wDACX;kEAEC,SAAS,QAAQ;;;;;;;;;;;;;;;;;;kDAO1B,2BAAC;wCAAI,WAAW,sCAAM,CAAC,gBAAgB;kDACrC,cAAA,2BAAC,WAAK;4CAAC,WAAU;4CAAW,MAAM;4CAAG,OAAO;gDAAE,OAAO;4CAAO;;gDACzD,SAAS,aAAa,kBACrB,2BAAC;oDAAI,WAAW,sCAAM,CAAC,aAAa;;sEAClC,2BAAC,0BAAmB;4DAClB,OAAO;gEACL,UAAU;gEACV,OAAO;4DACT;;;;;;sEAEF,2BAAC,gBAAU,CAAC,IAAI;4DACd,OAAO;gEACL,UAAU;gEACV,OAAO;gEACP,YAAY;4DACd;;gEACD;gEACO,SAAS,aAAa;;;;;;;;;;;;;gDAIjC,SAAS,aAAa,kBACrB,2BAAC;oDAAI,WAAW,sCAAM,CAAC,aAAa;;sEAClC,2BAAC,mBAAY;4DACX,OAAO;gEACL,UAAU;gEACV,OAAO;4DACT;;;;;;sEAEF,2BAAC,gBAAU,CAAC,IAAI;4DACd,OAAO;gEACL,UAAU;gEACV,OAAO;gEACP,YAAY;4DACd;;gEACD;gEACK,SAAS,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAW1C,2BAAC,6BAAoB;4BACnB,SAAS;4BACT,UAAU,IAAM,wBAAwB;4BACxC,UAAU;4BACV,WAAW;gCACT,cAAc;gCACd,QAAQ,GAAG,CAAC;4BACd;;;;;;;;;;;;YAIR;eA3KM;iBAAA;gBA6KN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCCnNf;;;2BAAA;;;;gBAAA,WAAe;gBAAC,WAAW,CAAC,gBAAgB,CAAC;gBAAC,gBAAgB,CAAC,qBAAqB,CAAC;gBAAC,eAAe,CAAC,oBAAoB,CAAC;gBAAC,QAAQ,CAAC,aAAa,CAAC;gBAAC,SAAS,CAAC,cAAc,CAAC;gBAAC,SAAS,CAAC,cAAc,CAAC;gBAAC,kBAAkB,CAAC,uBAAuB,CAAC;gBAAC,kBAAkB,CAAC,uBAAuB,CAAC;gBAAC,UAAU,CAAC,eAAe,CAAC;gBAAC,eAAe,CAAC,oBAAoB,CAAC;gBAAC,YAAY,CAAC,iBAAiB,CAAC;YAAA;;;;;;;wCC8JjY;;;2BAAA;;;;;;;0CA1JO;yCAMA;mFACW;8GAEC;;;;;;;;;YAEnB,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;YAO3B;;;;;;;;;;;;;;;;;CAiBC,GACD,MAAM,kBAAkD,CAAC,EACvD,QAAQ,EACR,QAAQ,EACT;gBACC,MAAM,+BACJ,2BAAC;oBAAI,WAAW,yCAAM,CAAC,cAAc;8BACnC,cAAA,2BAAC,WAAK;wBAAC,WAAU;wBAAW,MAAM;wBAAI,OAAO;4BAAE,OAAO;wBAAO;;4BAE1D,SAAS,KAAK,kBACb,2BAAC;gCAAI,WAAW,yCAAM,CAAC,QAAQ;;kDAC7B,2BAAC;wCAAI,WAAW,yCAAM,CAAC,WAAW;kDAChC,cAAA,2BAAC,mBAAY;4CAAC,WAAW,yCAAM,CAAC,IAAI;4CAAE,OAAO;gDAAE,OAAO;4CAAU;;;;;;;;;;;kDAElE,2BAAC;wCAAI,WAAW,yCAAM,CAAC,WAAW;;0DAChC,2BAAC;gDAAK,MAAK;gDAAY,WAAW,yCAAM,CAAC,KAAK;0DAAE;;;;;;0DAGhD,2BAAC;gDAAK,WAAW,yCAAM,CAAC,KAAK;gDAAE,QAAQ;0DACpC,SAAS,KAAK;;;;;;;;;;;;;;;;;;4BAMtB,SAAS,SAAS,kBACjB,2BAAC;gCAAI,WAAW,yCAAM,CAAC,QAAQ;;kDAC7B,2BAAC;wCAAI,WAAW,yCAAM,CAAC,WAAW;kDAChC,cAAA,2BAAC,oBAAa;4CAAC,WAAW,yCAAM,CAAC,IAAI;4CAAE,OAAO;gDAAE,OAAO;4CAAU;;;;;;;;;;;kDAEnE,2BAAC;wCAAI,WAAW,yCAAM,CAAC,WAAW;;0DAChC,2BAAC;gDAAK,MAAK;gDAAY,WAAW,yCAAM,CAAC,KAAK;0DAAE;;;;;;0DAGhD,2BAAC;gDAAK,WAAW,yCAAM,CAAC,KAAK;gDAAE,QAAQ;0DACpC,SAAS,SAAS;;;;;;;;;;;;;;;;;;4BAMzB,CAAA,SAAS,KAAK,IAAI,SAAS,SAAS,AAAD,KAAM,SAAS,YAAY,kBAC9D,2BAAC,aAAO;gCAAC,OAAO;oCAAE,QAAQ;gCAAQ;;;;;;4BAInC,SAAS,YAAY,kBACpB,2BAAC;gCAAI,WAAW,yCAAM,CAAC,QAAQ;;kDAC7B,2BAAC;wCAAI,WAAW,yCAAM,CAAC,WAAW;kDAChC,cAAA,2BAAC,uBAAgB;4CAAC,WAAW,yCAAM,CAAC,IAAI;4CAAE,OAAO;gDAAE,OAAO;4CAAU;;;;;;;;;;;kDAEtE,2BAAC;wCAAI,WAAW,yCAAM,CAAC,WAAW;;0DAChC,2BAAC;gDAAK,MAAK;gDAAY,WAAW,yCAAM,CAAC,KAAK;0DAAE;;;;;;0DAGhD,2BAAC;gDAAK,WAAW,yCAAM,CAAC,KAAK;0DAC1B,SAAS,YAAY;;;;;;;;;;;;;;;;;;4BAM7B,SAAS,aAAa,kBACrB,2BAAC;gCAAI,WAAW,yCAAM,CAAC,QAAQ;;kDAC7B,2BAAC;wCAAI,WAAW,yCAAM,CAAC,WAAW;kDAChC,cAAA,2BAAC,0BAAmB;4CAAC,WAAW,yCAAM,CAAC,IAAI;4CAAE,OAAO;gDAAE,OAAO;4CAAU;;;;;;;;;;;kDAEzE,2BAAC;wCAAI,WAAW,yCAAM,CAAC,WAAW;;0DAChC,2BAAC;gDAAK,MAAK;gDAAY,WAAW,yCAAM,CAAC,KAAK;0DAAE;;;;;;0DAGhD,2BAAC;gDAAK,WAAW,yCAAM,CAAC,KAAK;0DAC1B,SAAS,aAAa;;;;;;;;;;;;;;;;;;4BAM9B,SAAS,aAAa,kBACrB,2BAAC;gCAAI,WAAW,yCAAM,CAAC,QAAQ;;kDAC7B,2BAAC;wCAAI,WAAW,yCAAM,CAAC,WAAW;kDAChC,cAAA,2BAAC,mBAAY;4CAAC,WAAW,yCAAM,CAAC,IAAI;4CAAE,OAAO;gDAAE,OAAO;4CAAU;;;;;;;;;;;kDAElE,2BAAC;wCAAI,WAAW,yCAAM,CAAC,WAAW;;0DAChC,2BAAC;gDAAK,MAAK;gDAAY,WAAW,yCAAM,CAAC,KAAK;0DAAE;;;;;;0DAGhD,2BAAC;gDAAK,WAAW,yCAAM,CAAC,KAAK;0DAC1B,SAAS,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBASrC,qBACE,2BAAC,aAAO;oBACN,SAAS;oBACT,qBACE,2BAAC;wBAAI,WAAW,yCAAM,CAAC,YAAY;kCACjC,cAAA,2BAAC;4BAAK,MAAM;sCAAC;;;;;;;;;;;oBAGjB,SAAQ;oBACR,WAAU;oBACV,kBAAkB,yCAAM,CAAC,cAAc;oBACvC,cAAc;wBACZ,UAAU;oBACZ;8BAEA,cAAA,2BAAC;wBAAK,WAAW,yCAAM,CAAC,OAAO;kCAC5B;;;;;;;;;;;YAIT;iBApHM;gBAsHN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;IH7JD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,gCAA+B;YAAC;SAAqB;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AACrjB"}