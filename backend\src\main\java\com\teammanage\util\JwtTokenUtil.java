package com.teammanage.util;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import javax.crypto.SecretKey;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.teammanage.entity.Account;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;

/**
 * JWT Token工具类
 * 实现用户身份验证令牌管理
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class JwtTokenUtil {

    private static final Logger log = LoggerFactory.getLogger(JwtTokenUtil.class);

    @Value("${jwt.secret}")
    private String secret;

    @Value("${jwt.token-expiration:604800}") // 默认7天
    private Long tokenExpiration;

    private SecretKey getSigningKey() {
        return Keys.hmacShaKeyFor(secret.getBytes());
    }

    /**
     * 生成用户Token
     *
     * @param account 用户账号信息
     * @return JWT Token
     */
    public String generateToken(Account account) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", account.getId());
        claims.put("email", account.getEmail());
        claims.put("name", account.getName());
        claims.put("jti", UUID.randomUUID().toString());

        return Jwts.builder()
                .claims(claims)
                .subject(account.getEmail())
                .issuedAt(new Date())
                .expiration(new Date(System.currentTimeMillis() + tokenExpiration * 1000))
                .signWith(getSigningKey())
                .compact();
    }



    /**
     * 验证Token
     * 
     * @param token JWT Token
     * @return 是否有效
     */
    public boolean validateToken(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            return !isTokenExpired(claims);
        } catch (JwtException | IllegalArgumentException e) {
            log.warn("Token验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 从Token中获取Claims
     * 
     * @param token JWT Token
     * @return Claims
     */
    public Claims getClaimsFromToken(String token) {
        return Jwts.parser()
                .verifyWith(getSigningKey())
                .build()
                .parseSignedClaims(token)
                .getPayload();
    }

    /**
     * 从Token中获取用户ID
     * 
     * @param token JWT Token
     * @return 用户ID
     */
    public Long getUserIdFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.get("userId", Long.class);
    }

    /**
     * 从Token中获取邮箱
     * 
     * @param token JWT Token
     * @return 邮箱
     */
    public String getEmailFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.getSubject();
    }



    /**
     * 生成包含团队信息的Token
     *
     * @param account 用户账号信息
     * @param teamId 团队ID
     * @param isCreator 是否为团队创建者
     * @return JWT Token
     */
    public String generateTokenWithTeam(Account account, Long teamId, Boolean isCreator) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", account.getId());
        claims.put("email", account.getEmail());
        claims.put("name", account.getName());
        claims.put("teamId", teamId);
        claims.put("isCreator", isCreator);
        claims.put("jti", UUID.randomUUID().toString());

        return Jwts.builder()
                .claims(claims)
                .subject(account.getEmail())
                .issuedAt(new Date())
                .expiration(new Date(System.currentTimeMillis() + tokenExpiration * 1000))
                .signWith(getSigningKey())
                .compact();
    }

    /**
     * 从Token中获取团队ID
     *
     * @param token JWT Token
     * @return 团队ID，如果Token中没有团队信息则返回null
     */
    public Long getTeamIdFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.get("teamId", Long.class);
    }

    /**
     * 从Token中获取是否为创建者
     *
     * @param token JWT Token
     * @return 是否为创建者，如果Token中没有团队信息则返回null
     */
    public Boolean getIsCreatorFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.get("isCreator", Boolean.class);
    }

    /**
     * 从Token中获取JTI
     *
     * @param token JWT Token
     * @return JTI
     */
    public String getJtiFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.get("jti", String.class);
    }



    /**
     * 检查Token是否过期
     * 
     * @param claims Claims
     * @return 是否过期
     */
    private boolean isTokenExpired(Claims claims) {
        Date expiration = claims.getExpiration();
        return expiration.before(new Date());
    }

    /**
     * 获取Token过期时间
     * 
     * @param token JWT Token
     * @return 过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.getExpiration();
    }

    /**
     * 检查Token是否即将过期（剩余时间少于10分钟）
     *
     * @param token JWT Token
     * @return 是否即将过期
     */
    public boolean isTokenExpiringSoon(String token) {
        try {
            Date expiration = getExpirationDateFromToken(token);
            long timeUntilExpiration = expiration.getTime() - System.currentTimeMillis();
            return timeUntilExpiration < 10 * 60 * 1000; // 10分钟
        } catch (Exception e) {
            return true;
        }
    }





}
