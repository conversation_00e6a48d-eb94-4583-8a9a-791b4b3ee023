package com.teammanage.util;

import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.regex.Pattern;
import java.util.List;
import java.util.ArrayList;

/**
 * 输入数据清理和验证工具
 * 
 * 提供SQL注入防护、XSS攻击防护、特殊字符过滤等功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class InputSanitizer {

    // SQL注入关键词模式
    private static final Pattern SQL_INJECTION_PATTERN = Pattern.compile(
        "(?i).*(union|select|insert|update|delete|drop|create|alter|exec|execute|script|javascript|vbscript|onload|onerror|alert|confirm|prompt|eval|expression|iframe|object|embed|form|input|meta|link|style|base|applet|body|html|head|title).*"
    );

    // XSS攻击模式
    private static final Pattern XSS_PATTERN = Pattern.compile(
        "(?i).*(<script|</script|<iframe|</iframe|<object|</object|<embed|</embed|<form|</form|javascript:|vbscript:|onload=|onerror=|onclick=|onmouseover=|onfocus=|onblur=|onchange=|onsubmit=|alert\\(|confirm\\(|prompt\\(|eval\\(|expression\\().*"
    );

    // 特殊字符模式（允许的字符：字母、数字、中文、常用标点）
    private static final Pattern SPECIAL_CHARS_PATTERN = Pattern.compile(
        "[^\\u4e00-\\u9fa5a-zA-Z0-9\\s\\-_@\\.\\,\\!\\?\\(\\)\\[\\]\\{\\}\\:;\"'\\+\\=\\*\\&\\%\\$\\#]"
    );

    // 邮箱格式验证
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    );

    // 团队名称格式验证（允许中文、英文、数字、空格、连字符、下划线）
    private static final Pattern TEAM_NAME_PATTERN = Pattern.compile(
        "^[\\u4e00-\\u9fa5a-zA-Z0-9\\s\\-_]+$"
    );

    /**
     * 清理和验证字符串输入
     * 
     * @param input 输入字符串
     * @param fieldName 字段名称（用于错误消息）
     * @return 清理后的字符串
     * @throws IllegalArgumentException 如果输入包含危险内容
     */
    public String sanitizeString(String input, String fieldName) {
        if (!StringUtils.hasText(input)) {
            return input;
        }

        // 去除首尾空格
        String cleaned = input.trim();

        // 检查SQL注入
        if (SQL_INJECTION_PATTERN.matcher(cleaned).matches()) {
            throw new IllegalArgumentException(fieldName + "包含不安全的内容");
        }

        // 检查XSS攻击
        if (XSS_PATTERN.matcher(cleaned).matches()) {
            throw new IllegalArgumentException(fieldName + "包含不安全的脚本内容");
        }

        return cleaned;
    }

    /**
     * 验证团队名称
     * 
     * @param teamName 团队名称
     * @return 清理后的团队名称
     * @throws IllegalArgumentException 如果团队名称格式不正确
     */
    public String sanitizeTeamName(String teamName) {
        String cleaned = sanitizeString(teamName, "团队名称");
        
        if (!StringUtils.hasText(cleaned)) {
            throw new IllegalArgumentException("团队名称不能为空");
        }

        if (cleaned.length() > 100) {
            throw new IllegalArgumentException("团队名称长度不能超过100字符");
        }

        if (!TEAM_NAME_PATTERN.matcher(cleaned).matches()) {
            throw new IllegalArgumentException("团队名称只能包含中文、英文、数字、空格、连字符和下划线");
        }

        return cleaned;
    }

    /**
     * 验证团队描述
     * 
     * @param description 团队描述
     * @return 清理后的团队描述
     * @throws IllegalArgumentException 如果描述格式不正确
     */
    public String sanitizeTeamDescription(String description) {
        if (!StringUtils.hasText(description)) {
            return description;
        }

        String cleaned = sanitizeString(description, "团队描述");

        if (cleaned.length() > 500) {
            throw new IllegalArgumentException("团队描述长度不能超过500字符");
        }

        // 移除特殊字符
        cleaned = SPECIAL_CHARS_PATTERN.matcher(cleaned).replaceAll("");

        return cleaned;
    }

    /**
     * 验证邮箱列表
     * 
     * @param emails 邮箱列表
     * @return 清理后的邮箱列表
     * @throws IllegalArgumentException 如果邮箱格式不正确
     */
    public List<String> sanitizeEmails(List<String> emails) {
        if (emails == null || emails.isEmpty()) {
            throw new IllegalArgumentException("邮箱列表不能为空");
        }

        if (emails.size() > 10) {
            throw new IllegalArgumentException("一次最多邀请10个成员");
        }

        List<String> cleanedEmails = new ArrayList<>();
        for (String email : emails) {
            String cleanedEmail = sanitizeEmail(email);
            if (!cleanedEmails.contains(cleanedEmail)) {
                cleanedEmails.add(cleanedEmail);
            }
        }

        if (cleanedEmails.size() != emails.size()) {
            throw new IllegalArgumentException("邮箱列表中存在重复邮箱");
        }

        return cleanedEmails;
    }

    /**
     * 验证单个邮箱
     * 
     * @param email 邮箱地址
     * @return 清理后的邮箱地址
     * @throws IllegalArgumentException 如果邮箱格式不正确
     */
    public String sanitizeEmail(String email) {
        if (!StringUtils.hasText(email)) {
            throw new IllegalArgumentException("邮箱地址不能为空");
        }

        String cleaned = email.trim().toLowerCase();

        // 基本安全检查
        cleaned = sanitizeString(cleaned, "邮箱地址");

        if (!EMAIL_PATTERN.matcher(cleaned).matches()) {
            throw new IllegalArgumentException("邮箱格式不正确: " + email);
        }

        if (cleaned.length() > 254) {
            throw new IllegalArgumentException("邮箱地址长度不能超过254字符");
        }

        return cleaned;
    }

    /**
     * 验证邀请消息
     * 
     * @param message 邀请消息
     * @return 清理后的邀请消息
     * @throws IllegalArgumentException 如果消息格式不正确
     */
    public String sanitizeInvitationMessage(String message) {
        if (!StringUtils.hasText(message)) {
            return message;
        }

        String cleaned = sanitizeString(message, "邀请消息");

        if (cleaned.length() > 500) {
            throw new IllegalArgumentException("邀请消息长度不能超过500字符");
        }

        // 移除特殊字符
        cleaned = SPECIAL_CHARS_PATTERN.matcher(cleaned).replaceAll("");

        return cleaned;
    }

    /**
     * 检查字符串是否包含SQL注入风险
     * 
     * @param input 输入字符串
     * @return 是否包含SQL注入风险
     */
    public boolean containsSqlInjection(String input) {
        if (!StringUtils.hasText(input)) {
            return false;
        }
        return SQL_INJECTION_PATTERN.matcher(input).matches();
    }

    /**
     * 检查字符串是否包含XSS攻击风险
     * 
     * @param input 输入字符串
     * @return 是否包含XSS攻击风险
     */
    public boolean containsXss(String input) {
        if (!StringUtils.hasText(input)) {
            return false;
        }
        return XSS_PATTERN.matcher(input).matches();
    }
}
