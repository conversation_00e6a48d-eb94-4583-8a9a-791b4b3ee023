-------------------------------------------------------------------------------
Test set: com.teammanage.service.TeamInvitationServiceTest
-------------------------------------------------------------------------------
Tests run: 2, Failures: 0, Errors: 1, Skipped: 0, Time elapsed: 2.920 s <<< FAILURE! -- in com.teammanage.service.TeamInvitationServiceTest
com.teammanage.service.TeamInvitationServiceTest.testInvitationTokenGeneration -- Time elapsed: 0.017 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "com.teammanage.mapper.TeamInvitationMapper.selectById(java.io.Serializable)" because "this.teamInvitationMapper" is null
	at com.teammanage.service.TeamInvitationService.generateInvitationToken(TeamInvitationService.java:477)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:565)
	at com.teammanage.service.TeamInvitationServiceTest.testInvitationTokenGeneration(TeamInvitationServiceTest.java:33)
	at java.base/java.lang.reflect.Method.invoke(Method.java:565)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)

