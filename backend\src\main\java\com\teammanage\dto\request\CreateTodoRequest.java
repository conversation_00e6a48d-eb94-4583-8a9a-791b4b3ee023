package com.teammanage.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

/**
 * 创建TODO请求
 */
public class CreateTodoRequest {

    /**
     * 任务标题
     */
    @NotBlank(message = "任务标题不能为空")
    private String title;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 优先级：1-低，2-中，3-高
     */
    @NotNull(message = "优先级不能为空")
    @Min(value = 1, message = "优先级必须在1-3之间")
    @Max(value = 3, message = "优先级必须在1-3之间")
    private Integer priority;

    // Getter and Setter methods
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public Integer getPriority() { return priority; }
    public void setPriority(Integer priority) { this.priority = priority; }
}
