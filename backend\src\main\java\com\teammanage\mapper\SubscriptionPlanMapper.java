package com.teammanage.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teammanage.entity.SubscriptionPlan;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 订阅套餐Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SubscriptionPlanMapper extends BaseMapper<SubscriptionPlan> {

    /**
     * 查询所有启用的套餐
     * 
     * @return 套餐列表
     */
    @Select("SELECT * FROM subscription_plan WHERE is_active = 1 ORDER BY price ASC")
    List<SubscriptionPlan> findAllActive();

}
