/**
 * 请求工具类
 *
 */

import { extend } from 'umi-request';
import { history } from '@umijs/max';
import type { ApiResponse } from '@/types/api';

// 全局消息服务
let messageApi: any = null;

// 设置消息API实例（由App组件提供）
export const setMessageApi = (api: any) => {
  messageApi = api;
};

// 获取消息API实例
export const getMessageApi = () => {
  if (!messageApi) {
    // 如果没有设置messageApi，使用静态方法作为fallback
    console.warn('Message API not initialized, using console fallback');
    return {
      error: (content: string) => console.error('Message:', content),
      success: (content: string) => console.log('Message:', content),
      warning: (content: string) => console.warn('Message:', content),
      info: (content: string) => console.info('Message:', content),
    };
  }
  return messageApi;
};

// 创建请求实例
const request = extend({
  prefix: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * Token 管理器（单令牌系统）
 *
 * 这是前端身份验证系统的Token存储管理核心类，负责JWT Token的本地存储和管理。
 *
 * 功能特性：
 * - 统一管理用户认证Token的存储和获取
 * - 使用localStorage进行持久化存储，支持页面刷新后保持登录状态
 * - 支持Token的设置、获取、清除和检查操作
 * - 线程安全的静态方法设计，支持全局调用
 *
 * 设计理念：
 * - 采用单令牌系统：一个Token同时用于用户认证和团队访问
 * - 简化认证流程：避免多Token管理的复杂性
 * - 透明存储：上层业务逻辑无需关心存储细节
 * - 安全清理：提供完整的Token清除机制
 *
 * Token生命周期：
 * 1. 登录时：通过setToken()存储新Token
 * 2. 使用时：通过getToken()获取Token用于API请求
 * 3. 验证时：通过hasToken()检查Token是否存在
 * 4. 更新时：通过setToken()替换旧Token（如刷新、团队切换）
 * 5. 清除时：通过clearToken()删除Token（如登出、过期）
 *
 * 存储策略：
 * - 使用localStorage而非sessionStorage，支持多标签页共享
 * - 存储键名固定为'auth_token'，避免冲突
 * - 不进行额外加密，依赖HTTPS传输安全
 * - 支持浏览器的自动清理机制
 *
 * 安全考虑：
 * - Token存储在localStorage中，需要防范XSS攻击
 * - 不在控制台或日志中输出Token内容
 * - 提供完整的清除机制，确保敏感信息不残留
 * - 配合后端的Token过期和黑名单机制
 */
class TokenManager {
  /** Token在localStorage中的存储键名 */
  private static readonly TOKEN_KEY = 'auth_token';

  /**
   * 获取当前存储的Token
   *
   * 从localStorage中读取当前用户的JWT Token。
   * 这个方法不验证Token的有效性，只是简单的存储读取。
   *
   * @returns string | null 当前Token字符串，如果不存在则返回null
   *
   * @example
   * ```typescript
   * const token = TokenManager.getToken();
   * if (token) {
   *   console.log('用户已登录');
   *   // 使用Token进行API请求
   * } else {
   *   console.log('用户未登录');
   *   // 跳转到登录页面
   * }
   * ```
   */
  static getToken(): string | null {
    try {
      return localStorage.getItem(TokenManager.TOKEN_KEY);
    } catch (error) {
      // localStorage访问失败（如隐私模式），返回null
      console.warn('无法访问localStorage:', error);
      return null;
    }
  }

  /**
   * 设置Token到本地存储
   *
   * 将新的JWT Token保存到localStorage中。
   * 如果已存在Token，会被新Token覆盖。
   *
   * @param token JWT Token字符串
   *
   * @example
   * ```typescript
   * // 登录成功后保存Token
   * const loginResponse = await AuthService.login(credentials);
   * TokenManager.setToken(loginResponse.token);
   *
   * // 团队切换后更新Token
   * const teamResponse = await AuthService.selectTeam(teamId);
   * TokenManager.setToken(teamResponse.token);
   * ```
   */
  static setToken(token: string): void {
    try {
      localStorage.setItem(TokenManager.TOKEN_KEY, token);
    } catch (error) {
      // localStorage写入失败（如存储空间不足），记录错误
      console.error('无法保存Token到localStorage:', error);
      throw new Error('Token保存失败，请检查浏览器存储设置');
    }
  }

  /**
   * 清除本地存储的Token
   *
   * 从localStorage中删除Token，通常在用户登出或Token过期时调用。
   * 这个操作是幂等的，多次调用不会产生副作用。
   *
   * @example
   * ```typescript
   * // 用户主动登出
   * await AuthService.logout();
   * TokenManager.clearToken();
   *
   * // Token过期时自动清除
   * if (tokenExpired) {
   *   TokenManager.clearToken();
   *   history.push('/user/login');
   * }
   * ```
   */
  static clearToken(): void {
    try {
      localStorage.removeItem(TokenManager.TOKEN_KEY);
    } catch (error) {
      // localStorage访问失败，记录警告但不抛出异常
      console.warn('无法清除localStorage中的Token:', error);
    }
  }

  /**
   * 检查是否存在有效的Token
   *
   * 检查localStorage中是否存储了Token。
   * 注意：这只检查Token是否存在，不验证Token的有效性或过期状态。
   *
   * @returns boolean 如果存在Token返回true，否则返回false
   *
   * @example
   * ```typescript
   * // 路由守卫中检查登录状态
   * if (TokenManager.hasToken()) {
   *   // 有Token，但还需要验证有效性
   *   const isValid = await AuthService.validateToken();
   *   if (!isValid) {
   *     TokenManager.clearToken();
   *     return false;
   *   }
   *   return true;
   * } else {
   *   // 没有Token，直接返回未登录
   *   return false;
   * }
   * ```
   */
  static hasToken(): boolean {
    const token = TokenManager.getToken();
    return !!token && token.trim().length > 0;
  }
}

/**
 * 请求拦截器
 *
 * 这是HTTP请求的预处理拦截器，负责在每个API请求发送前自动注入认证信息。
 *
 * 主要功能：
 * 1. 自动Token注入：从本地存储获取Token并添加到请求头
 * 2. 认证头格式化：使用标准的Bearer Token格式
 * 3. 公开接口支持：对于没有Token的请求（如登录、注册）正常放行
 * 4. 头部合并：保持原有请求头的同时添加认证信息
 *
 * 工作流程：
 * 1. 从TokenManager获取当前存储的Token
 * 2. 如果Token存在，添加Authorization头部
 * 3. 使用Bearer Token标准格式：'Bearer <token>'
 * 4. 合并原有请求头，避免覆盖其他重要头部
 * 5. 返回修改后的请求配置
 *
 * 支持的场景：
 * - 已登录用户的API请求：自动携带Token
 * - 公开接口请求：如登录、注册、验证码发送等
 * - 团队切换后的请求：自动使用新的团队Token
 * - Token刷新后的请求：自动使用刷新后的Token
 *
 * 注意事项：
 * - 不验证Token的有效性，只负责注入
 * - Token验证由后端和响应拦截器处理
 * - 支持Token为空的情况，不会阻止请求
 */
request.interceptors.request.use((url, options) => {
  // 从本地存储获取当前Token
  const token = TokenManager.getToken();

  if (token) {
    // Token存在时，添加Authorization头部
    // 使用标准的Bearer Token格式
    const headers = {
      ...options.headers, // 保持原有头部信息
      Authorization: `Bearer ${token}`, // 添加认证头部
    };

    // 返回包含认证信息的请求配置
    return {
      url,
      options: { ...options, headers },
    };
  }

  // Token不存在时，直接返回原始请求配置
  // 这支持公开接口（如登录、注册）的正常访问
  return { url, options };
});

/**
 * 响应拦截器
 *
 * 这是HTTP响应的后处理拦截器，负责统一处理API响应和错误情况。
 *
 * 主要功能：
 * 1. 响应格式统一：处理后端返回的标准响应格式
 * 2. 认证失效处理：自动检测401错误并处理登录过期
 * 3. 错误消息提示：统一显示用户友好的错误信息
 * 4. 自动登出：Token过期时自动清除本地状态并跳转
 * 5. 特殊场景处理：针对不同页面的差异化错误处理
 *
 * 响应处理流程：
 * 1. 解析响应JSON数据
 * 2. 检查业务状态码（code字段）
 * 3. 根据状态码执行相应的处理逻辑
 * 4. 对于成功响应，直接返回数据
 * 5. 对于错误响应，执行错误处理流程
 *
 * 认证失效处理：
 * - 检测401状态码（未认证）
 * - 区分不同页面的处理策略
 * - Dashboard页面：延迟处理，避免团队切换时的时序问题
 * - 其他页面：立即清除Token并跳转到登录页
 * - 显示用户友好的错误提示
 *
 * 错误分类处理：
 * - 401 未认证：Token过期或无效，自动登出
 * - 403 权限不足：显示权限错误信息
 * - 404 资源不存在：显示资源不存在信息
 * - 500 服务器错误：显示服务器异常信息
 * - 网络错误：显示网络连接异常信息
 *
 * 特殊场景考虑：
 * - 团队切换时的Token更新时序问题
 * - 多标签页的Token同步问题
 * - 网络异常时的用户体验
 * - 重复跳转的防护机制
 */
request.interceptors.response.use(
  async (response) => {
    const data = await response.clone().json();

    // 检查业务状态码
    if (data.code !== 200) {
      // 认证失败的处理
      if (data.code === 401) {
        // 检查当前路径，如果是Dashboard相关页面且刚刚进行了团队切换，
        // 可能是Token更新的时序问题，不立即跳转
        const currentPath = window.location.pathname;
        const isDashboardRelated =
          currentPath.startsWith('/dashboard') ||
          currentPath.startsWith('/team');

        // 如果是Dashboard相关页面，延迟处理认证错误
        if (isDashboardRelated) {
          console.warn(
            'Dashboard页面认证失败，可能是Token更新时序问题:',
            data.message,
          );
          return Promise.reject(new Error(data.message));
        }

        // 其他页面立即处理认证错误
        TokenManager.clearToken();
        getMessageApi().error('登录已过期，请重新登录');
        // 跳转到登录页，避免重复跳转
        if (window.location.pathname !== '/user/login') {
          history.push('/user/login');
        }
        return Promise.reject(new Error(data.message));
      }

      // 处理权限错误
      if (data.code === 403) {
        // 显示后端返回的具体错误消息
        getMessageApi().error(data.message || '没有权限访问该资源');
        return Promise.reject(new Error(data.message));
      }

      // 其他业务错误，显示错误消息
      getMessageApi().error(data.message || '请求失败');
      return Promise.reject(new Error(data.message));
    }

    return response;
  },
  (error: any) => {
    // 网络错误或其他错误
    if (error.response) {
      const { status } = error.response;
      if (status === 401) {
        // 检查当前路径，如果是Dashboard相关页面，可能是Token更新的时序问题
        const currentPath = window.location.pathname;
        const isDashboardRelated =
          currentPath.startsWith('/dashboard') ||
          currentPath.startsWith('/team');

        if (isDashboardRelated) {
          console.warn('Dashboard页面认证失败，可能是Token更新时序问题');
          // 不立即清除Token和跳转，让页面自己处理
          return Promise.reject(error);
        }

        // 其他页面立即处理认证错误
        TokenManager.clearToken();
        getMessageApi().error('登录已过期，请重新登录');
        if (window.location.pathname !== '/user/login') {
          history.push('/user/login');
        }
      } else if (status === 403) {
        // 检查是否是团队访问被拒绝的特殊错误
        const errorMessage = error.response?.data?.message;
        if (errorMessage?.includes('停用') || errorMessage?.includes('禁用') || errorMessage?.includes('不是该团队的成员')) {
          // 团队访问相关的错误，使用后端返回的具体消息
          getMessageApi().error(errorMessage);
        } else {
          // 其他权限错误
          getMessageApi().error('没有权限访问该资源');
        }
      } else if (status === 404) {
        getMessageApi().error('请求的资源不存在');
      } else if (status >= 500) {
        getMessageApi().error('服务器错误，请稍后重试');
      } else {
        getMessageApi().error(`请求失败: ${status}`);
      }
    } else if (error.request) {
      // 网络连接错误
      getMessageApi().error('网络错误，请检查网络连接');
    } else {
      // 其他错误
      getMessageApi().error('请求失败，请重试');
    }

    return Promise.reject(error);
  },
);

// 封装常用的请求方法
export const apiRequest = {
  get: <T = any>(url: string, params?: any): Promise<ApiResponse<T>> => {
    return request.get(url, { params });
  },

  post: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> => {
    return request.post(url, { data });
  },

  put: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> => {
    return request.put(url, { data });
  },

  delete: <T = any>(url: string, params?: any): Promise<ApiResponse<T>> => {
    return request.delete(url, { params });
  },
};

// 导出 Token 管理器
export { TokenManager };

// 导出默认请求实例
export default request;
