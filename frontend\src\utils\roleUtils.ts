import { TeamRole, TeamMemberResponse, TeamDetailResponse, TeamInfo } from '@/types/api';

/**
 * 角色工具函数
 * 
 * 提供统一的角色判断和处理逻辑，确保向后兼容性
 */

/**
 * 判断用户是否为团队创建者
 * 优先使用 role 字段，如果没有则回退到 isCreator 字段
 * 
 * @param user 用户对象（可以是 TeamMemberResponse、TeamDetailResponse 或 TeamInfo）
 * @returns 是否为团队创建者
 */
export function isTeamCreator(user: { role?: TeamRole; isCreator: boolean }): boolean {
  if (user.role !== undefined) {
    return user.role === TeamRole.TEAM_CREATOR;
  }
  return user.isCreator;
}

/**
 * 判断用户是否为团队成员
 * 优先使用 role 字段，如果没有则回退到 isCreator 字段
 * 
 * @param user 用户对象
 * @returns 是否为团队成员
 */
export function isTeamMember(user: { role?: TeamRole; isCreator: boolean }): boolean {
  if (user.role !== undefined) {
    return user.role === TeamRole.TEAM_MEMBER;
  }
  return !user.isCreator;
}

/**
 * 获取用户角色的显示名称
 * 
 * @param user 用户对象
 * @returns 角色显示名称
 */
export function getRoleDisplayName(user: { role?: TeamRole; isCreator: boolean }): string {
  if (isTeamCreator(user)) {
    return '团队创建者';
  }
  return '团队成员';
}

/**
 * 获取用户角色的标签颜色
 * 
 * @param user 用户对象
 * @returns 标签颜色
 */
export function getRoleTagColor(user: { role?: TeamRole; isCreator: boolean }): string {
  return isTeamCreator(user) ? 'gold' : 'blue';
}

/**
 * 获取用户的实际角色枚举值
 * 优先使用 role 字段，如果没有则根据 isCreator 推断
 * 
 * @param user 用户对象
 * @returns 角色枚举值
 */
export function getUserRole(user: { role?: TeamRole; isCreator: boolean }): TeamRole {
  if (user.role !== undefined) {
    return user.role;
  }
  return user.isCreator ? TeamRole.TEAM_CREATOR : TeamRole.TEAM_MEMBER;
}

/**
 * 检查用户是否有管理团队的权限
 * 
 * @param user 用户对象
 * @returns 是否有管理权限
 */
export function canManageTeam(user: { role?: TeamRole; isCreator: boolean }): boolean {
  return isTeamCreator(user);
}

/**
 * 检查用户是否有管理成员的权限
 * 
 * @param user 用户对象
 * @returns 是否有管理成员权限
 */
export function canManageMembers(user: { role?: TeamRole; isCreator: boolean }): boolean {
  return isTeamCreator(user);
}

/**
 * 检查用户是否可以访问团队数据
 * 
 * @param user 用户对象
 * @returns 是否可以访问数据
 */
export function canAccessData(user: { role?: TeamRole; isCreator: boolean }): boolean {
  return true; // 所有团队成员都可以访问数据
}

/**
 * 角色权限级别映射
 */
const ROLE_LEVELS = {
  [TeamRole.TEAM_CREATOR]: 100,
  [TeamRole.TEAM_MEMBER]: 10,
};

/**
 * 检查用户是否有足够的权限级别
 * 
 * @param user 用户对象
 * @param requiredRole 所需的最低角色
 * @returns 是否有足够权限
 */
export function hasPermissionLevel(
  user: { role?: TeamRole; isCreator: boolean },
  requiredRole: TeamRole
): boolean {
  const userRole = getUserRole(user);
  return ROLE_LEVELS[userRole] >= ROLE_LEVELS[requiredRole];
}

/**
 * 批量过滤团队创建者
 * 
 * @param users 用户列表
 * @returns 团队创建者列表
 */
export function filterTeamCreators<T extends { role?: TeamRole; isCreator: boolean }>(users: T[]): T[] {
  return users.filter(isTeamCreator);
}

/**
 * 批量过滤团队成员
 * 
 * @param users 用户列表
 * @returns 团队成员列表
 */
export function filterTeamMembers<T extends { role?: TeamRole; isCreator: boolean }>(users: T[]): T[] {
  return users.filter(isTeamMember);
}

/**
 * 统计各角色数量
 * 
 * @param users 用户列表
 * @returns 角色统计对象
 */
export function countRoles<T extends { role?: TeamRole; isCreator: boolean }>(users: T[]): {
  creators: number;
  members: number;
  total: number;
} {
  const creators = filterTeamCreators(users).length;
  const members = filterTeamMembers(users).length;
  
  return {
    creators,
    members,
    total: users.length,
  };
}
