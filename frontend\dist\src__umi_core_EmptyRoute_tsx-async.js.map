{"version": 3, "sources": ["src/.umi/core/EmptyRoute.tsx"], "sourcesContent": ["// @ts-nocheck\n// This file is generated by Um<PERSON> automatically\n// DO NOT CHANGE IT MANUALLY!\nimport React from 'react';\nimport { Outlet, useOutletContext } from 'umi';\nexport default function EmptyRoute() {\n  const context = useOutletContext();\n  return <Outlet context={context} />;\n}\n"], "names": [], "mappings": ";;;AAAA,cAAc;AACd,8CAA8C;AAC9C,6BAA6B;;;;;4BAG7B;;;eAAwB;;;;;;;uEAFN;4BACuB;;;;;;;;;;AAC1B,SAAS;;IACtB,MAAM,UAAU,IAAA,qBAAgB;IAChC,qBAAO,2BAAC,WAAM;QAAC,SAAS;;;;;;AAC1B;GAHwB;;QACN,qBAAgB;;;KADV"}